using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for inventory monitoring services
    /// </summary>
    public interface IInventoryMonitoringService
    {
        /// <summary>
        /// Get items with low stock
        /// </summary>
        /// <param name="threshold">Low stock threshold</param>
        /// <returns>List of low stock items</returns>
        Task<List<LowStockItem>> GetLowStockItemsAsync(int? threshold = null);

        /// <summary>
        /// Get items that are out of stock
        /// </summary>
        /// <returns>List of out of stock items</returns>
        Task<List<Item>> GetOutOfStockItemsAsync();

        /// <summary>
        /// Get items that are overstocked
        /// </summary>
        /// <param name="threshold">Overstock threshold</param>
        /// <returns>List of overstocked items</returns>
        Task<List<Item>> GetOverstockedItemsAsync(int? threshold = null);

        /// <summary>
        /// Get items approaching expiration
        /// </summary>
        /// <param name="daysAhead">Days ahead to check</param>
        /// <returns>List of items approaching expiration</returns>
        Task<List<ExpiringItem>> GetItemsApproachingExpirationAsync(int daysAhead = 30);

        /// <summary>
        /// Get expired items
        /// </summary>
        /// <returns>List of expired items</returns>
        Task<List<ExpiringItem>> GetExpiredItemsAsync();

        /// <summary>
        /// Monitor inventory levels and trigger alerts
        /// </summary>
        /// <returns>Monitoring result</returns>
        Task<InventoryMonitoringResult> MonitorInventoryLevelsAsync();

        /// <summary>
        /// Get inventory alerts
        /// </summary>
        /// <param name="alertType">Type of alert</param>
        /// <param name="isActive">Filter by active status</param>
        /// <returns>List of inventory alerts</returns>
        Task<List<InventoryAlert>> GetInventoryAlertsAsync(InventoryAlertType? alertType = null, bool? isActive = null);

        /// <summary>
        /// Create inventory alert
        /// </summary>
        /// <param name="alert">Alert to create</param>
        /// <returns>Created alert</returns>
        Task<InventoryAlert> CreateInventoryAlertAsync(InventoryAlert alert);

        /// <summary>
        /// Update inventory alert
        /// </summary>
        /// <param name="alert">Alert to update</param>
        /// <returns>Updated alert</returns>
        Task<InventoryAlert> UpdateInventoryAlertAsync(InventoryAlert alert);

        /// <summary>
        /// Dismiss inventory alert
        /// </summary>
        /// <param name="alertId">Alert ID</param>
        /// <param name="dismissedBy">User who dismissed the alert</param>
        /// <returns>True if dismissed successfully</returns>
        Task<bool> DismissInventoryAlertAsync(int alertId, int dismissedBy);

        /// <summary>
        /// Get inventory movement trends
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Inventory movement trends</returns>
        Task<InventoryMovementTrends> GetInventoryMovementTrendsAsync(int itemId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get inventory turnover rate
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="periodDays">Period in days</param>
        /// <returns>Turnover rate</returns>
        Task<decimal> GetInventoryTurnoverRateAsync(int itemId, int periodDays = 365);

        /// <summary>
        /// Get reorder suggestions
        /// </summary>
        /// <returns>List of reorder suggestions</returns>
        Task<List<ReorderSuggestion>> GetReorderSuggestionsAsync();

        /// <summary>
        /// Set reorder point for an item
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="reorderPoint">Reorder point</param>
        /// <param name="reorderQuantity">Reorder quantity</param>
        /// <returns>True if set successfully</returns>
        Task<bool> SetReorderPointAsync(int itemId, int reorderPoint, int reorderQuantity);

        /// <summary>
        /// Get inventory health score
        /// </summary>
        /// <returns>Inventory health score</returns>
        Task<InventoryHealthScore> GetInventoryHealthScoreAsync();

        /// <summary>
        /// Event fired when low stock is detected
        /// </summary>
        event EventHandler<LowStockDetectedEventArgs> LowStockDetected;

        /// <summary>
        /// Event fired when item is out of stock
        /// </summary>
        event EventHandler<OutOfStockEventArgs> OutOfStockDetected;

        /// <summary>
        /// Event fired when item is approaching expiration
        /// </summary>
        event EventHandler<ExpirationWarningEventArgs> ExpirationWarning;

        /// <summary>
        /// Event fired when inventory alert is created
        /// </summary>
        event EventHandler<InventoryAlertEventArgs> InventoryAlertCreated;
    }

    // Note: LowStockItem class is defined in Models/Reports/InventoryReport.cs

    /// <summary>
    /// Expiring item model
    /// </summary>
    public class ExpiringItem
    {
        public int ItemId { get; set; }
        public string ItemCode { get; set; }
        public string Name { get; set; }
        public DateTime ExpirationDate { get; set; }
        public int DaysUntilExpiration { get; set; }
        public int Quantity { get; set; }
        public decimal Value { get; set; }
        public string BatchNumber { get; set; }
    }

    /// <summary>
    /// Inventory monitoring result model
    /// </summary>
    public class InventoryMonitoringResult
    {
        public int LowStockItemsCount { get; set; }
        public int OutOfStockItemsCount { get; set; }
        public int ExpiringItemsCount { get; set; }
        public int AlertsGenerated { get; set; }
        public DateTime MonitoringTime { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    // Note: InventoryAlert class is defined in Models/InventoryAlert.cs

    // Note: InventoryAlertType (AlertType) enum is defined in Models/InventoryAlert.cs

    /// <summary>
    /// Inventory alert severity levels
    /// </summary>
    public enum InventoryAlertSeverity
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// Inventory movement trends model
    /// </summary>
    public class InventoryMovementTrends
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public decimal AverageMovementPerDay { get; set; }
        public decimal TrendDirection { get; set; }
        public List<DailyMovement> DailyMovements { get; set; } = new();
    }

    /// <summary>
    /// Daily movement model
    /// </summary>
    public class DailyMovement
    {
        public DateTime Date { get; set; }
        public int QuantityIn { get; set; }
        public int QuantityOut { get; set; }
        public int NetMovement { get; set; }
        public int EndingBalance { get; set; }
    }

    /// <summary>
    /// Reorder suggestion model
    /// </summary>
    public class ReorderSuggestion
    {
        public int ItemId { get; set; }
        public string ItemCode { get; set; }
        public string Name { get; set; }
        public int CurrentStock { get; set; }
        public int SuggestedOrderQuantity { get; set; }
        public decimal EstimatedCost { get; set; }
        public string Supplier { get; set; }
        public int DaysOfStockRemaining { get; set; }
        public ReorderPriority Priority { get; set; }
    }

    /// <summary>
    /// Reorder priority levels
    /// </summary>
    public enum ReorderPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Urgent = 4
    }

    /// <summary>
    /// Inventory health score model
    /// </summary>
    public class InventoryHealthScore
    {
        public decimal OverallScore { get; set; }
        public decimal StockLevelScore { get; set; }
        public decimal TurnoverScore { get; set; }
        public decimal ExpirationScore { get; set; }
        public decimal AccuracyScore { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public DateTime CalculatedAt { get; set; }
    }

    /// <summary>
    /// Event arguments for low stock detected events
    /// </summary>
    public class LowStockDetectedEventArgs : EventArgs
    {
        public LowStockItem Item { get; set; }
        public DateTime DetectedAt { get; set; }
    }

    /// <summary>
    /// Event arguments for out of stock events
    /// </summary>
    public class OutOfStockEventArgs : EventArgs
    {
        public Item Item { get; set; }
        public DateTime DetectedAt { get; set; }
    }

    /// <summary>
    /// Event arguments for expiration warning events
    /// </summary>
    public class ExpirationWarningEventArgs : EventArgs
    {
        public ExpiringItem Item { get; set; }
        public DateTime DetectedAt { get; set; }
    }

    /// <summary>
    /// Event arguments for inventory alert events
    /// </summary>
    public class InventoryAlertEventArgs : EventArgs
    {
        public InventoryAlert Alert { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
