using InventoryManagement.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for Point of Sale service
    /// </summary>
    public interface IPosService
    {
        /// <summary>
        /// Processes a sale transaction
        /// </summary>
        /// <param name="saleItems">Items being sold</param>
        /// <param name="customerId">Customer ID (optional)</param>
        /// <param name="paymentMethod">Payment method</param>
        /// <param name="userId">User processing the sale</param>
        /// <returns>Sale result</returns>
        Task<(bool Success, string Message, int? SaleId)> ProcessSaleAsync(
            List<SaleItem> saleItems, 
            int? customerId, 
            string paymentMethod, 
            int userId);

        /// <summary>
        /// Calculates the total for a list of sale items
        /// </summary>
        /// <param name="saleItems">Items to calculate total for</param>
        /// <returns>Total amount</returns>
        decimal CalculateTotal(List<SaleItem> saleItems);

        /// <summary>
        /// Validates a sale before processing
        /// </summary>
        /// <param name="saleItems">Items being sold</param>
        /// <returns>Validation result</returns>
        Task<(bool IsValid, string Message)> ValidateSaleAsync(List<SaleItem> saleItems);

        /// <summary>
        /// Applies a discount to a sale
        /// </summary>
        /// <param name="saleItems">Items in the sale</param>
        /// <param name="discountPercentage">Discount percentage</param>
        /// <returns>Updated sale items with discount applied</returns>
        List<SaleItem> ApplyDiscount(List<SaleItem> saleItems, decimal discountPercentage);

        /// <summary>
        /// Processes a refund
        /// </summary>
        /// <param name="saleId">Original sale ID</param>
        /// <param name="refundItems">Items to refund</param>
        /// <param name="reason">Refund reason</param>
        /// <param name="userId">User processing the refund</param>
        /// <returns>Refund result</returns>
        Task<(bool Success, string Message)> ProcessRefundAsync(
            int saleId, 
            List<SaleItem> refundItems, 
            string reason, 
            int userId);

        /// <summary>
        /// Voids a transaction
        /// </summary>
        /// <param name="transactionId">Transaction ID to void</param>
        /// <param name="reason">Void reason</param>
        /// <param name="userId">User voiding the transaction</param>
        /// <returns>Void result</returns>
        Task<(bool Success, string Message)> VoidTransactionAsync(
            int transactionId, 
            string reason, 
            int userId);

        /// <summary>
        /// Gets available payment methods
        /// </summary>
        /// <returns>List of payment methods</returns>
        List<string> GetPaymentMethods();

        /// <summary>
        /// Prints a receipt for a sale
        /// </summary>
        /// <param name="saleId">Sale ID</param>
        /// <returns>Print result</returns>
        Task<(bool Success, string Message)> PrintReceiptAsync(int saleId);

        /// <summary>
        /// Opens the cash drawer
        /// </summary>
        /// <returns>Operation result</returns>
        Task<(bool Success, string Message)> OpenCashDrawerAsync();

        /// <summary>
        /// Gets the current cash drawer balance
        /// </summary>
        /// <returns>Cash drawer balance</returns>
        Task<decimal> GetCashDrawerBalanceAsync();

        /// <summary>
        /// Records a cash drawer transaction (cash in/out)
        /// </summary>
        /// <param name="amount">Amount (positive for cash in, negative for cash out)</param>
        /// <param name="reason">Transaction reason</param>
        /// <param name="userId">User performing the transaction</param>
        /// <returns>Transaction result</returns>
        Task<(bool Success, string Message)> RecordCashDrawerTransactionAsync(
            decimal amount, 
            string reason, 
            int userId);
    }
}
