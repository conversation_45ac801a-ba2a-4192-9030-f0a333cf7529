using InventoryManagement.Models;
using InventoryManagement.Events;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple navigation service implementation for offline inventory management
    /// </summary>
    public class SimpleNavigationService : INavigationService
    {
        private readonly ILogger<SimpleNavigationService> _logger;
        private readonly Stack<string> _navigationHistory = new Stack<string>();
        private string _currentView;
        private Frame _navigationFrame;

        public SimpleNavigationService(ILogger<SimpleNavigationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public string CurrentView => _currentView;

        public bool CanGoBack => _navigationHistory.Count > 0;

        public event EventHandler<NavigationEventArgs> Navigated;
        public event EventHandler<NavigatingEventArgs> Navigating;
        public event EventHandler NavigationCompleted;

        public async Task<bool> NavigateToAsync(string viewName, object parameter = null)
        {
            try
            {
                _logger.LogInformation("Navigating to view: {ViewName}", viewName);

                // Raise navigating event
                var navigatingArgs = new NavigatingEventArgs { ViewName = viewName, Parameter = parameter };
                Navigating?.Invoke(this, navigatingArgs);

                if (navigatingArgs.Cancel)
                {
                    _logger.LogInformation("Navigation to {ViewName} was cancelled", viewName);
                    return false;
                }

                // Add current view to history if it exists
                if (!string.IsNullOrEmpty(_currentView))
                {
                    _navigationHistory.Push(_currentView);
                }

                // Set new current view
                _currentView = viewName;

                // Raise navigated event
                var navigatedArgs = new NavigationEventArgs { ViewName = viewName, Parameter = parameter };
                Navigated?.Invoke(this, navigatedArgs);

                // Raise navigation completed event
                NavigationCompleted?.Invoke(this, EventArgs.Empty);

                _logger.LogInformation("Successfully navigated to view: {ViewName}", viewName);
                await Task.CompletedTask;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error navigating to view: {ViewName}", viewName);
                return false;
            }
        }

        public async Task<bool> GoBackAsync()
        {
            try
            {
                if (!CanGoBack)
                {
                    _logger.LogWarning("Cannot go back - no navigation history");
                    return false;
                }

                var previousView = _navigationHistory.Pop();
                _logger.LogInformation("Going back to view: {ViewName}", previousView);

                return await NavigateToAsync(previousView);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error going back");
                return false;
            }
        }

        public void ClearHistory()
        {
            try
            {
                _navigationHistory.Clear();
                _logger.LogInformation("Navigation history cleared");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing navigation history");
            }
        }

        public List<string> GetNavigationHistory()
        {
            try
            {
                return new List<string>(_navigationHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting navigation history");
                return new List<string>();
            }
        }

        public async Task<bool> NavigateToWithReplacementAsync(string viewName, object parameter = null)
        {
            try
            {
                _logger.LogInformation("Navigating to view with replacement: {ViewName}", viewName);

                // Don't add current view to history - replace it
                _currentView = viewName;

                // Raise navigated event
                var navigatedArgs = new NavigationEventArgs { ViewName = viewName, Parameter = parameter };
                Navigated?.Invoke(this, navigatedArgs);

                _logger.LogInformation("Successfully navigated to view with replacement: {ViewName}", viewName);
                await Task.CompletedTask;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error navigating to view with replacement: {ViewName}", viewName);
                return false;
            }
        }

        public bool IsViewRegistered(string viewName)
        {
            // Simple implementation - assume all views are registered
            return !string.IsNullOrEmpty(viewName);
        }

        public void RegisterView(string viewName, Type viewType)
        {
            try
            {
                _logger.LogInformation("Registering view: {ViewName} -> {ViewType}", viewName, viewType.Name);
                // Simple implementation - no actual registration needed
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering view: {ViewName}", viewName);
            }
        }

        public void UnregisterView(string viewName)
        {
            try
            {
                _logger.LogInformation("Unregistering view: {ViewName}", viewName);
                // Simple implementation - no actual unregistration needed
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unregistering view: {ViewName}", viewName);
            }
        }

        // Additional interface methods to match INavigationService
        public void NavigateTo(string viewName)
        {
            _ = NavigateToAsync(viewName);
        }

        public void NavigateTo(string viewName, object parameter)
        {
            _ = NavigateToAsync(viewName, parameter);
        }

        public void GoBack()
        {
            _ = GoBackAsync();
        }

        public void RegisterFrame(Frame frame)
        {
            try
            {
                _navigationFrame = frame;
                _logger.LogInformation("Navigation frame registered");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering navigation frame");
            }
        }
    }

    // Note: Using existing NavigationEventArgs and INavigationAware from Models namespace

    /// <summary>
    /// Event arguments for navigating events (before navigation)
    /// </summary>
    public class NavigatingEventArgs : EventArgs
    {
        public string ViewName { get; set; } = string.Empty;
        public object Parameter { get; set; }
        public bool Cancel { get; set; }
    }
}
