using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Windows;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service to provide user-friendly error messages and notifications
    /// Converts technical errors into easy-to-understand messages
    /// </summary>
    public interface IUserFriendlyMessageService
    {
        void ShowError(string technicalMessage, string userFriendlyMessage = null);
        void ShowSuccess(string message);
        void ShowWarning(string message);
        void ShowInfo(string message);
        string GetUserFriendlyErrorMessage(Exception exception);
        void ShowOfflineNotification();
    }

    /// <summary>
    /// Implementation of user-friendly message service
    /// </summary>
    public class UserFriendlyMessageService : IUserFriendlyMessageService
    {
        private readonly ILogger<UserFriendlyMessageService> _logger;
        private readonly Dictionary<string, string> _errorMappings;

        public UserFriendlyMessageService(ILogger<UserFriendlyMessageService> logger)
        {
            _logger = logger;
            _errorMappings = InitializeErrorMappings();
        }

        public void ShowError(string technicalMessage, string userFriendlyMessage = null)
        {
            _logger.LogError("Technical error: {TechnicalMessage}", technicalMessage);
            
            var displayMessage = userFriendlyMessage ?? GetFriendlyMessage(technicalMessage);
            
            MessageBox.Show(
                displayMessage,
                "Error",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }

        public void ShowSuccess(string message)
        {
            _logger.LogInformation("Success: {Message}", message);
            
            MessageBox.Show(
                message,
                "Success",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        public void ShowWarning(string message)
        {
            _logger.LogWarning("Warning: {Message}", message);
            
            MessageBox.Show(
                message,
                "Warning",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }

        public void ShowInfo(string message)
        {
            _logger.LogInformation("Info: {Message}", message);
            
            MessageBox.Show(
                message,
                "Information",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        public string GetUserFriendlyErrorMessage(Exception exception)
        {
            if (exception == null) return "An unknown error occurred.";

            var technicalMessage = exception.Message.ToLowerInvariant();
            
            // Check for common error patterns
            foreach (var mapping in _errorMappings)
            {
                if (technicalMessage.Contains(mapping.Key))
                {
                    return mapping.Value;
                }
            }

            // Default friendly message
            return "Something went wrong. Please try again or contact support if the problem continues.";
        }

        public void ShowOfflineNotification()
        {
            ShowInfo("System is running in offline mode. All data is stored locally and no internet connection is required.");
        }

        private string GetFriendlyMessage(string technicalMessage)
        {
            if (string.IsNullOrEmpty(technicalMessage))
                return "An unknown error occurred.";

            var lowerMessage = technicalMessage.ToLowerInvariant();
            
            foreach (var mapping in _errorMappings)
            {
                if (lowerMessage.Contains(mapping.Key))
                {
                    return mapping.Value;
                }
            }

            return "Something went wrong. Please try again.";
        }

        private Dictionary<string, string> InitializeErrorMappings()
        {
            return new Dictionary<string, string>
            {
                // Database errors
                { "connection", "Unable to connect to the database. Please check if the system is properly set up." },
                { "timeout", "The operation took too long. Please try again." },
                { "deadlock", "The system is busy. Please wait a moment and try again." },
                { "constraint", "This action would create invalid data. Please check your input." },
                { "foreign key", "This item is being used elsewhere and cannot be deleted." },
                { "duplicate", "This item already exists. Please use a different name or code." },
                
                // File system errors
                { "access denied", "Permission denied. Please check if you have the right to access this file." },
                { "file not found", "The required file could not be found. Please check the installation." },
                { "disk full", "Not enough disk space. Please free up some space and try again." },
                { "path too long", "The file path is too long. Please use a shorter path." },
                
                // Network errors (should be rare in offline mode)
                { "network", "Network error detected. The system will continue working offline." },
                { "dns", "Network configuration issue. The system will continue working offline." },
                { "ssl", "Security certificate issue. The system will continue working offline." },
                
                // Hardware errors
                { "scanner", "Barcode scanner issue. Please check if the scanner is connected and try again." },
                { "printer", "Printer issue. Please check if the printer is connected and has paper." },
                { "device", "Hardware device issue. Please check connections and try again." },
                
                // Application errors
                { "null reference", "Missing information. Please make sure all required fields are filled." },
                { "argument", "Invalid input. Please check your data and try again." },
                { "format", "Invalid format. Please check your input format." },
                { "overflow", "Number too large. Please use a smaller number." },
                { "unauthorized", "You don't have permission to perform this action." },
                
                // Backup/restore errors
                { "backup", "Backup operation failed. Please check disk space and try again." },
                { "restore", "Restore operation failed. Please check the backup file and try again." },
                { "corrupt", "The file appears to be damaged. Please try a different file." },
                
                // Validation errors
                { "required", "Please fill in all required fields." },
                { "email", "Please enter a valid email address." },
                { "phone", "Please enter a valid phone number." },
                { "date", "Please enter a valid date." },
                { "number", "Please enter a valid number." },
                
                // Business logic errors
                { "stock", "Not enough items in stock for this operation." },
                { "price", "Invalid price. Please enter a valid price." },
                { "quantity", "Invalid quantity. Please enter a valid quantity." },
                { "customer", "Customer information issue. Please check customer details." },
                { "product", "Product information issue. Please check product details." },
                
                // System errors
                { "memory", "System is running low on memory. Please close other applications." },
                { "cpu", "System is very busy. Please wait a moment and try again." },
                { "startup", "System startup issue. Please restart the application." },
                { "shutdown", "System shutdown issue. Some data may not have been saved." }
            };
        }
    }

    /// <summary>
    /// Extension methods for easy message display
    /// </summary>
    public static class MessageServiceExtensions
    {
        public static void ShowUserFriendlyError(this Exception exception, IUserFriendlyMessageService messageService)
        {
            var friendlyMessage = messageService.GetUserFriendlyErrorMessage(exception);
            messageService.ShowError(exception.Message, friendlyMessage);
        }
    }
}
