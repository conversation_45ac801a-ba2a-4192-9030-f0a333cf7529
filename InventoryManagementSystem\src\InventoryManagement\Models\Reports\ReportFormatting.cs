using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Represents formatting options for reports
    /// </summary>
    public class ReportFormatting
    {
        /// <summary>
        /// Font family for the report
        /// </summary>
        public string FontFamily { get; set; } = "Arial";

        /// <summary>
        /// Font size for the report
        /// </summary>
        public int FontSize { get; set; } = 12;

        /// <summary>
        /// Whether to use bold font
        /// </summary>
        public bool IsBold { get; set; }

        /// <summary>
        /// Whether to use italic font
        /// </summary>
        public bool IsItalic { get; set; }

        /// <summary>
        /// Text color in hex format
        /// </summary>
        public string TextColor { get; set; } = "#000000";

        /// <summary>
        /// Background color in hex format
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// Header formatting
        /// </summary>
        public HeaderFormatting Header { get; set; } = new HeaderFormatting();

        /// <summary>
        /// Footer formatting
        /// </summary>
        public FooterFormatting Footer { get; set; } = new FooterFormatting();

        /// <summary>
        /// Table formatting
        /// </summary>
        public TableFormatting Table { get; set; } = new TableFormatting();

        /// <summary>
        /// Page margins
        /// </summary>
        public PageMargins Margins { get; set; } = new PageMargins();

        /// <summary>
        /// Page orientation
        /// </summary>
        public PageOrientation Orientation { get; set; } = PageOrientation.Portrait;

        /// <summary>
        /// Page size
        /// </summary>
        public PageSize Size { get; set; } = PageSize.A4;

        /// <summary>
        /// Whether to include page numbers
        /// </summary>
        public bool IncludePageNumbers { get; set; } = true;

        /// <summary>
        /// Whether to include date and time
        /// </summary>
        public bool IncludeDateTime { get; set; } = true;

        /// <summary>
        /// Custom CSS styles
        /// </summary>
        public string CustomCss { get; set; }

        /// <summary>
        /// Logo settings
        /// </summary>
        public LogoSettings Logo { get; set; } = new LogoSettings();
    }

    /// <summary>
    /// Header formatting options
    /// </summary>
    public class HeaderFormatting
    {
        /// <summary>
        /// Header font size
        /// </summary>
        public int FontSize { get; set; } = 16;

        /// <summary>
        /// Whether header should be bold
        /// </summary>
        public bool IsBold { get; set; } = true;

        /// <summary>
        /// Header text color
        /// </summary>
        public string TextColor { get; set; } = "#000000";

        /// <summary>
        /// Header background color
        /// </summary>
        public string BackgroundColor { get; set; } = "#F5F5F5";

        /// <summary>
        /// Header alignment
        /// </summary>
        public TextAlignment Alignment { get; set; } = TextAlignment.Center;

        /// <summary>
        /// Whether to include company logo
        /// </summary>
        public bool IncludeLogo { get; set; } = true;

        /// <summary>
        /// Whether to include company name
        /// </summary>
        public bool IncludeCompanyName { get; set; } = true;
    }

    /// <summary>
    /// Footer formatting options
    /// </summary>
    public class FooterFormatting
    {
        /// <summary>
        /// Footer font size
        /// </summary>
        public int FontSize { get; set; } = 10;

        /// <summary>
        /// Footer text color
        /// </summary>
        public string TextColor { get; set; } = "#666666";

        /// <summary>
        /// Footer alignment
        /// </summary>
        public TextAlignment Alignment { get; set; } = TextAlignment.Center;

        /// <summary>
        /// Whether to include page numbers
        /// </summary>
        public bool IncludePageNumbers { get; set; } = true;

        /// <summary>
        /// Whether to include generation date
        /// </summary>
        public bool IncludeGenerationDate { get; set; } = true;

        /// <summary>
        /// Custom footer text
        /// </summary>
        public string CustomText { get; set; }
    }

    /// <summary>
    /// Table formatting options
    /// </summary>
    public class TableFormatting
    {
        /// <summary>
        /// Whether to show table borders
        /// </summary>
        public bool ShowBorders { get; set; } = true;

        /// <summary>
        /// Border color
        /// </summary>
        public string BorderColor { get; set; } = "#CCCCCC";

        /// <summary>
        /// Border width in pixels
        /// </summary>
        public int BorderWidth { get; set; } = 1;

        /// <summary>
        /// Whether to alternate row colors
        /// </summary>
        public bool AlternateRowColors { get; set; } = true;

        /// <summary>
        /// Primary row color
        /// </summary>
        public string PrimaryRowColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// Alternate row color
        /// </summary>
        public string AlternateRowColor { get; set; } = "#F9F9F9";

        /// <summary>
        /// Header row color
        /// </summary>
        public string HeaderRowColor { get; set; } = "#E0E0E0";

        /// <summary>
        /// Cell padding in pixels
        /// </summary>
        public int CellPadding { get; set; } = 5;
    }

    /// <summary>
    /// Page margin settings
    /// </summary>
    public class PageMargins
    {
        /// <summary>
        /// Top margin in inches
        /// </summary>
        public double Top { get; set; } = 1.0;

        /// <summary>
        /// Bottom margin in inches
        /// </summary>
        public double Bottom { get; set; } = 1.0;

        /// <summary>
        /// Left margin in inches
        /// </summary>
        public double Left { get; set; } = 1.0;

        /// <summary>
        /// Right margin in inches
        /// </summary>
        public double Right { get; set; } = 1.0;
    }

    /// <summary>
    /// Logo settings for reports
    /// </summary>
    public class LogoSettings
    {
        /// <summary>
        /// Whether to include logo
        /// </summary>
        public bool IncludeLogo { get; set; } = true;

        /// <summary>
        /// Logo file path
        /// </summary>
        public string LogoPath { get; set; }

        /// <summary>
        /// Logo width in pixels
        /// </summary>
        public int Width { get; set; } = 100;

        /// <summary>
        /// Logo height in pixels
        /// </summary>
        public int Height { get; set; } = 50;

        /// <summary>
        /// Logo alignment
        /// </summary>
        public TextAlignment Alignment { get; set; } = TextAlignment.Left;
    }

    /// <summary>
    /// Text alignment options
    /// </summary>
    public enum TextAlignment
    {
        Left,
        Center,
        Right,
        Justify
    }

    /// <summary>
    /// Page orientation options
    /// </summary>
    public enum PageOrientation
    {
        Portrait,
        Landscape
    }

    /// <summary>
    /// Page size options
    /// </summary>
    public enum PageSize
    {
        A4,
        A3,
        Letter,
        Legal,
        Tabloid
    }
}
