using System.Collections.Generic;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Validation result containing validation status and errors
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();
    }

    /// <summary>
    /// Validation error information
    /// </summary>
    public class ValidationError
    {
        public string PropertyName { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// Import result for data import operations
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int ImportedCount { get; set; }
        public int ErrorCount { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Stock update model for bulk operations
    /// </summary>
    public class StockUpdateModel
    {
        public int ProductId { get; set; }
        public int NewQuantity { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string UpdatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Query metrics for database monitoring
    /// </summary>
    public class QueryMetrics
    {
        public string QueryName { get; set; } = string.Empty;
        public double ExecutionTimeMs { get; set; }
        public int RowsAffected { get; set; }
        public DateTime ExecutedAt { get; set; }
    }

    /// <summary>
    /// Keyboard shortcut definition
    /// </summary>
    public class KeyboardShortcut
    {
        public string Name { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string Modifiers { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
    }

    // Note: TopSellingProduct/TopSellingItem is defined in InventoryModels.cs

    /// <summary>
    /// Low stock report item
    /// </summary>
    public class LowStockReport
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string SKU { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int ReorderLevel { get; set; }
        public int SuggestedOrderQuantity { get; set; }
    }

    /// <summary>
    /// Customer sales report
    /// </summary>
    public class CustomerSalesReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<CustomerSalesData> CustomerSales { get; set; } = new List<CustomerSalesData>();
    }

    /// <summary>
    /// Customer sales data
    /// </summary>
    public class CustomerSalesData
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
    }
}
