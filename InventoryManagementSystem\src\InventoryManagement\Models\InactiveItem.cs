using System;
using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents an item that has been inactive (no sales/movement) for a period
    /// </summary>
    public class InactiveItem
    {
        /// <summary>
        /// Unique identifier for the inactive item record
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// ID of the item
        /// </summary>
        [Required]
        public int ItemId { get; set; }

        /// <summary>
        /// Item name
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; }

        /// <summary>
        /// Item SKU
        /// </summary>
        [StringLength(50)]
        public string SKU { get; set; }

        /// <summary>
        /// Category name
        /// </summary>
        [StringLength(100)]
        public string CategoryName { get; set; }

        /// <summary>
        /// Current quantity in stock
        /// </summary>
        [Required]
        public int CurrentQuantity { get; set; }

        /// <summary>
        /// Cost price of the item
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// Selling price of the item
        /// </summary>
        public decimal SellingPrice { get; set; }

        /// <summary>
        /// Total value of inactive stock (Quantity * CostPrice)
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// Date of last sale
        /// </summary>
        public DateTime? LastSaleDate { get; set; }

        /// <summary>
        /// Date of last stock movement
        /// </summary>
        public DateTime? LastMovementDate { get; set; }

        /// <summary>
        /// Number of days since last activity
        /// </summary>
        public int DaysSinceLastActivity { get; set; }

        /// <summary>
        /// Location where the item is stored
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// Location name
        /// </summary>
        [StringLength(100)]
        public string LocationName { get; set; }

        /// <summary>
        /// Supplier information
        /// </summary>
        [StringLength(200)]
        public string SupplierName { get; set; }

        /// <summary>
        /// Reason for inactivity classification
        /// </summary>
        [StringLength(500)]
        public string InactivityReason { get; set; }

        /// <summary>
        /// Recommended action (Discount, Promote, Return to Supplier, Dispose)
        /// </summary>
        [StringLength(100)]
        public string RecommendedAction { get; set; }

        /// <summary>
        /// Priority level for handling (High, Medium, Low)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Priority { get; set; }

        /// <summary>
        /// Status of the inactive item (Active, Under Review, Action Taken)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; }

        /// <summary>
        /// Notes about the inactive item
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Date when the item was marked as inactive
        /// </summary>
        public DateTime MarkedInactiveDate { get; set; }

        /// <summary>
        /// User who marked the item as inactive
        /// </summary>
        [StringLength(100)]
        public string MarkedBy { get; set; }

        /// <summary>
        /// Date when action was taken (if any)
        /// </summary>
        public DateTime? ActionTakenDate { get; set; }

        /// <summary>
        /// User who took action
        /// </summary>
        [StringLength(100)]
        public string ActionTakenBy { get; set; }

        /// <summary>
        /// Description of action taken
        /// </summary>
        [StringLength(500)]
        public string ActionDescription { get; set; }

        /// <summary>
        /// Date when the record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when the record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Whether the item is critically inactive (very long period)
        /// </summary>
        public bool IsCriticallyInactive => DaysSinceLastActivity > 180;

        /// <summary>
        /// Whether the item needs immediate attention
        /// </summary>
        public bool NeedsImmediateAttention => Priority == "High" && Status == "Active";
    }
}
