using InventoryManagement.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Windows;
using System.Windows.Input;

namespace InventoryManagement.Views
{
    /// <summary>
    /// Interaction logic for ComprehensiveMainDashboard.xaml
    /// Full-featured main dashboard with complete business functionality
    /// </summary>
    public partial class ComprehensiveMainDashboard : Window
    {
        private readonly ILogger<ComprehensiveMainDashboard> _logger;
        private readonly ComprehensiveMainDashboardViewModel _viewModel;

        public ComprehensiveMainDashboard()
        {
            InitializeComponent();
            
            try
            {
                // Get services from DI container
                var serviceProvider = ((App)Application.Current).ServiceProvider;
                _logger = serviceProvider.GetRequiredService<ILogger<ComprehensiveMainDashboard>>();
                _viewModel = serviceProvider.GetRequiredService<ComprehensiveMainDashboardViewModel>();
                
                // Set DataContext
                DataContext = _viewModel;
                
                _logger.LogInformation("Comprehensive Main Dashboard initialized successfully");
            }
            catch (Exception ex)
            {
                // Fallback error handling if D<PERSON> fails
                MessageBox.Show($"Error initializing dashboard: {ex.Message}", 
                    "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handle window loaded event
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger?.LogInformation("Comprehensive Main Dashboard window loaded");
                
                // Set focus to the main content
                Focus();
                
                // Show welcome message for first-time users
                ShowWelcomeMessageIfNeeded();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during window load");
            }
        }

        /// <summary>
        /// Handle window closing event
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                _logger?.LogInformation("Comprehensive Main Dashboard closing");
                
                // Ask for confirmation before closing
                var result = MessageBox.Show(
                    "Are you sure you want to close the application?", 
                    "Confirm Exit", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
                
                // Close all other windows
                CloseAllOtherWindows();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during window closing");
            }
        }

        /// <summary>
        /// Show welcome message for new users
        /// </summary>
        private void ShowWelcomeMessageIfNeeded()
        {
            try
            {
                // Check if this is the first time running the application
                var settings = Properties.Settings.Default;
                
                if (!settings.HasShownWelcome)
                {
                    var welcomeMessage = @"Welcome to Tom General Trading Complete Business Management System!

This comprehensive system provides full business functionality:

MAIN OPERATIONS:
• Complete Point of Sale system with hardware integration
• Advanced Inventory Management with stock tracking
• Customer Relationship Management (CRM)
• Financial Management and reporting

BUSINESS INTELLIGENCE:
• Real-time analytics and reporting
• Sales performance tracking
• Inventory optimization
• Financial insights and KPIs

SYSTEM FEATURES:
• Complete offline operation - no internet required
• Automatic data backup and recovery
• Hardware integration (scanners, printers)
• Multi-user support with role-based access
• Advanced security and audit trails

The system is designed for complete business operations and scales with your needs.

Would you like to see the setup wizard to configure your system?";

                    var result = MessageBox.Show(welcomeMessage, 
                        "Welcome to Complete Business Management", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Information);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        // Open setup wizard
                        ShowSetupWizard();
                    }
                    
                    // Mark welcome as shown
                    settings.HasShownWelcome = true;
                    settings.Save();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing welcome message");
            }
        }

        /// <summary>
        /// Show the setup wizard for new users
        /// </summary>
        private void ShowSetupWizard()
        {
            try
            {
                _logger?.LogInformation("Opening setup wizard");
                
                var setupWizard = new SetupWizardWindow();
                setupWizard.Owner = this;
                setupWizard.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error opening setup wizard");
                MessageBox.Show("Unable to open setup wizard. You can access system settings from the Settings menu.", 
                    "Setup Wizard", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// Close all other application windows
        /// </summary>
        private void CloseAllOtherWindows()
        {
            try
            {
                var windows = Application.Current.Windows;
                for (int i = windows.Count - 1; i >= 0; i--)
                {
                    var window = windows[i];
                    if (window != this && window.GetType() != typeof(Views.LoginWindow))
                    {
                        window.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error closing other windows");
            }
        }

        /// <summary>
        /// Handle keyboard shortcuts
        /// </summary>
        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Handle common keyboard shortcuts
                if (e.Key == Key.F1)
                {
                    // F1 - Help
                    _viewModel?.ShowHelpCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F2)
                {
                    // F2 - POS
                    _viewModel?.ShowPOSCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F3)
                {
                    // F3 - Inventory
                    _viewModel?.ShowInventoryCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F4)
                {
                    // F4 - Customers
                    _viewModel?.ShowCustomersCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F5)
                {
                    // F5 - Reports
                    _viewModel?.ShowReportsCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F6)
                {
                    // F6 - Dashboard
                    _viewModel?.ShowDashboardCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F9)
                {
                    // F9 - Settings
                    _viewModel?.ShowSettingsCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.F10)
                {
                    // F10 - Backup
                    _viewModel?.ShowBackupCommand?.Execute(null);
                    e.Handled = true;
                }
                else if (e.Key == Key.Escape)
                {
                    // Escape - Minimize
                    WindowState = WindowState.Minimized;
                    e.Handled = true;
                }
                else if (Keyboard.Modifiers == ModifierKeys.Control)
                {
                    // Ctrl+Q - Quit
                    if (e.Key == Key.Q)
                    {
                        Close();
                        e.Handled = true;
                    }
                    // Ctrl+R - Refresh current view
                    else if (e.Key == Key.R)
                    {
                        RefreshCurrentView();
                        e.Handled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error handling keyboard shortcut");
            }
        }

        /// <summary>
        /// Refresh the current view
        /// </summary>
        private void RefreshCurrentView()
        {
            try
            {
                _logger?.LogInformation("Refreshing current view");
                
                // Trigger refresh of current view
                // This could be enhanced to call specific refresh methods on the current view
                
                ShowStatusMessage("View refreshed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error refreshing current view");
                ShowStatusMessage("Error refreshing view");
            }
        }

        /// <summary>
        /// Show a temporary status message
        /// </summary>
        private void ShowStatusMessage(string message)
        {
            try
            {
                // Update the status message in the view model
                if (_viewModel != null)
                {
                    _viewModel.StatusMessage = message;
                    
                    // Reset status message after 3 seconds
                    var timer = new System.Windows.Threading.DispatcherTimer();
                    timer.Interval = TimeSpan.FromSeconds(3);
                    timer.Tick += (s, e) =>
                    {
                        _viewModel.StatusMessage = "System ready for operations";
                        timer.Stop();
                    };
                    timer.Start();
                }
                
                _logger?.LogInformation("Status: {Message}", message);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing status message");
            }
        }
    }
}
