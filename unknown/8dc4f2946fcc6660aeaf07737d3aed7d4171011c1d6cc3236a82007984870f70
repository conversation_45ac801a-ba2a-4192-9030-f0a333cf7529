<Window x:Class="InventoryManagement.Views.ComprehensiveMainDashboard"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Tom General Trading - Complete Business Management System" 
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        KeyDown="Window_KeyDown"
        Loaded="Window_Loaded"
        Closing="Window_Closing">

    <Window.Resources>
        <!-- Navigation Button Style -->
        <Style x:Key="NavButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Padding" Value="15,0"/>
        </Style>

        <!-- Feature Card Style -->
        <Style x:Key="FeatureCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        </Style>

        <!-- Stats Card Style -->
        <Style x:Key="StatsCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- Action Button Style -->
        <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Left Navigation Panel -->
        <materialDesign:Card Grid.Column="0" Margin="5" Background="{DynamicResource MaterialDesignDarkBackground}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Company Header -->
                <Border Grid.Row="0" Margin="15" Background="{DynamicResource PrimaryHueMidBrush}" Padding="15">
                <StackPanel Orientation="Vertical">
                    <TextBlock Text="Tom General Trading" FontSize="16" FontWeight="Bold" 
                               Foreground="White" TextAlignment="Center"/>
                    <TextBlock Text="Business Management" FontSize="12" 
                               Foreground="White" Opacity="0.9" TextAlignment="Center"/>
                    <Separator Margin="0,10" Background="White" Opacity="0.3"/>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountCircle" Width="20" Height="20" 
                                               Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding CurrentUser.FullName}" FontSize="12" 
                                   Foreground="White" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
                </Border>

                <!-- Navigation Menu -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="5">
                        
                        <!-- Main Operations -->
                        <TextBlock Text="MAIN OPERATIONS" FontSize="11" FontWeight="Bold" 
                                   Foreground="Gray" Margin="10,15,10,5"/>
                        
                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowDashboardCommand}"
                                Background="{Binding DashboardBackground}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Dashboard" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowPOSCommand}"
                                Background="{Binding POSBackground}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashRegister" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Point of Sale" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowInventoryCommand}"
                                Background="{Binding InventoryBackground}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Package" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Inventory Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowCustomersCommand}"
                                Background="{Binding CustomersBackground}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Customer Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Analytics & Reports -->
                        <TextBlock Text="ANALYTICS &amp; REPORTS" FontSize="11" FontWeight="Bold"
                                   Foreground="Gray" Margin="10,20,10,5"/>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowReportsCommand}"
                                Background="{Binding ReportsBackground}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Reports &amp; Analytics" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowFinancialCommand}"
                                Background="{Binding FinancialBackground}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashMultiple" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Financial Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory Operations -->
                        <TextBlock Text="INVENTORY OPERATIONS" FontSize="11" FontWeight="Bold" 
                                   Foreground="Gray" Margin="10,20,10,5"/>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowStockManagementCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Warehouse" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Stock Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowPurchaseOrdersCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ShoppingCart" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Purchase Orders" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowSuppliersCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TruckDelivery" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Supplier Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- System Management -->
                        <TextBlock Text="SYSTEM MANAGEMENT" FontSize="11" FontWeight="Bold" 
                                   Foreground="Gray" Margin="10,20,10,5"/>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowUsersCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountMultiple" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="User Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowSettingsCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="System Settings" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowBackupCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Backup" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Backup &amp; Restore" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Hardware Integration -->
                        <TextBlock Text="HARDWARE" FontSize="11" FontWeight="Bold" 
                                   Foreground="Gray" Margin="10,20,10,5"/>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowHardwareCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Printer" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Hardware Setup" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowBarcodeCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Barcode" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Barcode Management" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </ScrollViewer>

                <!-- Bottom Actions -->
                <StackPanel Grid.Row="2" Margin="10">
                    <Separator Margin="0,10" Background="Gray" Opacity="0.3"/>
                    
                    <Button Style="{StaticResource NavButtonStyle}" Command="{Binding ShowHelpCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="HelpCircle" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock Text="Help &amp; Support" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource NavButtonStyle}" Command="{Binding LogoutCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Logout" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock Text="Logout" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content Area -->
        <Grid Grid.Column="1" Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Header Bar -->
            <materialDesign:Card Grid.Row="0" Margin="0,0,0,5" Padding="15" Background="{DynamicResource PrimaryHueMidBrush}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Vertical">
                        <TextBlock Text="{Binding CurrentViewTitle}" FontSize="20" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="{Binding CurrentDateTime, StringFormat='Today: {0:dddd, MMMM dd, yyyy HH:mm}'}" 
                                   FontSize="12" Foreground="White" Opacity="0.9"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                        <materialDesign:PackIcon Kind="DatabaseCheck" Width="16" Height="16" 
                                               Foreground="LightGreen" VerticalAlignment="Center"/>
                        <TextBlock Text="Database: Connected" Foreground="LightGreen" 
                                   Margin="5,0,0,0" VerticalAlignment="Center" FontSize="12"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="WifiOff" Width="16" Height="16" 
                                               Foreground="LightBlue" VerticalAlignment="Center"/>
                        <TextBlock Text="Offline Mode" Foreground="LightBlue" 
                                   Margin="5,0,0,0" VerticalAlignment="Center" FontSize="12"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Dynamic Content Area -->
            <ContentControl Grid.Row="1" Content="{Binding CurrentView}" />

            <!-- Status Bar -->
            <materialDesign:Card Grid.Row="2" Margin="0,5,0,0" Padding="10" Background="{DynamicResource MaterialDesignDarkBackground}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" 
                               Foreground="White" VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                        <TextBlock Text="Active Users: " Foreground="Gray" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ActiveUsersCount}" Foreground="White" VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <TextBlock Text="System Ready" Foreground="LightGreen" VerticalAlignment="Center"/>
                        <materialDesign:PackIcon Kind="CheckCircle" Width="16" Height="16" 
                                               Foreground="LightGreen" VerticalAlignment="Center" Margin="5,0,0,0"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Window>
