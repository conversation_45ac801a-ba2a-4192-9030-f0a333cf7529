﻿#pragma checksum "..\..\..\..\Views\AdminDashboard.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "07187DB0829A2EBC6076A539169B49DCDA7E7BDC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InventoryManagement.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InventoryManagement.Views {
    
    
    /// <summary>
    /// AdminDashboard
    /// </summary>
    public partial class AdminDashboard : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 37 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardNavButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UsersNavButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ItemsNavButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LocationsNavButton;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransactionsNavButton;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsNavButton;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransactionVerificationNavButton;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsNavButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ContentArea;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalItemsCount;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalMoneyText;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyIncomeText;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyIncomeDate;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyExpenseText;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyProfitText;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitPercentage;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailySalesCount;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesComparisonText;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalBasementsCount;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RecentTransactionsGrid;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ItemsToVerifyGrid;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl UsersContent;
        
        #line default
        #line hidden
        
        
        #line 398 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl ItemsContent;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl LocationsContent;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl TransactionsContent;
        
        #line default
        #line hidden
        
        
        #line 401 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl ReportsContent;
        
        #line default
        #line hidden
        
        
        #line 402 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl SettingsContent;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ItemLocationBreakdown;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView LocationBreakdownList;
        
        #line default
        #line hidden
        
        
        #line 484 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MoneyBreakdown;
        
        #line default
        #line hidden
        
        
        #line 505 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView MoneyBreakdownList;
        
        #line default
        #line hidden
        
        
        #line 555 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ExpenseBreakdown;
        
        #line default
        #line hidden
        
        
        #line 576 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView ExpenseBreakdownList;
        
        #line default
        #line hidden
        
        
        #line 626 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid BasementBreakdown;
        
        #line default
        #line hidden
        
        
        #line 647 "..\..\..\..\Views\AdminDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView BasementBreakdownList;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InventoryManagement;component/views/admindashboard.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AdminDashboard.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 47 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DashboardNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\..\Views\AdminDashboard.xaml"
            this.DashboardNavButton.Click += new System.Windows.RoutedEventHandler(this.DashboardNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.UsersNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\Views\AdminDashboard.xaml"
            this.UsersNavButton.Click += new System.Windows.RoutedEventHandler(this.UsersNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ItemsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\Views\AdminDashboard.xaml"
            this.ItemsNavButton.Click += new System.Windows.RoutedEventHandler(this.ItemsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.LocationsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\Views\AdminDashboard.xaml"
            this.LocationsNavButton.Click += new System.Windows.RoutedEventHandler(this.LocationsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TransactionsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\Views\AdminDashboard.xaml"
            this.TransactionsNavButton.Click += new System.Windows.RoutedEventHandler(this.TransactionsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ReportsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\Views\AdminDashboard.xaml"
            this.ReportsNavButton.Click += new System.Windows.RoutedEventHandler(this.ReportsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TransactionVerificationNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\..\Views\AdminDashboard.xaml"
            this.TransactionVerificationNavButton.Click += new System.Windows.RoutedEventHandler(this.TransactionVerificationNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SettingsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\..\Views\AdminDashboard.xaml"
            this.SettingsNavButton.Click += new System.Windows.RoutedEventHandler(this.SettingsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ContentArea = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.DashboardContent = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 13:
            
            #line 167 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TotalItemsCard_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.TotalItemsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            
            #line 189 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TotalMoneyCard_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TotalMoneyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.DailyIncomeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.DailyIncomeDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            
            #line 232 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.DailyExpenseCard_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.DailyExpenseText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.DailyProfitText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.ProfitPercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.DailySalesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.SalesComparisonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            
            #line 296 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TotalBasementsCard_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.TotalBasementsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.RecentTransactionsGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 29:
            this.ItemsToVerifyGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 32:
            this.UsersContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 33:
            this.ItemsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 34:
            this.LocationsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 35:
            this.TransactionsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 36:
            this.ReportsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 37:
            this.SettingsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 38:
            this.ItemLocationBreakdown = ((System.Windows.Controls.Grid)(target));
            return;
            case 39:
            this.LocationBreakdownList = ((System.Windows.Controls.ListView)(target));
            return;
            case 41:
            
            #line 474 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateLocationReport_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            
            #line 477 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBreakdown_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.MoneyBreakdown = ((System.Windows.Controls.Grid)(target));
            return;
            case 44:
            this.MoneyBreakdownList = ((System.Windows.Controls.ListView)(target));
            return;
            case 45:
            
            #line 545 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateLocationReport_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 548 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBreakdown_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.ExpenseBreakdown = ((System.Windows.Controls.Grid)(target));
            return;
            case 48:
            this.ExpenseBreakdownList = ((System.Windows.Controls.ListView)(target));
            return;
            case 49:
            
            #line 616 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateLocationReport_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            
            #line 619 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBreakdown_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.BasementBreakdown = ((System.Windows.Controls.Grid)(target));
            return;
            case 52:
            this.BasementBreakdownList = ((System.Windows.Controls.ListView)(target));
            return;
            case 54:
            
            #line 695 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBasementBreakdown_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 28:
            
            #line 340 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewTransactionButton_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 376 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewTransactionButton_Click);
            
            #line default
            #line hidden
            break;
            case 31:
            
            #line 381 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.VerifyButton_Click);
            
            #line default
            #line hidden
            break;
            case 40:
            
            #line 455 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewLocationDetails_Click);
            
            #line default
            #line hidden
            break;
            case 53:
            
            #line 681 "..\..\..\..\Views\AdminDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewLocationDetails_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

