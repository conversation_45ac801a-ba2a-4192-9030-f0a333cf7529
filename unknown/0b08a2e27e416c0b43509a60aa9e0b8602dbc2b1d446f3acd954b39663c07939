using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Repository implementation for Inventory operations
    /// </summary>
    public class InventoryRepository : Repository<Inventory>, IInventoryRepository
    {
        private readonly ILogger<InventoryRepository> _logger;

        public InventoryRepository(ApplicationDbContext context, ILogger<InventoryRepository> logger) 
            : base(context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets inventory by item ID
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Inventory for the specified item</returns>
        public async Task<Inventory> GetByItemIdAsync(int itemId)
        {
            try
            {
                return await _context.Inventory
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.ItemId == itemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by item ID: {ItemId}", itemId);
                throw;
            }
        }

        /// <summary>
        /// Gets inventory for items with stock below their reorder level
        /// </summary>
        /// <returns>Low stock inventory items</returns>
        public async Task<IEnumerable<Inventory>> GetLowStockAsync()
        {
            try
            {
                return await _context.Inventory
                    .Where(i => i.CurrentStock <= i.ReorderLevel && i.ReorderLevel > 0)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock inventory items");
                throw;
            }
        }

        /// <summary>
        /// Gets inventory transactions for a specific date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Inventory transactions in the specified date range</returns>
        public async Task<IEnumerable<Inventory>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Inventory
                    .Where(i => i.LastUpdated >= startDate && i.LastUpdated <= endDate)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by date range: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets inventory by location
        /// </summary>
        /// <param name="locationId">Location ID</param>
        /// <returns>Inventory at the specified location</returns>
        public async Task<IEnumerable<Inventory>> GetByLocationAsync(int locationId)
        {
            try
            {
                return await _context.Inventory
                    .Where(i => i.LocationId == locationId)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by location: {LocationId}", locationId);
                throw;
            }
        }

        /// <summary>
        /// Gets inventory with item details
        /// </summary>
        /// <returns>Inventory with related item information</returns>
        public async Task<IEnumerable<Inventory>> GetWithItemDetailsAsync()
        {
            try
            {
                return await _context.Inventory
                    .Include(i => i.Item)
                    .Include(i => i.Location)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory with item details");
                throw;
            }
        }

        /// <summary>
        /// Gets out of stock items
        /// </summary>
        /// <returns>Inventory items that are out of stock</returns>
        public async Task<IEnumerable<Inventory>> GetOutOfStockAsync()
        {
            try
            {
                return await _context.Inventory
                    .Where(i => i.CurrentStock <= 0)
                    .AsNoTracking()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting out of stock inventory items");
                throw;
            }
        }

        /// <summary>
        /// Gets inventory by item and location
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="locationId">Location ID</param>
        /// <returns>Inventory for the specified item at the specified location</returns>
        public async Task<Inventory> GetByItemAndLocationAsync(int itemId, int locationId)
        {
            try
            {
                return await _context.Inventory
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.ItemId == itemId && i.LocationId == locationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory by item and location: ItemId={ItemId}, LocationId={LocationId}", itemId, locationId);
                throw;
            }
        }

        /// <summary>
        /// Updates stock quantity for an inventory item
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="locationId">Location ID</param>
        /// <param name="newQuantity">New stock quantity</param>
        /// <param name="userId">User ID making the change</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateStockQuantityAsync(int itemId, int locationId, decimal newQuantity, int userId)
        {
            try
            {
                var inventory = await _context.Inventory
                    .FirstOrDefaultAsync(i => i.ItemId == itemId && i.LocationId == locationId);

                if (inventory == null)
                    return false;

                inventory.CurrentStock = newQuantity;
                inventory.LastUpdated = DateTime.Now;
                inventory.LastUpdatedByUserId = userId;

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating stock quantity: ItemId={ItemId}, LocationId={LocationId}, NewQuantity={NewQuantity}", 
                    itemId, locationId, newQuantity);
                throw;
            }
        }

        /// <summary>
        /// Gets total stock value across all locations
        /// </summary>
        /// <returns>Total value of all inventory</returns>
        public async Task<decimal> GetTotalStockValueAsync()
        {
            try
            {
                return await _context.Inventory
                    .Include(i => i.Item)
                    .SumAsync(i => i.CurrentStock * i.Item.CostPrice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total stock value");
                throw;
            }
        }
    }
}
