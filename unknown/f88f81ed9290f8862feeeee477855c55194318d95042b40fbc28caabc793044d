using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Repository implementation for DefectiveItem operations
    /// </summary>
    public class DefectiveItemRepository : Repository<DefectiveItem>, IDefectiveItemRepository
    {
        private readonly ILogger<DefectiveItemRepository> _logger;

        public DefectiveItemRepository(ApplicationDbContext context, ILogger<DefectiveItemRepository> logger) 
            : base(context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets defective items by item ID
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Defective items with the specified item ID</returns>
        public async Task<IEnumerable<DefectiveItem>> GetByItemIdAsync(int itemId)
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.ItemId == itemId)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .Include(d => d.StatusHistory)
                    .AsNoTracking()
                    .OrderByDescending(d => d.RegistrationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items by item ID: {ItemId}", itemId);
                throw;
            }
        }

        /// <summary>
        /// Gets defective items reported by a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Defective items reported by the specified user</returns>
        public async Task<IEnumerable<DefectiveItem>> GetByReportedByAsync(int userId)
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.BasementManagerId == userId)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .Include(d => d.StatusHistory)
                    .AsNoTracking()
                    .OrderByDescending(d => d.RegistrationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items by reported by user: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Gets defective items for a specific date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Defective items reported in the specified date range</returns>
        public async Task<IEnumerable<DefectiveItem>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.RegistrationDate >= startDate && d.RegistrationDate <= endDate)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .Include(d => d.StatusHistory)
                    .AsNoTracking()
                    .OrderByDescending(d => d.RegistrationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items by date range: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets defective items by status
        /// </summary>
        /// <param name="status">Defective item status</param>
        /// <returns>Defective items with the specified status</returns>
        public async Task<IEnumerable<DefectiveItem>> GetByStatusAsync(DefectiveItemStatus status)
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.Status == status)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .Include(d => d.StatusHistory)
                    .AsNoTracking()
                    .OrderByDescending(d => d.RegistrationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items by status: {Status}", status);
                throw;
            }
        }

        /// <summary>
        /// Gets defective item by defective item number
        /// </summary>
        /// <param name="defectiveItemNumber">Defective item number</param>
        /// <returns>Defective item with the specified number</returns>
        public async Task<DefectiveItem> GetByDefectiveItemNumberAsync(string defectiveItemNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(defectiveItemNumber))
                    return null;

                return await _context.DefectiveItems
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .Include(d => d.StatusHistory)
                        .ThenInclude(h => h.User)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(d => d.DefectiveItemNumber == defectiveItemNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective item by number: {DefectiveItemNumber}", defectiveItemNumber);
                throw;
            }
        }

        /// <summary>
        /// Gets defective items by supplier name
        /// </summary>
        /// <param name="supplierName">Supplier name</param>
        /// <returns>Defective items for the specified supplier</returns>
        public async Task<IEnumerable<DefectiveItem>> GetBySupplierNameAsync(string supplierName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(supplierName))
                    return new List<DefectiveItem>();

                return await _context.DefectiveItems
                    .Where(d => d.SupplierName.ToLower().Contains(supplierName.ToLower()))
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .Include(d => d.StatusHistory)
                    .AsNoTracking()
                    .OrderByDescending(d => d.RegistrationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items by supplier name: {SupplierName}", supplierName);
                throw;
            }
        }

        /// <summary>
        /// Gets pending defective items (not yet sent to supplier)
        /// </summary>
        /// <returns>Defective items that are registered but not sent to supplier</returns>
        public async Task<IEnumerable<DefectiveItem>> GetPendingAsync()
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.Status == DefectiveItemStatus.Registered)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .AsNoTracking()
                    .OrderBy(d => d.RegistrationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending defective items");
                throw;
            }
        }

        /// <summary>
        /// Gets defective items sent to supplier but not yet received back
        /// </summary>
        /// <returns>Defective items currently with supplier</returns>
        public async Task<IEnumerable<DefectiveItem>> GetWithSupplierAsync()
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.Status == DefectiveItemStatus.SentToSupplier)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .AsNoTracking()
                    .OrderBy(d => d.SentToSupplierDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items with supplier");
                throw;
            }
        }

        /// <summary>
        /// Gets defective items ready for customer return
        /// </summary>
        /// <returns>Defective items received from supplier and ready for customer</returns>
        public async Task<IEnumerable<DefectiveItem>> GetReadyForReturnAsync()
        {
            try
            {
                return await _context.DefectiveItems
                    .Where(d => d.Status == DefectiveItemStatus.ReceivedFromSupplier)
                    .Include(d => d.Item)
                    .Include(d => d.BasementManager)
                    .AsNoTracking()
                    .OrderBy(d => d.ReceivedFromSupplierDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items ready for return");
                throw;
            }
        }

        /// <summary>
        /// Gets defective items statistics for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary with status counts</returns>
        public async Task<Dictionary<DefectiveItemStatus, int>> GetStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var statistics = await _context.DefectiveItems
                    .Where(d => d.RegistrationDate >= startDate && d.RegistrationDate <= endDate)
                    .GroupBy(d => d.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                return statistics.ToDictionary(s => s.Status, s => s.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items statistics: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }
    }
}
