﻿#pragma checksum "..\..\..\..\Views\AdminDashboardDirect.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F47106AF750F13E536842379356C769C0BD8F6D2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InventoryManagement.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InventoryManagement.Views {
    
    
    /// <summary>
    /// AdminDashboardDirect
    /// </summary>
    public partial class AdminDashboardDirect : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 29 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameDisplay;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardBtn;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryBtn;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransactionsBtn;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsBtn;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UsersBtn;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardView;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalItemsText;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InventoryValueText;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockText;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayTransactionsText;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RecentActivityGrid;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InventoryView;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid TransactionsView;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ReportsView;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid UsersView;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Views\AdminDashboardDirect.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SettingsView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InventoryManagement;component/views/admindashboarddirect.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserNameDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 38 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DashboardBtn = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            this.DashboardBtn.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InventoryBtn = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            this.InventoryBtn.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TransactionsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            this.TransactionsBtn.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ReportsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 97 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            this.ReportsBtn.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.UsersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            this.UsersBtn.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            this.SettingsBtn.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DashboardView = ((System.Windows.Controls.Grid)(target));
            return;
            case 10:
            this.TotalItemsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.InventoryValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LowStockText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TodayTransactionsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.RecentActivityGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            
            #line 283 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 288 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 293 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 298 "..\..\..\..\Views\AdminDashboardDirect.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.InventoryView = ((System.Windows.Controls.Grid)(target));
            return;
            case 20:
            this.TransactionsView = ((System.Windows.Controls.Grid)(target));
            return;
            case 21:
            this.ReportsView = ((System.Windows.Controls.Grid)(target));
            return;
            case 22:
            this.UsersView = ((System.Windows.Controls.Grid)(target));
            return;
            case 23:
            this.SettingsView = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

