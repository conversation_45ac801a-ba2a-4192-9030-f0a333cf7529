﻿#pragma checksum "..\..\..\..\Views\LocationItemsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "231FCBF50250348066CB5B7CAC656D6442DE4EE7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InventoryManagement.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InventoryManagement.Views {
    
    
    /// <summary>
    /// LocationItemsView
    /// </summary>
    public partial class LocationItemsView : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 25 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackButton;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocationNameText;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalItemsText;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchBox;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BreadcrumbText;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TreeSearchBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearSearchButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox InStockCheckBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LowStockCheckBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OutOfStockCheckBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ActiveItemsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox InactiveItemsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SavedViewsComboBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ViewNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveViewButton;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UserRoleComboBox;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExpandAllCategoriesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExpandAllSubcategoriesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView InventoryTreeView;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryHeaderText;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel ItemsPanel;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Views\LocationItemsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoItemsText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InventoryManagement;component/views/locationitemsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\LocationItemsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BackButton = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Views\LocationItemsView.xaml"
            this.BackButton.Click += new System.Windows.RoutedEventHandler(this.BackButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LocationNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TotalItemsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SearchBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 54 "..\..\..\..\Views\LocationItemsView.xaml"
            this.SearchBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\Views\LocationItemsView.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BreadcrumbText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TreeSearchBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 116 "..\..\..\..\Views\LocationItemsView.xaml"
            this.TreeSearchBox.KeyUp += new System.Windows.Input.KeyEventHandler(this.TreeSearchBox_KeyUp);
            
            #line default
            #line hidden
            
            #line 117 "..\..\..\..\Views\LocationItemsView.xaml"
            this.TreeSearchBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TreeSearchBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ClearSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\..\Views\LocationItemsView.xaml"
            this.ClearSearchButton.Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 139 "..\..\..\..\Views\LocationItemsView.xaml"
            this.CategoryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CategoryFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InStockCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 147 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InStockCheckBox.Checked += new System.Windows.RoutedEventHandler(this.StockFilterCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 148 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InStockCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.StockFilterCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 11:
            this.LowStockCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 154 "..\..\..\..\Views\LocationItemsView.xaml"
            this.LowStockCheckBox.Checked += new System.Windows.RoutedEventHandler(this.StockFilterCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 155 "..\..\..\..\Views\LocationItemsView.xaml"
            this.LowStockCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.StockFilterCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            this.OutOfStockCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 161 "..\..\..\..\Views\LocationItemsView.xaml"
            this.OutOfStockCheckBox.Checked += new System.Windows.RoutedEventHandler(this.StockFilterCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 162 "..\..\..\..\Views\LocationItemsView.xaml"
            this.OutOfStockCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.StockFilterCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ActiveItemsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 170 "..\..\..\..\Views\LocationItemsView.xaml"
            this.ActiveItemsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ItemStatusCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 171 "..\..\..\..\Views\LocationItemsView.xaml"
            this.ActiveItemsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ItemStatusCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 14:
            this.InactiveItemsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 177 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InactiveItemsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ItemStatusCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 178 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InactiveItemsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ItemStatusCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 186 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SavedViewsComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 196 "..\..\..\..\Views\LocationItemsView.xaml"
            this.SavedViewsComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SavedViewsComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ViewNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.SaveViewButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\Views\LocationItemsView.xaml"
            this.SaveViewButton.Click += new System.Windows.RoutedEventHandler(this.SaveViewButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.UserRoleComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 220 "..\..\..\..\Views\LocationItemsView.xaml"
            this.UserRoleComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.UserRoleComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ExpandAllCategoriesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 230 "..\..\..\..\Views\LocationItemsView.xaml"
            this.ExpandAllCategoriesCheckBox.Click += new System.Windows.RoutedEventHandler(this.ExpandAllCategoriesCheckBox_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ExpandAllSubcategoriesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 235 "..\..\..\..\Views\LocationItemsView.xaml"
            this.ExpandAllSubcategoriesCheckBox.Click += new System.Windows.RoutedEventHandler(this.ExpandAllSubcategoriesCheckBox_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.InventoryTreeView = ((System.Windows.Controls.TreeView)(target));
            
            #line 245 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InventoryTreeView.PreviewMouseMove += new System.Windows.Input.MouseEventHandler(this.InventoryTreeView_PreviewMouseMove);
            
            #line default
            #line hidden
            
            #line 246 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InventoryTreeView.PreviewDragOver += new System.Windows.DragEventHandler(this.InventoryTreeView_PreviewDragOver);
            
            #line default
            #line hidden
            
            #line 247 "..\..\..\..\Views\LocationItemsView.xaml"
            this.InventoryTreeView.Drop += new System.Windows.DragEventHandler(this.InventoryTreeView_Drop);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 258 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 259 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.MoveItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 261 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 265 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AddNewItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 266 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.RenameCategoryMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 268 "..\..\..\..\Views\LocationItemsView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCategoryMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.CategoryHeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.ItemsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 32:
            this.NoItemsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            System.Windows.EventSetter eventSetter;
            switch (connectionId)
            {
            case 23:
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.PreviewMouseRightButtonDownEvent;
            
            #line 253 "..\..\..\..\Views\LocationItemsView.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseButtonEventHandler(this.TreeViewItem_PreviewMouseRightButtonDown);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            break;
            }
        }
    }
}

