<UserControl x:Class="InventoryManagement.Views.DashboardOverviewView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Business Dashboard Overview" 
                   FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- Quick Stats -->
        <UniformGrid Grid.Row="1" Rows="1" Columns="4" Margin="0,0,0,20">
            
            <materialDesign:Card Margin="5" Padding="15">
                <StackPanel>
                    <materialDesign:PackIcon Kind="Package" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" HorizontalAlignment="Center"/>
                    <TextBlock Text="1,234" FontSize="20" FontWeight="Bold" TextAlignment="Center" Margin="0,10,0,5"/>
                    <TextBlock Text="Total Items" FontSize="12" TextAlignment="Center" Opacity="0.7"/>
                </StackPanel>
            </materialDesign:Card>

            <materialDesign:Card Margin="5" Padding="15">
                <StackPanel>
                    <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32" 
                                           Foreground="Orange" HorizontalAlignment="Center"/>
                    <TextBlock Text="23" FontSize="20" FontWeight="Bold" TextAlignment="Center" Margin="0,10,0,5"/>
                    <TextBlock Text="Low Stock" FontSize="12" TextAlignment="Center" Opacity="0.7"/>
                </StackPanel>
            </materialDesign:Card>

            <materialDesign:Card Margin="5" Padding="15">
                <StackPanel>
                    <materialDesign:PackIcon Kind="CashMultiple" Width="32" Height="32" 
                                           Foreground="Green" HorizontalAlignment="Center"/>
                    <TextBlock Text="$12,345" FontSize="20" FontWeight="Bold" TextAlignment="Center" Margin="0,10,0,5"/>
                    <TextBlock Text="Today's Sales" FontSize="12" TextAlignment="Center" Opacity="0.7"/>
                </StackPanel>
            </materialDesign:Card>

            <materialDesign:Card Margin="5" Padding="15">
                <StackPanel>
                    <materialDesign:PackIcon Kind="AccountGroup" Width="32" Height="32" 
                                           Foreground="Blue" HorizontalAlignment="Center"/>
                    <TextBlock Text="567" FontSize="20" FontWeight="Bold" TextAlignment="Center" Margin="0,10,0,5"/>
                    <TextBlock Text="Customers" FontSize="12" TextAlignment="Center" Opacity="0.7"/>
                </StackPanel>
            </materialDesign:Card>
        </UniformGrid>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Charts and Analytics -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="20">
                <StackPanel>
                    <TextBlock Text="Sales Analytics" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                    <TextBlock Text="Sales chart and analytics will be displayed here" 
                               TextAlignment="Center" Margin="0,50" Opacity="0.7"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Recent Activity -->
            <materialDesign:Card Grid.Column="1" Margin="10,0,0,0" Padding="20">
                <StackPanel>
                    <TextBlock Text="Recent Activity" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <StackPanel>
                        <Border BorderBrush="LightGray" BorderThickness="0,0,0,1" Padding="0,5">
                            <TextBlock Text="New sale: $123.45" FontSize="12"/>
                        </Border>
                        <Border BorderBrush="LightGray" BorderThickness="0,0,0,1" Padding="0,5">
                            <TextBlock Text="Stock updated: Widget A" FontSize="12"/>
                        </Border>
                        <Border BorderBrush="LightGray" BorderThickness="0,0,0,1" Padding="0,5">
                            <TextBlock Text="New customer added" FontSize="12"/>
                        </Border>
                        <Border BorderBrush="LightGray" BorderThickness="0,0,0,1" Padding="0,5">
                            <TextBlock Text="Backup completed" FontSize="12"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
