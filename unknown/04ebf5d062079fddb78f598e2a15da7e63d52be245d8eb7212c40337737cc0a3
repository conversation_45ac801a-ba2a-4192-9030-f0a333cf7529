using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Repository implementation for Transaction operations
    /// </summary>
    public class TransactionRepository : Repository<Transaction>, ITransactionRepository
    {
        private readonly ILogger<TransactionRepository> _logger;

        public TransactionRepository(ApplicationDbContext context, ILogger<TransactionRepository> logger) 
            : base(context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets transactions by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Transactions created by the specified user</returns>
        public async Task<IEnumerable<Transaction>> GetByUserIdAsync(int userId)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.CashierId == userId)
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by user ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Gets transactions by item ID
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Transactions for the specified item</returns>
        public async Task<IEnumerable<Transaction>> GetByItemIdAsync(int itemId)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.Items.Any(i => i.ItemId == itemId))
                    .Include(t => t.Items)
                    .Include(t => t.User)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by item ID: {ItemId}", itemId);
                throw;
            }
        }

        /// <summary>
        /// Gets transactions for a specific date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Transactions in the specified date range</returns>
        public async Task<IEnumerable<Transaction>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.TransactionDate >= startDate && t.TransactionDate <= endDate)
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .Include(t => t.User)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by date range: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets transactions by type
        /// </summary>
        /// <param name="type">Transaction type</param>
        /// <returns>Transactions of the specified type</returns>
        public async Task<IEnumerable<Transaction>> GetByTypeAsync(TransactionType type)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.Type == type)
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .Include(t => t.User)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by type: {Type}", type);
                throw;
            }
        }

        /// <summary>
        /// Gets transactions by customer ID
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>Transactions for the specified customer</returns>
        public async Task<IEnumerable<Transaction>> GetByCustomerIdAsync(int customerId)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.CustomerId == customerId)
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .Include(t => t.Customer)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by customer ID: {CustomerId}", customerId);
                throw;
            }
        }

        /// <summary>
        /// Gets today's transactions
        /// </summary>
        /// <returns>Transactions for today</returns>
        public async Task<IEnumerable<Transaction>> GetTodaysTransactionsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                return await _context.Transactions
                    .Where(t => t.TransactionDate >= today && t.TransactionDate < tomorrow)
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .Include(t => t.User)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's transactions");
                throw;
            }
        }

        /// <summary>
        /// Gets sales transactions (excluding returns and adjustments)
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Sales transactions in the specified date range</returns>
        public async Task<IEnumerable<Transaction>> GetSalesTransactionsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.Type == TransactionType.Sale && 
                               t.TransactionDate >= startDate && 
                               t.TransactionDate <= endDate)
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .Include(t => t.User)
                    .AsNoTracking()
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales transactions: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets total sales amount for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Total sales amount</returns>
        public async Task<decimal> GetTotalSalesAmountAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.Type == TransactionType.Sale && 
                               t.TransactionDate >= startDate && 
                               t.TransactionDate <= endDate)
                    .SumAsync(t => t.Total);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total sales amount: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets transaction count for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Transaction count</returns>
        public async Task<int> GetTransactionCountAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.Transactions
                    .Where(t => t.TransactionDate >= startDate && t.TransactionDate <= endDate)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction count: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets transaction by reference number
        /// </summary>
        /// <param name="referenceNumber">Transaction reference number</param>
        /// <returns>Transaction with the specified reference number</returns>
        public async Task<Transaction> GetByReferenceNumberAsync(string referenceNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(referenceNumber))
                    return null;

                return await _context.Transactions
                    .Include(t => t.Items)
                    .Include(t => t.Payments)
                    .Include(t => t.User)
                    .Include(t => t.Customer)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(t => t.ReferenceNumber == referenceNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction by reference number: {ReferenceNumber}", referenceNumber);
                throw;
            }
        }
    }
}
