using System;
using System.Collections.Generic;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Result of an inventory transfer operation
    /// </summary>
    public class InventoryTransferResult
    {
        /// <summary>
        /// Whether the transfer was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transfer ID
        /// </summary>
        public int TransferId { get; set; }

        /// <summary>
        /// Source item ID
        /// </summary>
        public int SourceItemId { get; set; }

        /// <summary>
        /// Destination item ID
        /// </summary>
        public int DestinationItemId { get; set; }

        /// <summary>
        /// Quantity transferred
        /// </summary>
        public int QuantityTransferred { get; set; }

        /// <summary>
        /// Transfer timestamp
        /// </summary>
        public DateTime TransferTime { get; set; }

        /// <summary>
        /// Transfer reason
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Error message if transfer failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// User who performed the transfer
        /// </summary>
        public int UserId { get; set; }
    }

    /// <summary>
    /// Inventory movement record
    /// </summary>
    public class InventoryMovement
    {
        /// <summary>
        /// Movement ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// Movement type (In, Out, Transfer, Adjustment)
        /// </summary>
        public string MovementType { get; set; }

        /// <summary>
        /// Quantity moved (positive for in, negative for out)
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Previous quantity before movement
        /// </summary>
        public int PreviousQuantity { get; set; }

        /// <summary>
        /// New quantity after movement
        /// </summary>
        public int NewQuantity { get; set; }

        /// <summary>
        /// Movement timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Reason for movement
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Reference ID (transaction ID, transfer ID, etc.)
        /// </summary>
        public int? ReferenceId { get; set; }

        /// <summary>
        /// Reference type (Sale, Purchase, Transfer, Adjustment)
        /// </summary>
        public string ReferenceType { get; set; }

        /// <summary>
        /// User who performed the movement
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Additional notes
        /// </summary>
        public string Notes { get; set; }

        // Navigation properties
        public Item Item { get; set; }
        public User User { get; set; }
    }

    /// <summary>
    /// Result of an inventory adjustment operation
    /// </summary>
    public class InventoryAdjustmentResult
    {
        /// <summary>
        /// Whether the adjustment was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Adjustment ID
        /// </summary>
        public int AdjustmentId { get; set; }

        /// <summary>
        /// Item ID that was adjusted
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// Previous quantity
        /// </summary>
        public int PreviousQuantity { get; set; }

        /// <summary>
        /// New quantity
        /// </summary>
        public int NewQuantity { get; set; }

        /// <summary>
        /// Quantity difference
        /// </summary>
        public int QuantityDifference { get; set; }

        /// <summary>
        /// Adjustment timestamp
        /// </summary>
        public DateTime AdjustmentTime { get; set; }

        /// <summary>
        /// Reason for adjustment
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Error message if adjustment failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// User who performed the adjustment
        /// </summary>
        public int UserId { get; set; }
    }

    /// <summary>
    /// Daily sales summary
    /// </summary>
    public class DailySales
    {
        /// <summary>
        /// Date of sales
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Total sales amount
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// Number of transactions
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// Number of items sold
        /// </summary>
        public int ItemsSold { get; set; }

        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }

        /// <summary>
        /// Total cost of goods sold
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// Gross profit
        /// </summary>
        public decimal GrossProfit { get; set; }

        /// <summary>
        /// Gross profit margin percentage
        /// </summary>
        public decimal GrossProfitMargin { get; set; }

        /// <summary>
        /// Number of unique customers
        /// </summary>
        public int UniqueCustomers { get; set; }

        /// <summary>
        /// Top selling item of the day
        /// </summary>
        public string TopSellingItem { get; set; }

        /// <summary>
        /// Quantity of top selling item
        /// </summary>
        public int TopSellingItemQuantity { get; set; }
    }

    /// <summary>
    /// Top selling item information
    /// </summary>
    public class TopSellingItem
    {
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// Item code
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// Item name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Total quantity sold
        /// </summary>
        public int QuantitySold { get; set; }

        /// <summary>
        /// Total sales value
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// Number of transactions
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// Average selling price
        /// </summary>
        public decimal AveragePrice { get; set; }

        /// <summary>
        /// Category
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Rank in top selling items
        /// </summary>
        public int Rank { get; set; }
    }

    /// <summary>
    /// Sales performance metrics
    /// </summary>
    public class SalesPerformance
    {
        /// <summary>
        /// Period start date
        /// </summary>
        public DateTime PeriodStart { get; set; }

        /// <summary>
        /// Period end date
        /// </summary>
        public DateTime PeriodEnd { get; set; }

        /// <summary>
        /// Total sales amount
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// Total transactions
        /// </summary>
        public int TotalTransactions { get; set; }

        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }

        /// <summary>
        /// Sales growth percentage compared to previous period
        /// </summary>
        public decimal SalesGrowth { get; set; }

        /// <summary>
        /// Transaction growth percentage compared to previous period
        /// </summary>
        public decimal TransactionGrowth { get; set; }

        /// <summary>
        /// Best performing day
        /// </summary>
        public DateTime BestDay { get; set; }

        /// <summary>
        /// Best day sales amount
        /// </summary>
        public decimal BestDaySales { get; set; }

        /// <summary>
        /// Worst performing day
        /// </summary>
        public DateTime WorstDay { get; set; }

        /// <summary>
        /// Worst day sales amount
        /// </summary>
        public decimal WorstDaySales { get; set; }

        /// <summary>
        /// Top selling items
        /// </summary>
        public List<TopSellingItem> TopSellingItems { get; set; } = new();
    }

    /// <summary>
    /// Top customer information
    /// </summary>
    public class TopCustomer
    {
        /// <summary>
        /// Customer ID
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Total purchases amount
        /// </summary>
        public decimal TotalPurchases { get; set; }

        /// <summary>
        /// Number of transactions
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }

        /// <summary>
        /// Last purchase date
        /// </summary>
        public DateTime LastPurchaseDate { get; set; }

        /// <summary>
        /// Customer rank
        /// </summary>
        public int Rank { get; set; }

        /// <summary>
        /// Customer loyalty level
        /// </summary>
        public string LoyaltyLevel { get; set; }
    }
}
