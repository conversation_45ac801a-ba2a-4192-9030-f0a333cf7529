using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents individual items sold in a sales transaction
    /// </summary>
    public class SalesDetail
    {
        /// <summary>
        /// Unique identifier for the sales detail
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// ID of the parent sales transaction
        /// </summary>
        [Required]
        public int SalesTransactionId { get; set; }

        /// <summary>
        /// Reference to the parent sales transaction
        /// </summary>
        [ForeignKey("SalesTransactionId")]
        public virtual SalesTransaction SalesTransaction { get; set; }

        /// <summary>
        /// ID of the item sold
        /// </summary>
        [Required]
        public int ItemId { get; set; }

        /// <summary>
        /// Item name at the time of sale
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; }

        /// <summary>
        /// Item SKU at the time of sale
        /// </summary>
        [StringLength(50)]
        public string ItemSKU { get; set; }

        /// <summary>
        /// Category of the item
        /// </summary>
        [StringLength(100)]
        public string CategoryName { get; set; }

        /// <summary>
        /// Quantity sold
        /// </summary>
        [Required]
        public int Quantity { get; set; }

        /// <summary>
        /// Unit price at the time of sale
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Cost price of the item
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; }

        /// <summary>
        /// Discount applied to this line item
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Discount percentage applied
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; }

        /// <summary>
        /// Tax amount for this line item
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Total amount for this line item (Quantity * UnitPrice - Discount + Tax)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Profit for this line item (TotalAmount - (Quantity * CostPrice))
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Profit { get; set; }

        /// <summary>
        /// Location where the item was sold from
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// Location name
        /// </summary>
        [StringLength(100)]
        public string LocationName { get; set; }

        /// <summary>
        /// Serial number or batch number if applicable
        /// </summary>
        [StringLength(100)]
        public string SerialNumber { get; set; }

        /// <summary>
        /// Expiry date of the item if applicable
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Notes specific to this line item
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Whether this item was returned
        /// </summary>
        public bool IsReturned { get; set; }

        /// <summary>
        /// Date when the item was returned
        /// </summary>
        public DateTime? ReturnedAt { get; set; }

        /// <summary>
        /// Reason for return
        /// </summary>
        [StringLength(200)]
        public string ReturnReason { get; set; }

        /// <summary>
        /// Date when the record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when the record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
