using InventoryManagement.Commands;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.Views;
using Microsoft.Extensions.Logging;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the comprehensive main dashboard with full business functionality
    /// Provides complete access to all system features and modules
    /// </summary>
    public class ComprehensiveMainDashboardViewModel : INotifyPropertyChanged
    {
        private readonly ILogger<ComprehensiveMainDashboardViewModel> _logger;
        private readonly IInventoryService _inventoryService;
        private readonly ICustomerService _customerService;
        private readonly ISalesService _salesService;
        private readonly IUserFriendlyMessageService _messageService;
        
        // Current view management
        private object _currentView;
        private string _currentViewTitle;
        private DateTime _currentDateTime;
        private User _currentUser;
        private string _statusMessage;
        private int _activeUsersCount;

        // Navigation backgrounds for active state
        private Brush _dashboardBackground;
        private Brush _posBackground;
        private Brush _inventoryBackground;
        private Brush _customersBackground;
        private Brush _reportsBackground;
        private Brush _financialBackground;

        public ComprehensiveMainDashboardViewModel(
            ILogger<ComprehensiveMainDashboardViewModel> logger,
            IInventoryService inventoryService,
            ICustomerService customerService,
            ISalesService salesService,
            IUserFriendlyMessageService messageService)
        {
            _logger = logger;
            _inventoryService = inventoryService;
            _customerService = customerService;
            _salesService = salesService;
            _messageService = messageService;
            
            InitializeCommands();
            InitializeView();
            StartTimerUpdates();
        }

        #region Properties

        public object CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        public string CurrentViewTitle
        {
            get => _currentViewTitle;
            set => SetProperty(ref _currentViewTitle, value);
        }

        public DateTime CurrentDateTime
        {
            get => _currentDateTime;
            set => SetProperty(ref _currentDateTime, value);
        }

        public User CurrentUser
        {
            get => _currentUser ?? App.CurrentUser;
            set => SetProperty(ref _currentUser, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public int ActiveUsersCount
        {
            get => _activeUsersCount;
            set => SetProperty(ref _activeUsersCount, value);
        }

        // Navigation background properties
        public Brush DashboardBackground
        {
            get => _dashboardBackground;
            set => SetProperty(ref _dashboardBackground, value);
        }

        public Brush POSBackground
        {
            get => _posBackground;
            set => SetProperty(ref _posBackground, value);
        }

        public Brush InventoryBackground
        {
            get => _inventoryBackground;
            set => SetProperty(ref _inventoryBackground, value);
        }

        public Brush CustomersBackground
        {
            get => _customersBackground;
            set => SetProperty(ref _customersBackground, value);
        }

        public Brush ReportsBackground
        {
            get => _reportsBackground;
            set => SetProperty(ref _reportsBackground, value);
        }

        public Brush FinancialBackground
        {
            get => _financialBackground;
            set => SetProperty(ref _financialBackground, value);
        }

        #endregion

        #region Commands

        // Main Navigation Commands
        public ICommand ShowDashboardCommand { get; private set; }
        public ICommand ShowPOSCommand { get; private set; }
        public ICommand ShowInventoryCommand { get; private set; }
        public ICommand ShowCustomersCommand { get; private set; }
        public ICommand ShowReportsCommand { get; private set; }
        public ICommand ShowFinancialCommand { get; private set; }

        // Inventory Operations Commands
        public ICommand ShowStockManagementCommand { get; private set; }
        public ICommand ShowPurchaseOrdersCommand { get; private set; }
        public ICommand ShowSuppliersCommand { get; private set; }

        // System Management Commands
        public ICommand ShowUsersCommand { get; private set; }
        public ICommand ShowSettingsCommand { get; private set; }
        public ICommand ShowBackupCommand { get; private set; }

        // Hardware Commands
        public ICommand ShowHardwareCommand { get; private set; }
        public ICommand ShowBarcodeCommand { get; private set; }

        // Utility Commands
        public ICommand ShowHelpCommand { get; private set; }
        public ICommand LogoutCommand { get; private set; }

        #endregion

        #region Command Initialization

        private void InitializeCommands()
        {
            // Main Navigation
            ShowDashboardCommand = new RelayCommand(ExecuteShowDashboard);
            ShowPOSCommand = new RelayCommand(ExecuteShowPOS);
            ShowInventoryCommand = new RelayCommand(ExecuteShowInventory);
            ShowCustomersCommand = new RelayCommand(ExecuteShowCustomers);
            ShowReportsCommand = new RelayCommand(ExecuteShowReports);
            ShowFinancialCommand = new RelayCommand(ExecuteShowFinancial);

            // Inventory Operations
            ShowStockManagementCommand = new RelayCommand(ExecuteShowStockManagement);
            ShowPurchaseOrdersCommand = new RelayCommand(ExecuteShowPurchaseOrders);
            ShowSuppliersCommand = new RelayCommand(ExecuteShowSuppliers);

            // System Management
            ShowUsersCommand = new RelayCommand(ExecuteShowUsers);
            ShowSettingsCommand = new RelayCommand(ExecuteShowSettings);
            ShowBackupCommand = new RelayCommand(ExecuteShowBackup);

            // Hardware
            ShowHardwareCommand = new RelayCommand(ExecuteShowHardware);
            ShowBarcodeCommand = new RelayCommand(ExecuteShowBarcode);

            // Utility
            ShowHelpCommand = new RelayCommand(ExecuteShowHelp);
            LogoutCommand = new RelayCommand(ExecuteLogout);
        }

        #endregion

        #region Command Implementations

        private void ExecuteShowDashboard()
        {
            try
            {
                _logger.LogInformation("Showing Dashboard view");
                SetActiveNavigation("Dashboard");
                CurrentView = new DashboardOverviewView();
                CurrentViewTitle = "Business Dashboard";
                StatusMessage = "Dashboard loaded successfully";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing dashboard");
                _messageService.ShowError(ex.Message, "Unable to load dashboard. Please try again.");
            }
        }

        private void ExecuteShowPOS()
        {
            try
            {
                _logger.LogInformation("Opening Point of Sale");
                SetActiveNavigation("POS");
                CurrentView = new PointOfSaleView();
                CurrentViewTitle = "Point of Sale";
                StatusMessage = "POS system ready for transactions";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening POS");
                _messageService.ShowError(ex.Message, "Unable to open Point of Sale. Please try again.");
            }
        }

        private void ExecuteShowInventory()
        {
            try
            {
                _logger.LogInformation("Opening Inventory Management");
                SetActiveNavigation("Inventory");
                CurrentView = new InventoryDashboardView();
                CurrentViewTitle = "Inventory Management";
                StatusMessage = "Inventory management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening inventory");
                _messageService.ShowError(ex.Message, "Unable to open Inventory Management. Please try again.");
            }
        }

        private void ExecuteShowCustomers()
        {
            try
            {
                _logger.LogInformation("Opening Customer Management");
                SetActiveNavigation("Customers");
                CurrentView = new CustomerManagementView();
                CurrentViewTitle = "Customer Management";
                StatusMessage = "Customer management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening customer management");
                _messageService.ShowError(ex.Message, "Unable to open Customer Management. Please try again.");
            }
        }

        private void ExecuteShowReports()
        {
            try
            {
                _logger.LogInformation("Opening Reports & Analytics");
                SetActiveNavigation("Reports");
                CurrentView = new Views.Reports.ReportsDashboardView();
                CurrentViewTitle = "Reports & Analytics";
                StatusMessage = "Reports and analytics loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening reports");
                _messageService.ShowError(ex.Message, "Unable to open Reports & Analytics. Please try again.");
            }
        }

        private void ExecuteShowFinancial()
        {
            try
            {
                _logger.LogInformation("Opening Financial Management");
                SetActiveNavigation("Financial");
                CurrentView = new Views.Reports.ReportsDashboardView();
                CurrentViewTitle = "Financial Management";
                StatusMessage = "Financial management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening financial management");
                _messageService.ShowError(ex.Message, "Unable to open Financial Management. Please try again.");
            }
        }

        private void ExecuteShowStockManagement()
        {
            try
            {
                _logger.LogInformation("Opening Stock Management");
                CurrentView = new InventoryDashboardView();
                CurrentViewTitle = "Stock Management";
                StatusMessage = "Stock management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening stock management");
                _messageService.ShowError(ex.Message, "Unable to open Stock Management. Please try again.");
            }
        }

        private void ExecuteShowPurchaseOrders()
        {
            try
            {
                _logger.LogInformation("Opening Purchase Orders");
                CurrentView = new InventoryDashboardView();
                CurrentViewTitle = "Purchase Orders";
                StatusMessage = "Purchase orders system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening purchase orders");
                _messageService.ShowError(ex.Message, "Unable to open Purchase Orders. Please try again.");
            }
        }

        private void ExecuteShowSuppliers()
        {
            try
            {
                _logger.LogInformation("Opening Supplier Management");
                CurrentView = new CustomerManagementView();
                CurrentViewTitle = "Supplier Management";
                StatusMessage = "Supplier management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening supplier management");
                _messageService.ShowError(ex.Message, "Unable to open Supplier Management. Please try again.");
            }
        }

        private void ExecuteShowUsers()
        {
            try
            {
                _logger.LogInformation("Opening User Management");
                CurrentView = new AdminDashboardView();
                CurrentViewTitle = "User Management";
                StatusMessage = "User management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening user management");
                _messageService.ShowError(ex.Message, "Unable to open User Management. Please try again.");
            }
        }

        private void ExecuteShowSettings()
        {
            try
            {
                _logger.LogInformation("Opening System Settings");
                CurrentView = new Views.Settings.HardwareSettingsView();
                CurrentViewTitle = "System Settings";
                StatusMessage = "System settings loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening settings");
                _messageService.ShowError(ex.Message, "Unable to open System Settings. Please try again.");
            }
        }

        private void ExecuteShowBackup()
        {
            try
            {
                _logger.LogInformation("Opening Backup & Restore");
                CurrentView = new Views.Settings.HardwareSettingsView();
                CurrentViewTitle = "Backup & Restore";
                StatusMessage = "Backup and restore system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening backup");
                _messageService.ShowError(ex.Message, "Unable to open Backup & Restore. Please try again.");
            }
        }

        private void ExecuteShowHardware()
        {
            try
            {
                _logger.LogInformation("Opening Hardware Setup");
                CurrentView = new Views.Settings.HardwareSettingsView();
                CurrentViewTitle = "Hardware Setup";
                StatusMessage = "Hardware configuration loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening hardware setup");
                _messageService.ShowError(ex.Message, "Unable to open Hardware Setup. Please try again.");
            }
        }

        private void ExecuteShowBarcode()
        {
            try
            {
                _logger.LogInformation("Opening Barcode Management");
                CurrentView = new Views.Settings.HardwareSettingsView();
                CurrentViewTitle = "Barcode Management";
                StatusMessage = "Barcode management system loaded";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening barcode management");
                _messageService.ShowError(ex.Message, "Unable to open Barcode Management. Please try again.");
            }
        }

        private void ExecuteShowHelp()
        {
            try
            {
                _logger.LogInformation("Opening Help & Support");
                var helpDialog = new Views.Dialogs.HelpViewerDialog();
                helpDialog.ShowDialog();
                StatusMessage = "Help system opened";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening help");
                _messageService.ShowError(ex.Message, "Unable to open Help & Support. Please try again.");
            }
        }

        private void ExecuteLogout()
        {
            try
            {
                _logger.LogInformation("User logging out");
                
                var result = MessageBox.Show(
                    "Are you sure you want to logout?", 
                    "Confirm Logout", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // Clear current user
                    App.CurrentUser = null;
                    
                    // Close current window and show login
                    var loginWindow = new LoginWindow();
                    loginWindow.Show();
                    
                    // Close current dashboard
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window is ComprehensiveMainDashboard)
                        {
                            window.Close();
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                _messageService.ShowError(ex.Message, "Error during logout. Please try again.");
            }
        }

        #endregion

        #region Helper Methods

        private void InitializeView()
        {
            CurrentViewTitle = "Business Dashboard";
            StatusMessage = "System ready for operations";
            ActiveUsersCount = 1;
            
            // Load dashboard by default
            ExecuteShowDashboard();
        }

        private void StartTimerUpdates()
        {
            CurrentDateTime = DateTime.Now;
            
            // Update time every minute
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMinutes(1);
            timer.Tick += (s, e) => CurrentDateTime = DateTime.Now;
            timer.Start();
        }

        private void SetActiveNavigation(string activeSection)
        {
            // Reset all backgrounds
            var defaultBrush = Brushes.Transparent;
            var activeBrush = new SolidColorBrush(Color.FromArgb(50, 33, 150, 243)); // Light blue

            DashboardBackground = defaultBrush;
            POSBackground = defaultBrush;
            InventoryBackground = defaultBrush;
            CustomersBackground = defaultBrush;
            ReportsBackground = defaultBrush;
            FinancialBackground = defaultBrush;

            // Set active background
            switch (activeSection)
            {
                case "Dashboard":
                    DashboardBackground = activeBrush;
                    break;
                case "POS":
                    POSBackground = activeBrush;
                    break;
                case "Inventory":
                    InventoryBackground = activeBrush;
                    break;
                case "Customers":
                    CustomersBackground = activeBrush;
                    break;
                case "Reports":
                    ReportsBackground = activeBrush;
                    break;
                case "Financial":
                    FinancialBackground = activeBrush;
                    break;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
