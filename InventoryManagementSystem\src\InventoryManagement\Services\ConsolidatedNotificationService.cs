using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Infrastructure.Notifications;
using InventoryManagement.Events;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Consolidated notification service that merges functionality from multiple notification services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - NotificationService
    /// - EnhancedNotificationService
    /// - DesktopNotificationService
    /// - DesktopPushNotificationService
    /// - OfflineNotificationService
    /// </summary>
    public class ConsolidatedNotificationService : INotificationService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<ConsolidatedNotificationService> _logger;
        private readonly EventBus _eventBus;
        private readonly List<Notification> _inMemoryNotifications = new List<Notification>();

        // Events required by INotificationService
        public event EventHandler<NotificationEventArgs> NotificationCreated;
        public event EventHandler<NotificationEventArgs> NotificationRead;
        public event EventHandler<NotificationEventArgs> NotificationDismissed;

        public ConsolidatedNotificationService(
            ApplicationDbContext dbContext,
            ILogger<ConsolidatedNotificationService> logger,
            EventBus eventBus)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _eventBus = eventBus ?? throw new ArgumentNullException(nameof(eventBus));
        }

        #region Core Notification Methods

        /// <summary>
        /// Sends a notification to a specific user
        /// </summary>
        /// <param name="userId">ID of the user to notify</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SendNotificationAsync(int userId, string title, string message, 
            NotificationType type = NotificationType.Information, NotificationPriority priority = NotificationPriority.Normal)
        {
            try
            {
                _logger.LogInformation("Sending notification to user {UserId}: {Title}", userId, title);

                var notification = new Notification
                {
                    UserId = userId,
                    Title = title,
                    Message = message,
                    Type = type,
                    Priority = priority,
                    CreatedAt = DateTime.Now,
                    IsRead = false
                };

                // Save to database for persistence
                await SaveNotificationToDatabaseAsync(notification);

                // Add to in-memory collection for immediate access
                _inMemoryNotifications.Add(notification);

                // Raise event for real-time UI updates
                NotificationCreated?.Invoke(this, new NotificationEventArgs(notification));

                // Publish to event bus for cross-component communication
                await _eventBus.PublishAsync(new NotificationCreatedEvent(notification));

                _logger.LogInformation("Notification sent successfully to user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to user {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Sends a notification to all users with a specific role
        /// </summary>
        /// <param name="role">Role to notify</param>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SendNotificationToRoleAsync(string role, string title, string message, 
            NotificationType type, NotificationPriority priority)
        {
            try
            {
                _logger.LogInformation("Sending notification to role {Role}: {Title}", role, title);

                var users = await _dbContext.Users
                    .Where(u => u.Role.ToString() == role && u.IsActive)
                    .ToListAsync();

                var tasks = users.Select(user => 
                    SendNotificationAsync(user.Id, title, message, type, priority));

                await Task.WhenAll(tasks);

                _logger.LogInformation("Notification sent to {Count} users with role {Role}", users.Count, role);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to role {Role}", role);
                throw;
            }
        }

        /// <summary>
        /// Sends a broadcast notification to all active users
        /// </summary>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SendBroadcastNotificationAsync(string title, string message, 
            NotificationType type, NotificationPriority priority)
        {
            try
            {
                _logger.LogInformation("Sending broadcast notification: {Title}", title);

                var users = await _dbContext.Users
                    .Where(u => u.IsActive)
                    .ToListAsync();

                var tasks = users.Select(user => 
                    SendNotificationAsync(user.Id, title, message, type, priority));

                await Task.WhenAll(tasks);

                _logger.LogInformation("Broadcast notification sent to {Count} users", users.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending broadcast notification");
                throw;
            }
        }

        #endregion

        #region Desktop/UI Notification Methods

        /// <summary>
        /// Shows a notification in the UI (for immediate user feedback)
        /// </summary>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="type">Type of notification</param>
        public void ShowNotification(string title, string message, NotificationType type)
        {
            try
            {
                _logger.LogInformation("Showing UI notification: {Title}", title);

                // Create notification for UI display
                var notification = new Notification
                {
                    Title = title,
                    Message = message,
                    Type = type,
                    Priority = NotificationPriority.Normal,
                    CreatedAt = DateTime.Now,
                    IsRead = false
                };

                // Publish to event bus for immediate UI display
                _eventBus.PublishAsync(new NotificationDisplayEvent(notification));

                _logger.LogDebug("UI notification displayed: {Title}", title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing UI notification");
                // Don't throw here as this is for UI feedback
            }
        }

        /// <summary>
        /// Shows a notification with custom priority
        /// </summary>
        /// <param name="title">Notification title</param>
        /// <param name="message">Notification message</param>
        /// <param name="priority">Priority level</param>
        public async Task SendNotificationAsync(string title, string message, NotificationPriority priority = NotificationPriority.Normal)
        {
            var notificationType = priority switch
            {
                NotificationPriority.Critical => NotificationType.Error,
                NotificationPriority.High => NotificationType.Warning,
                _ => NotificationType.Information
            };

            // Show immediate UI notification
            ShowNotification(title, message, notificationType);

            // If there's a current user, also save to database
            var currentUser = App.CurrentUser;
            if (currentUser != null)
            {
                await SendNotificationAsync(currentUser.Id, title, message, notificationType, priority);
            }
        }

        #endregion

        #region Notification Management Methods

        /// <summary>
        /// Gets notifications for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="includeRead">Whether to include read notifications</param>
        /// <returns>List of notifications</returns>
        public async Task<List<Notification>> GetNotificationsAsync(int userId, bool includeRead = true)
        {
            try
            {
                var query = _dbContext.Notifications.Where(n => n.UserId == userId);

                if (!includeRead)
                {
                    query = query.Where(n => !n.IsRead);
                }

                var notifications = await query
                    .OrderByDescending(n => n.CreatedAt)
                    .ToListAsync();

                return notifications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications for user {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Marks a notification as read
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task MarkAsReadAsync(int notificationId)
        {
            try
            {
                var notification = await _dbContext.Notifications.FindAsync(notificationId);
                if (notification != null && !notification.IsRead)
                {
                    notification.IsRead = true;
                    notification.ReadAt = DateTime.Now;
                    await _dbContext.SaveChangesAsync();

                    // Raise event
                    NotificationRead?.Invoke(this, new NotificationEventArgs(notification));

                    _logger.LogDebug("Notification {NotificationId} marked as read", notificationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {NotificationId} as read", notificationId);
                throw;
            }
        }

        /// <summary>
        /// Dismisses a notification
        /// </summary>
        /// <param name="notificationId">Notification ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task DismissNotificationAsync(int notificationId)
        {
            try
            {
                var notification = await _dbContext.Notifications.FindAsync(notificationId);
                if (notification != null)
                {
                    notification.IsDismissed = true;
                    notification.DismissedAt = DateTime.Now;
                    await _dbContext.SaveChangesAsync();

                    // Raise event
                    NotificationDismissed?.Invoke(this, new NotificationEventArgs(notification));

                    _logger.LogDebug("Notification {NotificationId} dismissed", notificationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error dismissing notification {NotificationId}", notificationId);
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task SaveNotificationToDatabaseAsync(Notification notification)
        {
            try
            {
                _dbContext.Notifications.Add(notification);
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving notification to database");
                // Don't throw here - notification can still work in-memory
            }
        }

        #endregion
    }
}
