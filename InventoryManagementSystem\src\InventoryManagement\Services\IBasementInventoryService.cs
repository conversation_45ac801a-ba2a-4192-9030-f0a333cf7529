using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for basement inventory management services
    /// </summary>
    public interface IBasementInventoryService
    {
        /// <summary>
        /// Get all basement inventory items
        /// </summary>
        /// <returns>List of basement items</returns>
        Task<List<BasementItem>> GetAllBasementItemsAsync();

        /// <summary>
        /// Get basement item by ID
        /// </summary>
        /// <param name="id">Item ID</param>
        /// <returns>Basement item</returns>
        Task<BasementItem> GetBasementItemByIdAsync(int id);

        /// <summary>
        /// Get basement items by main item ID
        /// </summary>
        /// <param name="mainItemId">Main item ID</param>
        /// <returns>List of basement items</returns>
        Task<List<BasementItem>> GetBasementItemsByMainItemIdAsync(int mainItemId);

        /// <summary>
        /// Add item to basement inventory
        /// </summary>
        /// <param name="item">Basement item to add</param>
        /// <returns>Added basement item</returns>
        Task<BasementItem> AddBasementItemAsync(BasementItem item);

        /// <summary>
        /// Update basement item
        /// </summary>
        /// <param name="item">Basement item to update</param>
        /// <returns>Updated basement item</returns>
        Task<BasementItem> UpdateBasementItemAsync(BasementItem item);

        /// <summary>
        /// Remove item from basement inventory
        /// </summary>
        /// <param name="id">Item ID</param>
        /// <returns>True if removed successfully</returns>
        Task<bool> RemoveBasementItemAsync(int id);

        /// <summary>
        /// Transfer item from main inventory to basement
        /// </summary>
        /// <param name="mainItemId">Main item ID</param>
        /// <param name="quantity">Quantity to transfer</param>
        /// <param name="reason">Transfer reason</param>
        /// <returns>Transfer result</returns>
        Task<InventoryTransferResult> TransferToBasementAsync(int mainItemId, int quantity, string reason);

        /// <summary>
        /// Transfer item from basement to main inventory
        /// </summary>
        /// <param name="basementItemId">Basement item ID</param>
        /// <param name="quantity">Quantity to transfer</param>
        /// <param name="reason">Transfer reason</param>
        /// <returns>Transfer result</returns>
        Task<InventoryTransferResult> TransferFromBasementAsync(int basementItemId, int quantity, string reason);

        /// <summary>
        /// Get basement inventory summary
        /// </summary>
        /// <returns>Basement inventory summary</returns>
        Task<BasementInventorySummary> GetBasementInventorySummaryAsync();

        /// <summary>
        /// Search basement items
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching basement items</returns>
        Task<List<BasementItem>> SearchBasementItemsAsync(string searchTerm);

        /// <summary>
        /// Get basement items with low stock
        /// </summary>
        /// <param name="threshold">Low stock threshold</param>
        /// <returns>List of low stock basement items</returns>
        Task<List<BasementItem>> GetLowStockBasementItemsAsync(int threshold = 10);

        /// <summary>
        /// Get basement inventory movements
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>List of inventory movements</returns>
        Task<List<InventoryMovement>> GetBasementInventoryMovementsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Adjust basement item quantity
        /// </summary>
        /// <param name="basementItemId">Basement item ID</param>
        /// <param name="newQuantity">New quantity</param>
        /// <param name="reason">Adjustment reason</param>
        /// <returns>Adjustment result</returns>
        Task<InventoryAdjustmentResult> AdjustBasementItemQuantityAsync(int basementItemId, int newQuantity, string reason);

        /// <summary>
        /// Get basement item history
        /// </summary>
        /// <param name="basementItemId">Basement item ID</param>
        /// <returns>List of basement item history records</returns>
        Task<List<BasementItemHistory>> GetBasementItemHistoryAsync(int basementItemId);

        /// <summary>
        /// Event fired when basement item is added
        /// </summary>
        event EventHandler<BasementItemEventArgs> BasementItemAdded;

        /// <summary>
        /// Event fired when basement item is updated
        /// </summary>
        event EventHandler<BasementItemEventArgs> BasementItemUpdated;

        /// <summary>
        /// Event fired when basement item is removed
        /// </summary>
        event EventHandler<BasementItemEventArgs> BasementItemRemoved;

        /// <summary>
        /// Event fired when inventory transfer occurs
        /// </summary>
        event EventHandler<InventoryTransferEventArgs> InventoryTransferred;
    }

    /// <summary>
    /// Basement item model
    /// </summary>
    public class BasementItem
    {
        public int Id { get; set; }
        public int MainItemId { get; set; }
        public string ItemCode { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int Quantity { get; set; }
        public decimal Cost { get; set; }
        public string Location { get; set; }
        public DateTime DateAdded { get; set; }
        public DateTime LastUpdated { get; set; }
        public string Notes { get; set; }
        public bool IsActive { get; set; }

        // Navigation properties
        public Item MainItem { get; set; }
    }

    /// <summary>
    /// Basement inventory summary model
    /// </summary>
    public class BasementInventorySummary
    {
        public int TotalItems { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockItems { get; set; }
        public int InactiveItems { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Basement item history model
    /// </summary>
    public class BasementItemHistory
    {
        public int Id { get; set; }
        public int BasementItemId { get; set; }
        public string Action { get; set; }
        public int? OldQuantity { get; set; }
        public int? NewQuantity { get; set; }
        public string Reason { get; set; }
        public DateTime Timestamp { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
    }

    /// <summary>
    /// Event arguments for basement item events
    /// </summary>
    public class BasementItemEventArgs : EventArgs
    {
        public BasementItem BasementItem { get; set; }
        public string Action { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Event arguments for inventory transfer events
    /// </summary>
    public class InventoryTransferEventArgs : EventArgs
    {
        public int ItemId { get; set; }
        public string TransferType { get; set; }
        public int Quantity { get; set; }
        public string Reason { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
