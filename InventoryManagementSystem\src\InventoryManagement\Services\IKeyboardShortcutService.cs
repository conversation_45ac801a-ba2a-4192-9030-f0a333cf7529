using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for managing keyboard shortcuts throughout the application
    /// </summary>
    public interface IKeyboardShortcutService
    {
        /// <summary>
        /// Registers a new keyboard shortcut
        /// </summary>
        /// <param name="id">Unique identifier for the shortcut</param>
        /// <param name="key">Primary key</param>
        /// <param name="modifiers">Key modifiers (Ctrl, Alt, Shift)</param>
        /// <param name="action">Action to execute when shortcut is triggered</param>
        /// <param name="description">Description of the shortcut</param>
        /// <returns>True if registered successfully, false if already exists</returns>
        bool RegisterShortcut(string id, Key key, ModifierKeys modifiers, Action action, string description);
        
        /// <summary>
        /// Unregisters a keyboard shortcut
        /// </summary>
        /// <param name="id">Shortcut ID</param>
        /// <returns>True if unregistered, false if not found</returns>
        bool UnregisterShortcut(string id);
        
        /// <summary>
        /// Gets a list of all registered shortcuts
        /// </summary>
        /// <returns>Collection of keyboard shortcuts</returns>
        IEnumerable<KeyboardShortcut> GetAllShortcuts();
        
        /// <summary>
        /// Handles a key down event and executes the associated shortcut if found
        /// </summary>
        /// <param name="key">The key that was pressed</param>
        /// <param name="modifiers">Key modifiers (Ctrl, Alt, Shift)</param>
        /// <returns>True if a shortcut was executed, false otherwise</returns>
        bool HandleKeyDown(Key key, ModifierKeys modifiers);
        
        /// <summary>
        /// Shows a dialog with all available keyboard shortcuts
        /// </summary>
        void ShowShortcutsDialog();
    }
}
