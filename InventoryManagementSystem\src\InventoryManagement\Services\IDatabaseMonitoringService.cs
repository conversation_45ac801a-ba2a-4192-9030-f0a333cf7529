using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for monitoring database performance and statistics
    /// </summary>
    public interface IDatabaseMonitoringService
    {
        /// <summary>
        /// Event that is triggered when database statistics are updated
        /// </summary>
        event EventHandler<DatabaseStatistics> StatisticsUpdated;
        
        /// <summary>
        /// Event that is triggered when a slow query is detected
        /// </summary>
        event EventHandler<QueryMetrics> SlowQueryDetected;
        
        /// <summary>
        /// Start monitoring the database
        /// </summary>
        /// <returns>True if successfully started, false otherwise</returns>
        Task<bool> StartMonitoringAsync();
        
        /// <summary>
        /// Stop monitoring the database
        /// </summary>
        /// <returns>True if successfully stopped, false otherwise</returns>
        Task<bool> StopMonitoringAsync();
        
        /// <summary>
        /// Track a database query for performance monitoring
        /// </summary>
        /// <param name="metrics">The query metrics to track</param>
        void TrackQuery(QueryMetrics metrics);
        
        /// <summary>
        /// Get the most recent database statistics
        /// </summary>
        /// <returns>The latest database statistics, or null if none available</returns>
        DatabaseStatistics GetLatestStatistics();
        
        /// <summary>
        /// Get recent queries that have been tracked
        /// </summary>
        /// <returns>Collection of recent query metrics</returns>
        IEnumerable<QueryMetrics> GetRecentQueries();
    }
}
