using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models.Domain
{
    /// <summary>
    /// Consolidated core domain models for the offline inventory management system.
    /// This file consolidates fundamental entities that are used across the entire system.
    /// </summary>

    #region User and Authentication Models

    /// <summary>
    /// Represents a user in the system with role-based access
    /// </summary>
    public class CoreUser
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; }

        [Required]
        [StringLength(100)]
        public string FullName { get; set; }

        [Required]
        public string PasswordHash { get; set; }

        [Required]
        public UserRole Role { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        public DateTime? LastModifiedDate { get; set; }

        // Navigation properties
        public virtual ICollection<CoreUserSession> Sessions { get; set; } = new List<CoreUserSession>();
        public virtual ICollection<CoreAuditLog> AuditLogs { get; set; } = new List<CoreAuditLog>();
    }

    /// <summary>
    /// User session tracking for security and audit purposes
    /// </summary>
    public class CoreUserSession
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public string SessionToken { get; set; }

        public DateTime StartTime { get; set; } = DateTime.Now;

        public DateTime? EndTime { get; set; }

        public bool IsActive { get; set; } = true;

        public string IPAddress { get; set; }

        public string UserAgent { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual CoreUser User { get; set; }
    }

    /// <summary>
    /// User roles in the system
    /// </summary>
    public enum UserRole
    {
        Admin = 1,
        BasementManager = 2,
        Cashier = 3
    }

    #endregion

    #region Item and Category Models

    /// <summary>
    /// Core item model representing products in the inventory
    /// </summary>
    public class CoreItem
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string SKU { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [StringLength(1000)]
        public string Description { get; set; }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? CostPrice { get; set; }

        public int MinimumStockLevel { get; set; } = 0;

        public int ReorderLevel { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastModifiedDate { get; set; }

        // Navigation properties
        [ForeignKey("CategoryId")]
        public virtual CoreCategory Category { get; set; }

        public virtual ICollection<CoreItemStock> StockByLocation { get; set; } = new List<CoreItemStock>();
    }

    /// <summary>
    /// Item categories for organization and reporting
    /// </summary>
    public class CoreCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<CoreItem> Items { get; set; } = new List<CoreItem>();
    }

    /// <summary>
    /// Stock levels for items at specific locations
    /// </summary>
    public class CoreItemStock
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ItemId { get; set; }

        [Required]
        public int LocationId { get; set; }

        [Required]
        public int Quantity { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        public int? LastUpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey("ItemId")]
        public virtual CoreItem Item { get; set; }

        [ForeignKey("LocationId")]
        public virtual CoreLocation Location { get; set; }

        [ForeignKey("LastUpdatedBy")]
        public virtual CoreUser LastUpdatedByUser { get; set; }
    }

    #endregion

    #region Location Models

    /// <summary>
    /// Physical locations where inventory is stored
    /// </summary>
    public class CoreLocation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(200)]
        public string Address { get; set; }

        public LocationType Type { get; set; } = LocationType.Warehouse;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<CoreItemStock> ItemStocks { get; set; } = new List<CoreItemStock>();
    }

    /// <summary>
    /// Types of locations in the system
    /// </summary>
    public enum LocationType
    {
        Warehouse = 1,
        Store = 2,
        Basement = 3,
        Display = 4
    }

    #endregion

    #region Audit and Logging Models

    /// <summary>
    /// Audit log for tracking all system activities
    /// </summary>
    public class CoreAuditLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Action { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(1000)]
        public string Details { get; set; }

        public DateTime Timestamp { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string IPAddress { get; set; }

        [StringLength(200)]
        public string UserAgent { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual CoreUser User { get; set; }
    }

    #endregion

    #region Configuration Models

    /// <summary>
    /// Application settings stored in the database
    /// </summary>
    public class CoreAppSetting
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Key { get; set; }

        [Required]
        public string Value { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        public int? LastUpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey("LastUpdatedBy")]
        public virtual CoreUser LastUpdatedByUser { get; set; }
    }

    /// <summary>
    /// User preferences for dashboard and UI customization
    /// </summary>
    public class CoreUserPreference
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string PreferenceKey { get; set; }

        [Required]
        public string PreferenceValue { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual CoreUser User { get; set; }
    }

    #endregion

    #region Common Value Objects

    /// <summary>
    /// Date range value object for filtering and reporting
    /// </summary>
    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public DateRange()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today;
        }

        public DateRange(DateTime startDate, DateTime endDate)
        {
            StartDate = startDate;
            EndDate = endDate;
        }

        public bool IsValid => StartDate <= EndDate;

        public TimeSpan Duration => EndDate - StartDate;

        public bool Contains(DateTime date)
        {
            return date >= StartDate && date <= EndDate;
        }
    }

    /// <summary>
    /// Money value object for consistent currency handling
    /// </summary>
    public class Money
    {
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";

        public Money() { }

        public Money(decimal amount, string currency = "USD")
        {
            Amount = amount;
            Currency = currency;
        }

        public static Money Zero => new Money(0);

        public static Money operator +(Money left, Money right)
        {
            if (left.Currency != right.Currency)
                throw new InvalidOperationException("Cannot add money with different currencies");

            return new Money(left.Amount + right.Amount, left.Currency);
        }

        public static Money operator -(Money left, Money right)
        {
            if (left.Currency != right.Currency)
                throw new InvalidOperationException("Cannot subtract money with different currencies");

            return new Money(left.Amount - right.Amount, left.Currency);
        }

        public override string ToString()
        {
            return $"{Amount:C} {Currency}";
        }
    }

    #endregion
}
