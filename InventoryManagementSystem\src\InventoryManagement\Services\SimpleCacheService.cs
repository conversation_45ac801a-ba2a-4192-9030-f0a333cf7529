using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple in-memory cache service implementation
    /// </summary>
    public class SimpleCacheService : ICacheService
    {
        private readonly ILogger<SimpleCacheService> _logger;
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new ConcurrentDictionary<string, CacheItem>();

        public SimpleCacheService(ILogger<SimpleCacheService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<T> GetAsync<T>(string key)
        {
            await Task.CompletedTask;
            return Get<T>(key);
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
        {
            await Task.CompletedTask;
            Set(key, value, expiration);
        }

        public async Task RemoveAsync(string key)
        {
            await Task.CompletedTask;
            Remove(key);
        }

        public async Task<bool> ExistsAsync(string key)
        {
            await Task.CompletedTask;
            return Exists(key);
        }

        public async Task ClearAsync()
        {
            await Task.CompletedTask;
            Clear();
        }

        public T Get<T>(string key)
        {
            try
            {
                if (_cache.TryGetValue(key, out var item))
                {
                    if (item.ExpiresAt.HasValue && item.ExpiresAt.Value < DateTime.UtcNow)
                    {
                        _cache.TryRemove(key, out _);
                        return default(T);
                    }

                    if (item.Value is T typedValue)
                    {
                        return typedValue;
                    }
                }

                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return default(T);
            }
        }

        public void Set<T>(string key, T value, TimeSpan? expiration = null)
        {
            try
            {
                var expiresAt = expiration.HasValue ? DateTime.UtcNow.Add(expiration.Value) : (DateTime?)null;
                var item = new CacheItem
                {
                    Value = value,
                    ExpiresAt = expiresAt
                };

                _cache.AddOrUpdate(key, item, (k, v) => item);
                _logger.LogDebug("Cached value for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
            }
        }

        public void Remove(string key)
        {
            try
            {
                _cache.TryRemove(key, out _);
                _logger.LogDebug("Removed cached value for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
            }
        }

        public bool Exists(string key)
        {
            try
            {
                if (_cache.TryGetValue(key, out var item))
                {
                    if (item.ExpiresAt.HasValue && item.ExpiresAt.Value < DateTime.UtcNow)
                    {
                        _cache.TryRemove(key, out _);
                        return false;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if cached value exists for key: {Key}", key);
                return false;
            }
        }

        public void Clear()
        {
            try
            {
                _cache.Clear();
                _logger.LogInformation("Cache cleared");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache");
            }
        }

        private class CacheItem
        {
            public object Value { get; set; }
            public DateTime? ExpiresAt { get; set; }
        }
    }
}
