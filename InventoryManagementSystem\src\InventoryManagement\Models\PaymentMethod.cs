namespace InventoryManagement.Models
{
    /// <summary>
    /// Payment methods available in the system
    /// </summary>
    public enum PaymentMethod
    {
        /// <summary>
        /// Cash payment
        /// </summary>
        Cash = 0,

        /// <summary>
        /// Credit card payment
        /// </summary>
        CreditCard = 1,

        /// <summary>
        /// Debit card payment
        /// </summary>
        DebitCard = 2,

        /// <summary>
        /// Check payment
        /// </summary>
        Check = 3,

        /// <summary>
        /// Bank transfer
        /// </summary>
        BankTransfer = 4,

        /// <summary>
        /// Mobile payment (e.g., Apple Pay, Google Pay)
        /// </summary>
        MobilePayment = 5,

        /// <summary>
        /// Gift card payment
        /// </summary>
        GiftCard = 6,

        /// <summary>
        /// Store credit
        /// </summary>
        StoreCredit = 7,

        /// <summary>
        /// Cryptocurrency payment
        /// </summary>
        Cryptocurrency = 8,

        /// <summary>
        /// Buy now, pay later services
        /// </summary>
        BuyNowPayLater = 9,

        /// <summary>
        /// Other payment method
        /// </summary>
        Other = 99
    }

    /// <summary>
    /// Status of a sale transaction
    /// </summary>
    public enum SaleStatus
    {
        /// <summary>
        /// Sale is pending/in progress
        /// </summary>
        Pending = 0,

        /// <summary>
        /// Sale has been completed successfully
        /// </summary>
        Completed = 1,

        /// <summary>
        /// Sale has been voided/cancelled
        /// </summary>
        Voided = 2,

        /// <summary>
        /// Sale has been refunded
        /// </summary>
        Refunded = 3,

        /// <summary>
        /// Sale is partially refunded
        /// </summary>
        PartiallyRefunded = 4,

        /// <summary>
        /// Sale is on hold
        /// </summary>
        OnHold = 5,

        /// <summary>
        /// Sale failed to process
        /// </summary>
        Failed = 6
    }
}
