using System;
using System.Collections.Generic;

namespace InventoryManagement.Infrastructure.Configuration
{
    public class AppConfiguration
    {
        public DatabaseSettings Database { get; set; }
        public AuthSettings Auth { get; set; }
        public CacheSettings Cache { get; set; }
        public NetworkSettings Network { get; set; }
        public OfflineSettings Offline { get; set; }
        public SecurityConfiguration Security { get; set; } = new();
        public BackupConfiguration Backup { get; set; } = new();
        public LoggingConfiguration Logging { get; set; } = new();
    }

    // Note: DatabaseSettings class is defined in Infrastructure/Configuration/DatabaseSettings.cs

    public class AuthSettings
    {
        public string Authority { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string[] Scopes { get; set; }
        public bool RequireHttpsMetadata { get; set; }
    }

    public class CacheSettings
    {
        public bool Enabled { get; set; }
        public int DefaultExpirationMinutes { get; set; }
        public int MaxCacheSizeMB { get; set; }
        public string[] PrewarmItems { get; set; }
        public Dictionary<string, int> TypeExpirations { get; set; }
    }

    public class NetworkSettings
    {
        public string[] TestEndpoints { get; set; }
        public int MonitoringIntervalMs { get; set; }
        public int ConnectionTimeoutMs { get; set; }
        public int RetryCount { get; set; }
        public int RetryDelayMs { get; set; }
        public bool EnableCompression { get; set; }
    }

    public class OfflineSettings
    {
        public int MaxOfflineQueueSize { get; set; }
        public int MaxOperationSizeKB { get; set; }
        public int SyncIntervalMinutes { get; set; }
        public string[] CriticalOperations { get; set; }
        public ConflictResolutionSettings ConflictResolution { get; set; }
    }

    public class ConflictResolutionSettings
    {
        public string DefaultStrategy { get; set; }
        public bool RequireManualResolution { get; set; }
        public Dictionary<string, string> EntityStrategies { get; set; }
    }

    public class SecurityConfiguration
    {
        public int MaxLoginAttempts { get; set; } = 5;
        public int LockoutDurationMinutes { get; set; } = 30;
        public bool RequirePasswordChange { get; set; }
        public int PasswordExpiryDays { get; set; } = 90;
        public string[] AllowedTerminalIds { get; set; } = Array.Empty<string>();
    }

    public class BackupConfiguration
    {
        public bool EnableAutoBackup { get; set; } = true;
        public string BackupPath { get; set; } = "backup";
        public int BackupIntervalHours { get; set; } = 24;
        public int RetentionDays { get; set; } = 7;
        public bool CompressBackups { get; set; } = true;
    }

    public class LoggingConfiguration
    {
        public string LogLevel { get; set; } = "Information";
        public string LogPath { get; set; } = "logs";
        public bool EnableConsoleLogging { get; set; } = true;
        public int RetentionDays { get; set; } = 30;
        public bool IncludeScopes { get; set; } = true;
    }
} 