using System;
using System.Windows;

namespace InventoryManagement.Views
{
    /// <summary>
    /// Interaction logic for SetupWizardWindow.xaml
    /// Guides new users through initial system setup
    /// </summary>
    public partial class SetupWizardWindow : Window
    {
        public SetupWizardWindow()
        {
            InitializeComponent();
            
            // Initialize with a simple setup process
            DataContext = new SetupWizardViewModel();
        }
    }
    
    /// <summary>
    /// Simple ViewModel for the Setup Wizard
    /// </summary>
    public class SetupWizardViewModel
    {
        public string StepText => "Step 1 of 5";
        public Visibility Step1Visibility => Visibility.Visible;
        public Visibility Step2Visibility => Visibility.Collapsed;
        public Visibility Step3Visibility => Visibility.Collapsed;
        public Visibility Step4Visibility => Visibility.Collapsed;
        public Visibility Step5Visibility => Visibility.Collapsed;
        
        public string CompanyName { get; set; } = "Tom General Trading";
        public string CompanyPhone { get; set; } = "";
        public string CompanyEmail { get; set; } = "";
        public string CompanyTaxNumber { get; set; } = "";
        public string CompanyAddress { get; set; } = "";
        
        public bool CanGoPrevious => false;
        public bool CanGoNext => true;
        public bool CanFinish => false;
    }
}
