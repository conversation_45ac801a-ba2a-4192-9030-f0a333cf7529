using InventoryManagement.Infrastructure.Shortcuts;
using InventoryManagement.Infrastructure.MVVM;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace InventoryManagement.Infrastructure.Consolidated.UI
{
    /// <summary>
    /// Consolidated UI service that merges functionality from multiple UI-related services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - Navigation services
    /// - Dialog services
    /// - Shortcut management
    /// - MVVM support
    /// - UI state management
    /// </summary>
    public class ConsolidatedUIService
    {
        private readonly IShortcutManager _shortcutManager;
        private readonly ILogger<ConsolidatedUIService> _logger;
        private readonly Dictionary<string, object> _viewModels = new Dictionary<string, object>();
        private readonly Dictionary<string, Window> _openWindows = new Dictionary<string, Window>();
        private readonly Stack<string> _navigationHistory = new Stack<string>();

        public ConsolidatedUIService(
            IShortcutManager shortcutManager,
            ILogger<ConsolidatedUIService> logger)
        {
            _shortcutManager = shortcutManager ?? throw new ArgumentNullException(nameof(shortcutManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Navigation Management

        /// <summary>
        /// Navigates to a specific view
        /// </summary>
        /// <param name="viewName">Name of the view to navigate to</param>
        /// <param name="parameters">Navigation parameters</param>
        /// <returns>Task representing the async operation</returns>
        public async Task NavigateToAsync(string viewName, Dictionary<string, object> parameters = null)
        {
            try
            {
                _logger.LogInformation("Navigating to view: {ViewName}", viewName);

                // Add current view to navigation history
                if (_navigationHistory.Count > 0)
                {
                    var currentView = _navigationHistory.Peek();
                    if (currentView != viewName)
                    {
                        _navigationHistory.Push(viewName);
                    }
                }
                else
                {
                    _navigationHistory.Push(viewName);
                }

                // Perform navigation logic here
                await PerformNavigationAsync(viewName, parameters);

                _logger.LogInformation("Navigation to {ViewName} completed successfully", viewName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error navigating to view: {ViewName}", viewName);
                throw;
            }
        }

        /// <summary>
        /// Navigates back to the previous view
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task NavigateBackAsync()
        {
            try
            {
                if (_navigationHistory.Count > 1)
                {
                    _navigationHistory.Pop(); // Remove current view
                    var previousView = _navigationHistory.Peek();
                    
                    _logger.LogInformation("Navigating back to view: {ViewName}", previousView);
                    
                    await PerformNavigationAsync(previousView, null);
                }
                else
                {
                    _logger.LogWarning("Cannot navigate back: No previous view in history");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error navigating back");
                throw;
            }
        }

        /// <summary>
        /// Gets the current view name
        /// </summary>
        /// <returns>Current view name</returns>
        public string GetCurrentView()
        {
            return _navigationHistory.Count > 0 ? _navigationHistory.Peek() : null;
        }

        /// <summary>
        /// Clears navigation history
        /// </summary>
        public void ClearNavigationHistory()
        {
            _navigationHistory.Clear();
            _logger.LogDebug("Navigation history cleared");
        }

        #endregion

        #region Dialog Management

        /// <summary>
        /// Shows a message dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="message">Dialog message</param>
        /// <param name="messageType">Type of message</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ShowMessageAsync(string title, string message, MessageType messageType = MessageType.Information)
        {
            try
            {
                _logger.LogInformation("Showing message dialog: {Title}", title);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var icon = messageType switch
                    {
                        MessageType.Information => MessageBoxImage.Information,
                        MessageType.Warning => MessageBoxImage.Warning,
                        MessageType.Error => MessageBoxImage.Error,
                        MessageType.Question => MessageBoxImage.Question,
                        _ => MessageBoxImage.Information
                    };

                    MessageBox.Show(message, title, MessageBoxButton.OK, icon);
                });

                _logger.LogDebug("Message dialog shown successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing message dialog");
                throw;
            }
        }

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="message">Dialog message</param>
        /// <returns>True if user confirmed, false otherwise</returns>
        public async Task<bool> ShowConfirmationAsync(string title, string message)
        {
            try
            {
                _logger.LogInformation("Showing confirmation dialog: {Title}", title);

                var result = await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    return MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
                });

                _logger.LogDebug("Confirmation dialog result: {Result}", result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing confirmation dialog");
                throw;
            }
        }

        /// <summary>
        /// Shows an input dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="prompt">Input prompt</param>
        /// <param name="defaultValue">Default input value</param>
        /// <returns>User input or null if cancelled</returns>
        public async Task<string> ShowInputDialogAsync(string title, string prompt, string defaultValue = "")
        {
            try
            {
                _logger.LogInformation("Showing input dialog: {Title}", title);

                // For simplicity, using a basic input box
                // In a real implementation, you'd create a custom dialog
                var result = await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // This is a simplified implementation
                    // You would typically create a custom input dialog window
                    return Microsoft.VisualBasic.Interaction.InputBox(prompt, title, defaultValue);
                });

                _logger.LogDebug("Input dialog completed");
                return string.IsNullOrEmpty(result) ? null : result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing input dialog");
                throw;
            }
        }

        #endregion

        #region Window Management

        /// <summary>
        /// Opens a new window
        /// </summary>
        /// <param name="windowName">Name of the window</param>
        /// <param name="viewModel">ViewModel for the window</param>
        /// <param name="isModal">Whether the window should be modal</param>
        /// <returns>Task representing the async operation</returns>
        public async Task OpenWindowAsync(string windowName, object viewModel = null, bool isModal = false)
        {
            try
            {
                _logger.LogInformation("Opening window: {WindowName}", windowName);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Check if window is already open
                    if (_openWindows.ContainsKey(windowName))
                    {
                        _openWindows[windowName].Activate();
                        return;
                    }

                    // Create and show new window
                    var window = CreateWindow(windowName, viewModel);
                    _openWindows[windowName] = window;

                    window.Closed += (s, e) => _openWindows.Remove(windowName);

                    if (isModal)
                    {
                        window.ShowDialog();
                    }
                    else
                    {
                        window.Show();
                    }
                });

                _logger.LogInformation("Window {WindowName} opened successfully", windowName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening window: {WindowName}", windowName);
                throw;
            }
        }

        /// <summary>
        /// Closes a window
        /// </summary>
        /// <param name="windowName">Name of the window to close</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CloseWindowAsync(string windowName)
        {
            try
            {
                _logger.LogInformation("Closing window: {WindowName}", windowName);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    if (_openWindows.TryGetValue(windowName, out var window))
                    {
                        window.Close();
                        _openWindows.Remove(windowName);
                    }
                });

                _logger.LogInformation("Window {WindowName} closed successfully", windowName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error closing window: {WindowName}", windowName);
                throw;
            }
        }

        #endregion

        #region Keyboard Shortcuts

        /// <summary>
        /// Registers a keyboard shortcut
        /// </summary>
        /// <param name="shortcut">Shortcut definition</param>
        /// <param name="action">Action to execute when shortcut is pressed</param>
        /// <returns>Task representing the async operation</returns>
        public async Task RegisterShortcutAsync(ShortcutDefinition shortcut, Action action)
        {
            try
            {
                _logger.LogInformation("Registering keyboard shortcut: {Shortcut}", shortcut.DisplayName);

                await Task.Run(() =>
                {
                    _shortcutManager.RegisterShortcut(shortcut, action);
                });

                _logger.LogDebug("Keyboard shortcut registered successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering keyboard shortcut");
                throw;
            }
        }

        /// <summary>
        /// Unregisters a keyboard shortcut
        /// </summary>
        /// <param name="shortcut">Shortcut to unregister</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UnregisterShortcutAsync(ShortcutDefinition shortcut)
        {
            try
            {
                _logger.LogInformation("Unregistering keyboard shortcut: {Shortcut}", shortcut.DisplayName);

                await Task.Run(() =>
                {
                    _shortcutManager.UnregisterShortcut(shortcut);
                });

                _logger.LogDebug("Keyboard shortcut unregistered successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unregistering keyboard shortcut");
                throw;
            }
        }

        #endregion

        #region ViewModel Management

        /// <summary>
        /// Registers a ViewModel for a specific view
        /// </summary>
        /// <param name="viewName">Name of the view</param>
        /// <param name="viewModel">ViewModel instance</param>
        public void RegisterViewModel(string viewName, object viewModel)
        {
            try
            {
                _viewModels[viewName] = viewModel;
                _logger.LogDebug("ViewModel registered for view: {ViewName}", viewName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering ViewModel for view: {ViewName}", viewName);
                throw;
            }
        }

        /// <summary>
        /// Gets a ViewModel for a specific view
        /// </summary>
        /// <param name="viewName">Name of the view</param>
        /// <returns>ViewModel instance or null if not found</returns>
        public T GetViewModel<T>(string viewName) where T : class
        {
            try
            {
                if (_viewModels.TryGetValue(viewName, out var viewModel))
                {
                    return viewModel as T;
                }

                _logger.LogWarning("ViewModel not found for view: {ViewName}", viewName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ViewModel for view: {ViewName}", viewName);
                return null;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task PerformNavigationAsync(string viewName, Dictionary<string, object> parameters)
        {
            // This is where you would implement the actual navigation logic
            // For now, we'll just simulate navigation
            await Task.Delay(100);
            
            _logger.LogDebug("Navigation performed to view: {ViewName}", viewName);
        }

        private Window CreateWindow(string windowName, object viewModel)
        {
            // This is where you would create the actual window based on the window name
            // For now, we'll return a basic window
            var window = new Window
            {
                Title = windowName,
                Width = 800,
                Height = 600,
                WindowStartupLocation = WindowStartupLocation.CenterScreen
            };

            if (viewModel != null)
            {
                window.DataContext = viewModel;
            }

            return window;
        }

        #endregion
    }

    /// <summary>
    /// Message types for dialogs
    /// </summary>
    public enum MessageType
    {
        Information,
        Warning,
        Error,
        Question
    }
}
