{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\tom general trading\\InventoryManagementSystem\\src\\InventoryManagement\\InventoryManagement.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\tom general trading\\InventoryManagementSystem\\src\\InventoryManagement\\InventoryManagement.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\tom general trading\\InventoryManagementSystem\\src\\InventoryManagement\\InventoryManagement.csproj", "projectName": "InventoryManagement", "projectPath": "C:\\Users\\<USER>\\Desktop\\tom general trading\\InventoryManagementSystem\\src\\InventoryManagement\\InventoryManagement.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\tom general trading\\InventoryManagementSystem\\src\\InventoryManagement\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CsvHelper": {"target": "Package", "version": "[33.0.1, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[12.0.0, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Localization": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.DataAnnotations": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.39, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "Polly": {"target": "Package", "version": "[8.2.0, )"}, "QuestPDF": {"target": "Package", "version": "[2023.12.5, )"}, "Serilog": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Extensions.Logging.File": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.CommandLine": {"target": "Package", "version": "[2.0.0-beta4.22272.1, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[8.0.0, )"}, "System.Management": {"target": "Package", "version": "[9.0.5, )"}, "ZXing.Net": {"target": "Package", "version": "[0.16.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}