using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Repository implementation for Supplier operations
    /// </summary>
    public class SupplierRepository : Repository<Supplier>, ISupplierRepository
    {
        private readonly ILogger<SupplierRepository> _logger;

        public SupplierRepository(ApplicationDbContext context, ILogger<SupplierRepository> logger) 
            : base(context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a supplier by name
        /// </summary>
        /// <param name="name">Supplier name</param>
        /// <returns>Supplier with the specified name</returns>
        public async Task<Supplier> GetByNameAsync(string name)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                    return null;

                return await _context.Suppliers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Name.ToLower() == name.ToLower());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supplier by name: {Name}", name);
                throw;
            }
        }

        /// <summary>
        /// Gets suppliers by category
        /// </summary>
        /// <param name="category">Category name</param>
        /// <returns>Suppliers in the specified category</returns>
        public async Task<IEnumerable<Supplier>> GetByCategoryAsync(string category)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(category))
                    return new List<Supplier>();

                return await _context.Suppliers
                    .Where(s => s.Category.ToLower().Contains(category.ToLower()))
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suppliers by category: {Category}", category);
                throw;
            }
        }

        /// <summary>
        /// Gets active suppliers
        /// </summary>
        /// <returns>Active suppliers</returns>
        public async Task<IEnumerable<Supplier>> GetActiveAsync()
        {
            try
            {
                return await _context.Suppliers
                    .Where(s => s.IsActive && !s.IsDeleted)
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active suppliers");
                throw;
            }
        }

        /// <summary>
        /// Gets suppliers by contact email
        /// </summary>
        /// <param name="email">Contact email</param>
        /// <returns>Suppliers with the specified contact email</returns>
        public async Task<IEnumerable<Supplier>> GetByEmailAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return new List<Supplier>();

                return await _context.Suppliers
                    .Where(s => s.ContactEmail.ToLower() == email.ToLower())
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suppliers by email: {Email}", email);
                throw;
            }
        }

        /// <summary>
        /// Gets suppliers by phone number
        /// </summary>
        /// <param name="phoneNumber">Phone number</param>
        /// <returns>Suppliers with the specified phone number</returns>
        public async Task<IEnumerable<Supplier>> GetByPhoneAsync(string phoneNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                    return new List<Supplier>();

                return await _context.Suppliers
                    .Where(s => s.ContactPhone.Contains(phoneNumber))
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suppliers by phone: {PhoneNumber}", phoneNumber);
                throw;
            }
        }

        /// <summary>
        /// Gets suppliers with their supplied items
        /// </summary>
        /// <returns>Suppliers with related item information</returns>
        public async Task<IEnumerable<Supplier>> GetWithItemsAsync()
        {
            try
            {
                return await _context.Suppliers
                    .Include(s => s.SuppliedItems)
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suppliers with items");
                throw;
            }
        }

        /// <summary>
        /// Gets suppliers by country
        /// </summary>
        /// <param name="country">Country name</param>
        /// <returns>Suppliers from the specified country</returns>
        public async Task<IEnumerable<Supplier>> GetByCountryAsync(string country)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(country))
                    return new List<Supplier>();

                return await _context.Suppliers
                    .Where(s => s.Country.ToLower() == country.ToLower())
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suppliers by country: {Country}", country);
                throw;
            }
        }

        /// <summary>
        /// Gets suppliers by preferred payment method
        /// </summary>
        /// <param name="paymentMethod">Payment method</param>
        /// <returns>Suppliers with the specified payment method</returns>
        public async Task<IEnumerable<Supplier>> GetByPaymentMethodAsync(string paymentMethod)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(paymentMethod))
                    return new List<Supplier>();

                return await _context.Suppliers
                    .Where(s => s.PreferredPaymentMethod.ToLower() == paymentMethod.ToLower())
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suppliers by payment method: {PaymentMethod}", paymentMethod);
                throw;
            }
        }

        /// <summary>
        /// Searches suppliers by name or contact person
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>Suppliers matching the search term</returns>
        public async Task<IEnumerable<Supplier>> SearchAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetActiveAsync();

                var term = searchTerm.ToLower();
                return await _context.Suppliers
                    .Where(s => s.Name.ToLower().Contains(term) || 
                               s.ContactPerson.ToLower().Contains(term) ||
                               s.ContactEmail.ToLower().Contains(term))
                    .AsNoTracking()
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching suppliers: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <summary>
        /// Checks if a supplier name already exists
        /// </summary>
        /// <param name="name">Supplier name</param>
        /// <param name="excludeSupplierId">Supplier ID to exclude from check (for updates)</param>
        /// <returns>True if name exists</returns>
        public async Task<bool> NameExistsAsync(string name, int? excludeSupplierId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                    return false;

                var query = _context.Suppliers
                    .Where(s => s.Name.ToLower() == name.ToLower() && !s.IsDeleted);

                if (excludeSupplierId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeSupplierId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if supplier name exists: {Name}", name);
                throw;
            }
        }

        /// <summary>
        /// Gets supplier statistics
        /// </summary>
        /// <returns>Dictionary with supplier statistics</returns>
        public async Task<Dictionary<string, object>> GetStatisticsAsync()
        {
            try
            {
                var totalSuppliers = await _context.Suppliers.CountAsync(s => !s.IsDeleted);
                var activeSuppliers = await _context.Suppliers.CountAsync(s => s.IsActive && !s.IsDeleted);
                var inactiveSuppliers = await _context.Suppliers.CountAsync(s => !s.IsActive && !s.IsDeleted);

                var categoryCounts = await _context.Suppliers
                    .Where(s => !s.IsDeleted)
                    .GroupBy(s => s.Category)
                    .Select(g => new { Category = g.Key, Count = g.Count() })
                    .ToListAsync();

                var countryCounts = await _context.Suppliers
                    .Where(s => !s.IsDeleted)
                    .GroupBy(s => s.Country)
                    .Select(g => new { Country = g.Key, Count = g.Count() })
                    .ToListAsync();

                return new Dictionary<string, object>
                {
                    ["TotalSuppliers"] = totalSuppliers,
                    ["ActiveSuppliers"] = activeSuppliers,
                    ["InactiveSuppliers"] = inactiveSuppliers,
                    ["CategoriesCount"] = categoryCounts.Count,
                    ["CountriesCount"] = countryCounts.Count,
                    ["CategoryBreakdown"] = categoryCounts.ToDictionary(c => c.Category, c => c.Count),
                    ["CountryBreakdown"] = countryCounts.ToDictionary(c => c.Country, c => c.Count)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supplier statistics");
                throw;
            }
        }
    }
}
