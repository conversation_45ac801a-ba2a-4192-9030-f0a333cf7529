using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple offline reporting service implementation
    /// </summary>
    public class SimpleOfflineReportingService : IOfflineReportingService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SimpleOfflineReportingService> _logger;

        public SimpleOfflineReportingService(ApplicationDbContext dbContext, ILogger<SimpleOfflineReportingService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<SalesSummary> GetSalesSummaryAsync(DateTime startDate, DateTime endDate, int? locationId = null)
        {
            try
            {
                var sales = await _dbContext.Sales
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                    .ToListAsync();

                return new SalesSummary
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalSales = sales.Sum(s => s.TotalAmount),
                    TransactionCount = sales.Count,
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales summary");
                return new SalesSummary
                {
                    StartDate = startDate,
                    EndDate = endDate
                };
            }
        }

        public async Task<InventorySummary> GetInventorySummaryAsync(DateTime? asOfDate = null)
        {
            try
            {
                var products = await _dbContext.Products.ToListAsync();

                return new InventorySummary
                {
                    AsOfDate = asOfDate ?? DateTime.Now,
                    TotalItems = products.Count,
                    TotalValue = products.Sum(p => p.Price * p.StockQuantity),
                    LowStockItems = products.Count(p => p.StockQuantity <= p.ReorderLevel),
                    OutOfStockItems = products.Count(p => p.StockQuantity == 0)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory summary");
                return new InventorySummary
                {
                    AsOfDate = asOfDate ?? DateTime.Now
                };
            }
        }

        public async Task<List<TopSellingItem>> GetTopSellingProductsAsync(DateTime startDate, DateTime endDate, int count = 10)
        {
            try
            {
                var topProducts = await _dbContext.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => new { si.ProductId, si.Product.Name })
                    .Select(g => new TopSellingItem
                    {
                        ItemId = g.Key.ProductId,
                        Name = g.Key.Name,
                        QuantitySold = g.Sum(si => si.Quantity),
                        TotalSales = g.Sum(si => si.Quantity * si.UnitPrice)
                    })
                    .OrderByDescending(p => p.QuantitySold)
                    .Take(count)
                    .ToListAsync();

                return topProducts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top selling products");
                return new List<TopSellingItem>();
            }
        }

        public async Task<List<LowStockReport>> GetLowStockReportAsync()
        {
            try
            {
                var lowStockItems = await _dbContext.Products
                    .Where(p => p.StockQuantity <= p.ReorderLevel)
                    .Select(p => new LowStockReport
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        SKU = p.SKU,
                        CurrentStock = p.StockQuantity,
                        ReorderLevel = p.ReorderLevel,
                        SuggestedOrderQuantity = Math.Max(p.ReorderLevel * 2 - p.StockQuantity, 0)
                    })
                    .ToListAsync();

                return lowStockItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock report");
                return new List<LowStockReport>();
            }
        }

        public async Task<CustomerSalesReport> GetCustomerSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var customerSales = await _dbContext.Sales
                    .Include(s => s.Customer)
                    .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate && s.CustomerId.HasValue)
                    .GroupBy(s => new { s.CustomerId, s.Customer.Name })
                    .Select(g => new CustomerSalesData
                    {
                        CustomerId = g.Key.CustomerId.Value,
                        CustomerName = g.Key.Name,
                        TotalSales = g.Sum(s => s.TotalAmount),
                        TransactionCount = g.Count()
                    })
                    .OrderByDescending(c => c.TotalSales)
                    .ToListAsync();

                return new CustomerSalesReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    CustomerSales = customerSales
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer sales report");
                return new CustomerSalesReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    CustomerSales = new List<CustomerSalesData>()
                };
            }
        }

        public async Task<byte[]> ExportSalesReportAsync(DateTime startDate, DateTime endDate, string format = "PDF")
        {
            // Simple implementation - not supported in offline mode
            _logger.LogInformation("Report export not supported in offline mode");
            await Task.CompletedTask;
            return new byte[0];
        }

        public async Task<byte[]> ExportInventoryReportAsync(string format = "PDF")
        {
            // Simple implementation - not supported in offline mode
            _logger.LogInformation("Report export not supported in offline mode");
            await Task.CompletedTask;
            return new byte[0];
        }

        public async Task<bool> ScheduleReportAsync(string reportType, DateTime scheduledTime, string recipients)
        {
            // Simple implementation - not supported in offline mode
            _logger.LogInformation("Report scheduling not supported in offline mode");
            await Task.CompletedTask;
            return false;
        }

        public async Task<List<ScheduledReport>> GetScheduledReportsAsync()
        {
            // Simple implementation - return empty list
            await Task.CompletedTask;
            return new List<ScheduledReport>();
        }
    }

    // Note: Using existing model classes from Models namespace
}
