﻿#pragma checksum "..\..\..\..\Views\BasementManagerDashboard.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EC6F12DAA8FBD067A9AD1D61F4144582F7886584"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InventoryManagement.Controls;
using InventoryManagement.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InventoryManagement.Views {
    
    
    /// <summary>
    /// BasementManagerDashboard
    /// </summary>
    public partial class BasementManagerDashboard : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 38 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardNavButton;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryNavButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddItemsNavButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransferNavButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsNavButton;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ItemExchangeNavButton;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DefectiveItemNavButton;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LocationsGrid;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RecentActivitiesGrid;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PendingVerificationGrid;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl InventoryContent;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl AddItemsContent;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl TransferContent;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl ReportsContent;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl ItemExchangeContent;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl DefectiveItemContent;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationSidebar;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal InventoryManagement.Controls.NotificationControl NotificationControl;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NotificationButton;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationBadge;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Views\BasementManagerDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotificationCount;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InventoryManagement;component/views/basementmanagerdashboard.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 48 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DashboardNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.DashboardNavButton.Click += new System.Windows.RoutedEventHandler(this.DashboardNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InventoryNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.InventoryNavButton.Click += new System.Windows.RoutedEventHandler(this.InventoryNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AddItemsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.AddItemsNavButton.Click += new System.Windows.RoutedEventHandler(this.AddItemsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TransferNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.TransferNavButton.Click += new System.Windows.RoutedEventHandler(this.TransferNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ReportsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.ReportsNavButton.Click += new System.Windows.RoutedEventHandler(this.ReportsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ItemExchangeNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.ItemExchangeNavButton.Click += new System.Windows.RoutedEventHandler(this.ItemExchangeNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DefectiveItemNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 127 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.DefectiveItemNavButton.Click += new System.Windows.RoutedEventHandler(this.DefectiveItemNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.DashboardContent = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 11:
            this.LocationsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.RecentActivitiesGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.PendingVerificationGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.InventoryContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 15:
            this.AddItemsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 16:
            this.TransferContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 17:
            this.ReportsContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 18:
            this.ItemExchangeContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 19:
            this.DefectiveItemContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 20:
            this.NotificationSidebar = ((System.Windows.Controls.Border)(target));
            return;
            case 21:
            this.NotificationControl = ((InventoryManagement.Controls.NotificationControl)(target));
            return;
            case 22:
            this.NotificationButton = ((System.Windows.Controls.Button)(target));
            
            #line 234 "..\..\..\..\Views\BasementManagerDashboard.xaml"
            this.NotificationButton.Click += new System.Windows.RoutedEventHandler(this.NotificationButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.NotificationBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.NotificationCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

