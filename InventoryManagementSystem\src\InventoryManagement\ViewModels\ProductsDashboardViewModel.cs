using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using InventoryManagement.Commands;
using InventoryManagement.Infrastructure.Data;
using InventoryManagement.Infrastructure.Caching;
using InventoryManagement.Models;
using InventoryManagement.Services;
// Note: Data services are in InventoryManagement.Services namespace
using InventoryManagement.DataAccess;
using Microsoft.EntityFrameworkCore;
using IAuditService = InventoryManagement.Services.IAuditService;
using ICacheService = InventoryManagement.Infrastructure.Caching.ICacheService;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Products section of the main dashboard
    /// </summary>
    public class ProductsDashboardViewModel : ViewModelBase
    {
        private readonly IDataContext _dataContext;
        private readonly IAuditService _auditService;
        private readonly IBarcodeService _barcodeService;
        private readonly ICacheService _cacheService;
        
        private ObservableCollection<ProductViewModel> _products;
        private ICollectionView _filteredProducts;
        private string _searchText;
        private bool _isGridView = true;
        private bool _isListView;
        private string _selectedCategory;
        private string _selectedSupplier;
        private bool _isLoading;
        private ProductViewModel _selectedProduct;
        
        public ProductsDashboardViewModel(
            IDataContext dataContext, 
            IAuditService auditService,
            IBarcodeService barcodeService,
            ICacheService cacheService)
        {
            _dataContext = dataContext ?? throw new ArgumentNullException(nameof(dataContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _barcodeService = barcodeService ?? throw new ArgumentNullException(nameof(barcodeService));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            
            // Initialize commands
            SearchCommand = new RelayCommand(param => ApplyFilters());
            AddProductCommand = new RelayCommand(param => OpenAddProductDialog());
            ExportCommand = new RelayCommand(param => ExportProducts());
            RefreshCommand = new RelayCommand(async param => await LoadProductsAsync());
            GridViewCommand = new RelayCommand(param => SwitchToGridView());
            ListViewCommand = new RelayCommand(param => SwitchToListView());
            
            // Initialize collections
            Products = new ObservableCollection<ProductViewModel>();
            Categories = new ObservableCollection<string>();
            Suppliers = new ObservableCollection<string>();
            
            // Initial load
            _ = InitializeAsync();
        }
        
        #region Properties
        
        public ObservableCollection<ProductViewModel> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }
        
        public ICollectionView FilteredProducts
        {
            get => _filteredProducts;
            set => SetProperty(ref _filteredProducts, value);
        }
        
        public ObservableCollection<string> Categories { get; }
        
        public ObservableCollection<string> Suppliers { get; }
        
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }
        
        public bool IsGridView
        {
            get => _isGridView;
            set => SetProperty(ref _isGridView, value);
        }
        
        public bool IsListView
        {
            get => _isListView;
            set => SetProperty(ref _isListView, value);
        }
        
        public string SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                if (SetProperty(ref _selectedCategory, value))
                {
                    ApplyFilters();
                }
            }
        }
        
        public string SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                if (SetProperty(ref _selectedSupplier, value))
                {
                    ApplyFilters();
                }
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }
        
        public ProductViewModel SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand SearchCommand { get; }
        public ICommand AddProductCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand GridViewCommand { get; }
        public ICommand ListViewCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                
                // Try to load categories and suppliers from cache first
                var cachedCategories = _cacheService.Get<List<string>>("ProductCategories");
                var cachedSuppliers = _cacheService.Get<List<string>>("ProductSuppliers");
                
                if (cachedCategories != null)
                {
                    foreach (var category in cachedCategories)
                    {
                        Categories.Add(category);
                    }
                }
                
                if (cachedSuppliers != null)
                {
                    foreach (var supplier in cachedSuppliers)
                    {
                        Suppliers.Add(supplier);
                    }
                }
                
                // Load products
                await LoadProductsAsync();
                
                // If categories weren't in cache, load them from database
                if (cachedCategories == null)
                {
                    await LoadCategoriesAsync();
                }
                
                // If suppliers weren't in cache, load them from database
                if (cachedSuppliers == null)
                {
                    await LoadSuppliersAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error initializing Products Dashboard: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private async Task LoadProductsAsync()
        {
            try
            {
                IsLoading = true;
                
                // Check if products are in cache
                var cachedProducts = _cacheService.Get<List<Item>>("Products");
                
                if (cachedProducts == null)
                {
                    // Not in cache, load from database
                    cachedProducts = await _dataContext.Items
                        .Where(i => !i.IsDeleted)
                        .Include(i => i.StockByLocation)
                        .ToListAsync();
                    
                    // Cache the result for future use
                    _cacheService.Set("Products", cachedProducts, TimeSpan.FromMinutes(5));
                }
                
                // Clear existing products
                Products.Clear();
                
                // Add products to collection
                foreach (var item in cachedProducts)
                {
                    Products.Add(new ProductViewModel(item, _barcodeService));
                }
                
                // Set up filtering
                FilteredProducts = CollectionViewSource.GetDefaultView(Products);
                FilteredProducts.Filter = ProductFilter;
                
                // Log audit trail for viewing products
                await _auditService.LogActionAsync(
                    "Products", 
                    "View", 
                    $"Viewed {Products.Count} products", 
                    null);
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error loading products: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private async Task LoadCategoriesAsync()
        {
            try
            {
                // Get unique categories from database
                var categories = await _dataContext.Items
                    .Where(i => !i.IsDeleted && !string.IsNullOrEmpty(i.Category))
                    .Select(i => i.Category)
                    .Distinct()
                    .ToListAsync();
                
                // Update collection
                Categories.Clear();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
                
                // Cache the result
                _cacheService.Set("ProductCategories", categories, TimeSpan.FromHours(1));
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error loading categories: {ex.Message}");
            }
        }
        
        private async Task LoadSuppliersAsync()
        {
            try
            {
                // For the offline-only version, suppliers are simplified
                var suppliers = await _dataContext.Items
                    .Where(i => !i.IsDeleted && !string.IsNullOrEmpty(i.Manufacturer))
                    .Select(i => i.Manufacturer)
                    .Distinct()
                    .ToListAsync();
                
                // Update collection
                Suppliers.Clear();
                foreach (var supplier in suppliers)
                {
                    Suppliers.Add(supplier);
                }
                
                // Cache the result
                _cacheService.Set("ProductSuppliers", suppliers, TimeSpan.FromHours(1));
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error loading suppliers: {ex.Message}");
            }
        }
        
        private bool ProductFilter(object item)
        {
            if (item is not ProductViewModel product)
                return false;
            
            // Apply search text filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                bool matchesSearch = product.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    product.SKU.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    product.Barcode.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    product.Category.Contains(SearchText, StringComparison.OrdinalIgnoreCase);
                
                if (!matchesSearch)
                    return false;
            }
            
            // Apply category filter
            if (!string.IsNullOrWhiteSpace(SelectedCategory) && SelectedCategory != "All Categories")
            {
                if (product.Category != SelectedCategory)
                    return false;
            }
            
            // Apply supplier filter
            if (!string.IsNullOrWhiteSpace(SelectedSupplier) && SelectedSupplier != "All Suppliers")
            {
                if (product.Supplier != SelectedSupplier)
                    return false;
            }
            
            return true;
        }
        
        private void ApplyFilters()
        {
            FilteredProducts?.Refresh();
        }
        
        private void SwitchToGridView()
        {
            IsGridView = true;
            IsListView = false;
        }
        
        private void SwitchToListView()
        {
            IsGridView = false;
            IsListView = true;
        }
        
        private void OpenAddProductDialog()
        {
            // Implementation would open a dialog for adding a new product
            // This would typically use a dialog service
        }
        
        private void ExportProducts()
        {
            // Implementation would export products to Excel/CSV
            // This would typically use an export service
        }
        
        #endregion
    }
    
    /// <summary>
    /// ViewModel for an individual product in the UI
    /// </summary>
    public class ProductViewModel : ViewModelBase
    {
        private readonly Item _item;
        private readonly IBarcodeService _barcodeService;
        
        public ProductViewModel(Item item, IBarcodeService barcodeService)
        {
            _item = item ?? throw new ArgumentNullException(nameof(item));
            _barcodeService = barcodeService ?? throw new ArgumentNullException(nameof(barcodeService));
            
            // Initialize commands
            EditCommand = new RelayCommand(param => EditProduct());
            DetailsCommand = new RelayCommand(param => ViewDetails());
            
            // Generate barcode if not already set
            if (string.IsNullOrEmpty(_item.Barcode) && !string.IsNullOrEmpty(_item.SKU))
            {
                GenerateBarcode();
            }
        }
        
        #region Properties
        
        public int Id => _item.Id;
        public string Name => _item.Name;
        public string SKU => _item.SKU;
        public string Category => _item.Category;
        public string SubCategory => _item.SubCategory;
        public decimal Price => _item.SellingPrice;
        public decimal EffectivePrice => _item.EffectivePrice;
        public string Barcode => _item.Barcode;
        public int StockLevel => _item.TotalStockQuantity;
        public string Brand => _item.Brand;
        public string Model => _item.Model;
        public string Supplier => _item.Manufacturer;
        public string ImagePath => _item.ImagePath;
        public bool IsActive => _item.IsActive;
        
        #endregion
        
        #region Commands
        
        public ICommand EditCommand { get; }
        public ICommand DetailsCommand { get; }
        
        #endregion
        
        #region Methods
        
        private void EditProduct()
        {
            // Implementation would open a dialog for editing this product
            // This would typically use a dialog service
        }
        
        private void ViewDetails()
        {
            // Implementation would navigate to product details view
            // This would typically use a navigation service
        }
        
        private void GenerateBarcode()
        {
            try
            {
                // Generate a barcode based on SKU
                string barcode = _barcodeService.GenerateBarcodeFromSKU(_item.SKU);
                
                // In a real implementation, we would update the item in the database
                // For this example, we just update the property
                _item.Barcode = barcode;
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error generating barcode: {ex.Message}");
            }
        }
        
        #endregion
    }
}
