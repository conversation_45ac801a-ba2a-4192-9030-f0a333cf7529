using System;
using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents an item that is approaching or has passed its expiry date
    /// </summary>
    public class ExpiringItem
    {
        /// <summary>
        /// Unique identifier for the expiring item record
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// ID of the item
        /// </summary>
        [Required]
        public int ItemId { get; set; }

        /// <summary>
        /// Item name
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; }

        /// <summary>
        /// Item SKU
        /// </summary>
        [StringLength(50)]
        public string SKU { get; set; }

        /// <summary>
        /// Category name
        /// </summary>
        [StringLength(100)]
        public string CategoryName { get; set; }

        /// <summary>
        /// Current quantity in stock
        /// </summary>
        [Required]
        public int CurrentQuantity { get; set; }

        /// <summary>
        /// Expiry date of the item
        /// </summary>
        [Required]
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// Number of days until expiry (negative if already expired)
        /// </summary>
        public int DaysUntilExpiry { get; set; }

        /// <summary>
        /// Batch or lot number
        /// </summary>
        [StringLength(100)]
        public string BatchNumber { get; set; }

        /// <summary>
        /// Serial number if applicable
        /// </summary>
        [StringLength(100)]
        public string SerialNumber { get; set; }

        /// <summary>
        /// Location where the item is stored
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// Location name
        /// </summary>
        [StringLength(100)]
        public string LocationName { get; set; }

        /// <summary>
        /// Cost price of the item
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// Selling price of the item
        /// </summary>
        public decimal SellingPrice { get; set; }

        /// <summary>
        /// Total value of expiring stock (Quantity * CostPrice)
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// Potential loss if items expire
        /// </summary>
        public decimal PotentialLoss { get; set; }

        /// <summary>
        /// Priority level for handling (High, Medium, Low)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Priority { get; set; }

        /// <summary>
        /// Status of the expiring item (Active, Discounted, Disposed, Sold)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; }

        /// <summary>
        /// Suggested action (Discount, Promote, Dispose, etc.)
        /// </summary>
        [StringLength(100)]
        public string SuggestedAction { get; set; }

        /// <summary>
        /// Notes about the expiring item
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Whether notification has been sent
        /// </summary>
        public bool NotificationSent { get; set; }

        /// <summary>
        /// Date when notification was sent
        /// </summary>
        public DateTime? NotificationSentAt { get; set; }

        /// <summary>
        /// Date when the item was last checked
        /// </summary>
        public DateTime LastCheckedAt { get; set; }

        /// <summary>
        /// User who last checked the item
        /// </summary>
        [StringLength(100)]
        public string LastCheckedBy { get; set; }

        /// <summary>
        /// Date when the record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when the record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Whether the item has already expired
        /// </summary>
        public bool IsExpired => DateTime.Now > ExpiryDate;

        /// <summary>
        /// Whether the item is expiring soon (within warning period)
        /// </summary>
        public bool IsExpiringSoon => DaysUntilExpiry <= 7 && DaysUntilExpiry > 0;

        /// <summary>
        /// Whether the item is critically expiring (within 2 days)
        /// </summary>
        public bool IsCriticallyExpiring => DaysUntilExpiry <= 2 && DaysUntilExpiry > 0;
    }
}
