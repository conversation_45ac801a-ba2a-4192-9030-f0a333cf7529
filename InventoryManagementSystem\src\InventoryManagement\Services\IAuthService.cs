using System;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for authentication services
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// Authenticate a user with username and password
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Authentication result</returns>
        Task<AuthResult> AuthenticateAsync(string username, string password);

        /// <summary>
        /// Logout the current user
        /// </summary>
        /// <returns>Task</returns>
        Task LogoutAsync();

        /// <summary>
        /// Get the current authenticated user
        /// </summary>
        /// <returns>Current user or null if not authenticated</returns>
        User GetCurrentUser();

        /// <summary>
        /// Check if a user is authenticated
        /// </summary>
        /// <returns>True if authenticated</returns>
        bool IsAuthenticated();

        /// <summary>
        /// Validate a user's session
        /// </summary>
        /// <param name="sessionToken">Session token</param>
        /// <returns>True if session is valid</returns>
        Task<bool> ValidateSessionAsync(string sessionToken);

        /// <summary>
        /// Refresh an authentication token
        /// </summary>
        /// <param name="refreshToken">Refresh token</param>
        /// <returns>New authentication result</returns>
        Task<AuthResult> RefreshTokenAsync(string refreshToken);

        /// <summary>
        /// Change a user's password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>True if password was changed successfully</returns>
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);

        /// <summary>
        /// Reset a user's password
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>True if password reset was initiated</returns>
        Task<bool> ResetPasswordAsync(string username);

        /// <summary>
        /// Lock a user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if account was locked</returns>
        Task<bool> LockAccountAsync(int userId);

        /// <summary>
        /// Unlock a user account
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if account was unlocked</returns>
        Task<bool> UnlockAccountAsync(int userId);

        /// <summary>
        /// Check if an account is locked
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if account is locked</returns>
        Task<bool> IsAccountLockedAsync(int userId);

        /// <summary>
        /// Get failed login attempts for a user
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>Number of failed attempts</returns>
        Task<int> GetFailedLoginAttemptsAsync(string username);

        /// <summary>
        /// Clear failed login attempts for a user
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>Task</returns>
        Task ClearFailedLoginAttemptsAsync(string username);

        /// <summary>
        /// Event fired when a user logs in
        /// </summary>
        event EventHandler<UserLoginEventArgs> UserLoggedIn;

        /// <summary>
        /// Event fired when a user logs out
        /// </summary>
        event EventHandler<UserLogoutEventArgs> UserLoggedOut;

        /// <summary>
        /// Event fired when authentication fails
        /// </summary>
        event EventHandler<AuthenticationFailedEventArgs> AuthenticationFailed;
    }

    /// <summary>
    /// Event arguments for user login events
    /// </summary>
    public class UserLoginEventArgs : EventArgs
    {
        public User User { get; set; }
        public DateTime LoginTime { get; set; }
        public string IpAddress { get; set; }
    }

    /// <summary>
    /// Event arguments for user logout events
    /// </summary>
    public class UserLogoutEventArgs : EventArgs
    {
        public User User { get; set; }
        public DateTime LogoutTime { get; set; }
        public string Reason { get; set; }
    }

    /// <summary>
    /// Event arguments for authentication failed events
    /// </summary>
    public class AuthenticationFailedEventArgs : EventArgs
    {
        public string Username { get; set; }
        public DateTime AttemptTime { get; set; }
        public string IpAddress { get; set; }
        public string Reason { get; set; }
        public int FailedAttempts { get; set; }
    }
}
