using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Events
{
    /// <summary>
    /// Simple event bus implementation for desktop application
    /// </summary>
    public class EventBus
    {
        private readonly ILogger<EventBus> _logger;
        private readonly ConcurrentDictionary<Type, List<Delegate>> _handlers = new ConcurrentDictionary<Type, List<Delegate>>();

        public EventBus(ILogger<EventBus> logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// Subscribe to an event of type T
        /// </summary>
        public void Subscribe<T>(Action<T> handler) where T : class
        {
            var type = typeof(T);
            _handlers.AddOrUpdate(
                type,
                _ => new List<Delegate> { handler },
                (_, list) =>
                {
                    list.Add(handler);
                    return list;
                });
        }

        /// <summary>
        /// Unsubscribe from an event of type T
        /// </summary>
        public void Unsubscribe<T>(Action<T> handler) where T : class
        {
            var type = typeof(T);
            if (_handlers.TryGetValue(type, out var list))
            {
                list.Remove(handler);
            }
        }

        /// <summary>
        /// Publish an event to all subscribers
        /// </summary>
        public void Publish<T>(T @event) where T : class
        {
            if (@event == null)
                return;

            try
            {
                var type = @event.GetType();
                if (_handlers.TryGetValue(type, out var handlers))
                {
                    foreach (var handler in handlers)
                    {
                        try
                        {
                            ((Action<T>)handler)(@event);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error handling event of type {EventType}", type.Name);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error publishing event of type {EventType}", typeof(T).Name);
            }
        }

        /// <summary>
        /// Publish an event to all subscribers asynchronously
        /// </summary>
        public async Task PublishAsync<T>(T @event) where T : class
        {
            if (@event == null)
                return;

            try
            {
                var type = @event.GetType();
                if (_handlers.TryGetValue(type, out var handlers))
                {
                    var tasks = new List<Task>();
                    
                    foreach (var handler in handlers)
                    {
                        tasks.Add(Task.Run(() => {
                            try
                            {
                                ((Action<T>)handler)(@event);
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogError(ex, "Error handling event of type {EventType}", type.Name);
                            }
                        }));
                    }
                    
                    await Task.WhenAll(tasks);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error publishing event of type {EventType}", typeof(T).Name);
            }
        }
    }

    /// <summary>
    /// Base class for all events
    /// </summary>
    public abstract class BaseEvent
    {
        public DateTime Timestamp { get; } = DateTime.Now;
        public Guid EventId { get; } = Guid.NewGuid();
    }

    /// <summary>
    /// Event raised when a notification is created
    /// </summary>
    public class NotificationCreatedEvent : BaseEvent
    {
        public Models.Notification Notification { get; }

        public NotificationCreatedEvent(Models.Notification notification)
        {
            Notification = notification ?? throw new ArgumentNullException(nameof(notification));
        }
    }

    /// <summary>
    /// Event raised when a notification should be displayed in the UI
    /// </summary>
    public class NotificationDisplayEvent : BaseEvent
    {
        public Models.Notification Notification { get; }

        public NotificationDisplayEvent(Models.Notification notification)
        {
            Notification = notification ?? throw new ArgumentNullException(nameof(notification));
        }
    }
}
