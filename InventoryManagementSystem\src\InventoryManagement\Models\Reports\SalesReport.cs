using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Sales report model containing sales analytics and metrics
    /// </summary>
    public class SalesReport
    {
        public DateTime ReportDate { get; set; } = DateTime.Now;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string ReportPeriod { get; set; } = string.Empty;
        public string GeneratedBy { get; set; } = string.Empty;

        // Summary metrics
        public decimal TotalSales { get; set; }
        public decimal TotalRefunds { get; set; }
        public decimal NetSales { get; set; }
        public int TotalTransactions { get; set; }
        public int TotalRefundTransactions { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int TotalItemsSold { get; set; }
        public decimal TotalDiscountsGiven { get; set; }

        // Payment method breakdown
        public decimal CashSales { get; set; }
        public decimal CreditSales { get; set; }
        public decimal OtherPaymentSales { get; set; }

        // Time-based analytics
        public List<HourlySales> HourlySalesData { get; set; } = new List<HourlySales>();
        public List<DailySales> DailySalesData { get; set; } = new List<DailySales>();

        // Product analytics
        public List<TopSellingItem> TopSellingItems { get; set; } = new List<TopSellingItem>();
        public List<CategorySales> CategorySalesData { get; set; } = new List<CategorySales>();

        // Cashier performance
        public List<CashierPerformance> CashierPerformanceData { get; set; } = new List<CashierPerformance>();

        // Customer analytics
        public int UniqueCustomers { get; set; }
        public int NewCustomers { get; set; }
        public int ReturningCustomers { get; set; }
        public List<TopCustomer> TopCustomers { get; set; } = new List<TopCustomer>();

        // Discount analytics
        public List<DiscountUsage> DiscountUsageData { get; set; } = new List<DiscountUsage>();

        // Return analytics
        public decimal ReturnRate { get; set; } // Percentage
        public List<ReturnReason> ReturnReasonData { get; set; } = new List<ReturnReason>();
    }

    /// <summary>
    /// Hourly sales breakdown
    /// </summary>
    public class HourlySales
    {
        public int Hour { get; set; }
        public string TimeRange { get; set; } = string.Empty;
        public decimal Sales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }

    /// <summary>
    /// Daily sales breakdown
    /// </summary>
    public class DailySales
    {
        public DateTime Date { get; set; }
        public string DayOfWeek { get; set; } = string.Empty;
        public decimal Sales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int ItemsSold { get; set; }
    }

    // Note: TopSellingItem class is defined in Models/InventoryModels.cs

    /// <summary>
    /// Category sales breakdown
    /// </summary>
    public class CategorySales
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int ItemsSold { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageItemPrice { get; set; }
        public decimal PercentageOfTotalSales { get; set; }
    }

    /// <summary>
    /// Cashier performance metrics
    /// </summary>
    public class CashierPerformance
    {
        public int CashierId { get; set; }
        public string CashierName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int ItemsSold { get; set; }
        public decimal TotalDiscountsGiven { get; set; }
        public int RefundsProcessed { get; set; }
        public decimal RefundAmount { get; set; }
        public TimeSpan TotalWorkTime { get; set; }
        public decimal SalesPerHour { get; set; }
        public decimal TransactionsPerHour { get; set; }
    }

    /// <summary>
    /// Top customer information
    /// </summary>
    public class TopCustomer
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerNumber { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public DateTime LastPurchaseDate { get; set; }
        public string CustomerType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Discount usage analytics
    /// </summary>
    public class DiscountUsage
    {
        public int DiscountId { get; set; }
        public string DiscountCode { get; set; } = string.Empty;
        public string DiscountName { get; set; } = string.Empty;
        public int UsageCount { get; set; }
        public decimal TotalDiscountAmount { get; set; }
        public decimal AverageDiscountAmount { get; set; }
        public int UniqueCustomers { get; set; }
        public decimal PercentageOfTransactions { get; set; }
    }

    /// <summary>
    /// Return reason analytics
    /// </summary>
    public class ReturnReason
    {
        public string Reason { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PercentageOfReturns { get; set; }
    }

    /// <summary>
    /// Sales report parameters for generating reports
    /// </summary>
    public class SalesReportParameters
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<int> LocationIds { get; set; } = new List<int>();
        public List<int> CashierIds { get; set; } = new List<int>();
        public List<string> Categories { get; set; } = new List<string>();
        public bool IncludeRefunds { get; set; } = true;
        public bool IncludeDiscounts { get; set; } = true;
        public bool IncludeHourlyBreakdown { get; set; } = false;
        public bool IncludeCashierPerformance { get; set; } = true;
        public bool IncludeCustomerAnalytics { get; set; } = true;
        public int TopItemsCount { get; set; } = 10;
        public int TopCustomersCount { get; set; } = 10;
        public SalesReportGrouping Grouping { get; set; } = SalesReportGrouping.Daily;
    }

    /// <summary>
    /// Sales report grouping options
    /// </summary>
    public enum SalesReportGrouping
    {
        Hourly,
        Daily,
        Weekly,
        Monthly,
        Yearly
    }

    /// <summary>
    /// Sales report export format
    /// </summary>
    public enum SalesReportFormat
    {
        PDF,
        Excel,
        CSV,
        JSON
    }
}
