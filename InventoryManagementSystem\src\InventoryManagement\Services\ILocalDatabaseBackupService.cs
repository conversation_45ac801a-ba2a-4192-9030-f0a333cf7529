using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for local database backup services
    /// </summary>
    public interface ILocalDatabaseBackupService
    {
        /// <summary>
        /// Creates a backup of the database
        /// </summary>
        /// <param name="backupPath">Path where to save the backup</param>
        /// <param name="description">Optional description for the backup</param>
        /// <returns>Backup information</returns>
        Task<BackupInfo> CreateBackupAsync(string backupPath, string description = null);

        /// <summary>
        /// Creates an automatic backup with default naming
        /// </summary>
        /// <param name="description">Optional description for the backup</param>
        /// <returns>Backup information</returns>
        Task<BackupInfo> CreateAutoBackupAsync(string description = null);

        /// <summary>
        /// Restores the database from a backup
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <returns>Task representing the async operation</returns>
        Task RestoreBackupAsync(string backupPath);

        /// <summary>
        /// Verifies the integrity of a backup file
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <returns>Verification result</returns>
        Task<BackupVerificationResult> VerifyBackupAsync(string backupPath);

        /// <summary>
        /// Gets a list of available backups
        /// </summary>
        /// <returns>List of backup information</returns>
        Task<List<BackupInfo>> GetAvailableBackupsAsync();

        /// <summary>
        /// Deletes a backup file
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <returns>True if backup was deleted successfully</returns>
        Task<bool> DeleteBackupAsync(string backupPath);

        /// <summary>
        /// Schedules automatic backups
        /// </summary>
        /// <param name="schedule">Backup schedule configuration</param>
        /// <returns>Task representing the async operation</returns>
        Task ScheduleBackupsAsync(BackupSchedule schedule);

        /// <summary>
        /// Cancels scheduled backups
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task CancelScheduledBackupsAsync();

        /// <summary>
        /// Gets the current backup schedule
        /// </summary>
        /// <returns>Current backup schedule or null if not scheduled</returns>
        Task<BackupSchedule> GetBackupScheduleAsync();

        /// <summary>
        /// Cleans up old backups based on retention policy
        /// </summary>
        /// <param name="retentionPolicy">Retention policy</param>
        /// <returns>Number of backups deleted</returns>
        Task<int> CleanupOldBackupsAsync(BackupRetentionPolicy retentionPolicy);

        /// <summary>
        /// Exports backup metadata
        /// </summary>
        /// <param name="exportPath">Path to save the metadata</param>
        /// <returns>Task representing the async operation</returns>
        Task ExportBackupMetadataAsync(string exportPath);

        /// <summary>
        /// Imports backup metadata
        /// </summary>
        /// <param name="metadataPath">Path to the metadata file</param>
        /// <returns>Task representing the async operation</returns>
        Task ImportBackupMetadataAsync(string metadataPath);

        /// <summary>
        /// Gets backup statistics
        /// </summary>
        /// <returns>Backup statistics</returns>
        Task<BackupStatistics> GetBackupStatisticsAsync();

        /// <summary>
        /// Compresses a backup file
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <param name="compressionLevel">Compression level (0-9)</param>
        /// <returns>Path to the compressed backup</returns>
        Task<string> CompressBackupAsync(string backupPath, int compressionLevel = 6);

        /// <summary>
        /// Decompresses a backup file
        /// </summary>
        /// <param name="compressedBackupPath">Path to the compressed backup</param>
        /// <param name="extractPath">Path to extract the backup</param>
        /// <returns>Path to the extracted backup</returns>
        Task<string> DecompressBackupAsync(string compressedBackupPath, string extractPath);

        /// <summary>
        /// Encrypts a backup file
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <param name="password">Encryption password</param>
        /// <returns>Path to the encrypted backup</returns>
        Task<string> EncryptBackupAsync(string backupPath, string password);

        /// <summary>
        /// Decrypts a backup file
        /// </summary>
        /// <param name="encryptedBackupPath">Path to the encrypted backup</param>
        /// <param name="password">Decryption password</param>
        /// <param name="outputPath">Path to save the decrypted backup</param>
        /// <returns>Path to the decrypted backup</returns>
        Task<string> DecryptBackupAsync(string encryptedBackupPath, string password, string outputPath);

        /// <summary>
        /// Tests the backup and restore process
        /// </summary>
        /// <param name="backupPath">Path to the backup to test</param>
        /// <returns>Test result</returns>
        Task<BackupTestResult> TestBackupRestoreAsync(string backupPath);

        /// <summary>
        /// Gets the default backup directory
        /// </summary>
        /// <returns>Default backup directory path</returns>
        string GetDefaultBackupDirectory();

        /// <summary>
        /// Sets the default backup directory
        /// </summary>
        /// <param name="directoryPath">New default backup directory</param>
        /// <returns>Task representing the async operation</returns>
        Task SetDefaultBackupDirectoryAsync(string directoryPath);

        /// <summary>
        /// Event fired when a backup operation starts
        /// </summary>
        event EventHandler<BackupEventArgs> BackupStarted;

        /// <summary>
        /// Event fired when a backup operation completes
        /// </summary>
        event EventHandler<BackupEventArgs> BackupCompleted;

        /// <summary>
        /// Event fired when a backup operation fails
        /// </summary>
        event EventHandler<BackupEventArgs> BackupFailed;

        /// <summary>
        /// Event fired to report backup progress
        /// </summary>
        event EventHandler<BackupProgressEventArgs> BackupProgress;
    }

    /// <summary>
    /// Backup verification result
    /// </summary>
    public class BackupVerificationResult
    {
        /// <summary>
        /// Whether the backup is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Verification errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Verification warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Backup file size
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Backup creation date
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// Database version when backup was created
        /// </summary>
        public string DatabaseVersion { get; set; }

        /// <summary>
        /// Application version when backup was created
        /// </summary>
        public string ApplicationVersion { get; set; }

        /// <summary>
        /// Checksum of the backup file
        /// </summary>
        public string Checksum { get; set; }
    }

    /// <summary>
    /// Backup schedule configuration
    /// </summary>
    public class BackupSchedule
    {
        /// <summary>
        /// Whether automatic backups are enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Backup frequency
        /// </summary>
        public BackupFrequency Frequency { get; set; }

        /// <summary>
        /// Time of day to perform backups (for daily/weekly schedules)
        /// </summary>
        public TimeSpan BackupTime { get; set; }

        /// <summary>
        /// Day of week for weekly backups
        /// </summary>
        public DayOfWeek? WeeklyBackupDay { get; set; }

        /// <summary>
        /// Day of month for monthly backups (1-31)
        /// </summary>
        public int? MonthlyBackupDay { get; set; }

        /// <summary>
        /// Custom cron expression for advanced scheduling
        /// </summary>
        public string CronExpression { get; set; }

        /// <summary>
        /// Maximum number of automatic backups to keep
        /// </summary>
        public int MaxBackupsToKeep { get; set; } = 10;

        /// <summary>
        /// Whether to compress automatic backups
        /// </summary>
        public bool CompressBackups { get; set; } = true;

        /// <summary>
        /// Whether to encrypt automatic backups
        /// </summary>
        public bool EncryptBackups { get; set; } = false;

        /// <summary>
        /// Encryption password for automatic backups
        /// </summary>
        public string EncryptionPassword { get; set; }
    }

    /// <summary>
    /// Backup frequency options
    /// </summary>
    public enum BackupFrequency
    {
        Hourly,
        Daily,
        Weekly,
        Monthly,
        Custom
    }

    /// <summary>
    /// Backup retention policy
    /// </summary>
    public class BackupRetentionPolicy
    {
        /// <summary>
        /// Maximum age of backups to keep
        /// </summary>
        public TimeSpan MaxAge { get; set; } = TimeSpan.FromDays(30);

        /// <summary>
        /// Maximum number of backups to keep
        /// </summary>
        public int MaxCount { get; set; } = 50;

        /// <summary>
        /// Maximum total size of all backups
        /// </summary>
        public long MaxTotalSize { get; set; } = 10L * 1024 * 1024 * 1024; // 10 GB

        /// <summary>
        /// Whether to keep at least one backup regardless of age/count
        /// </summary>
        public bool KeepAtLeastOne { get; set; } = true;

        /// <summary>
        /// Backup types to apply the policy to
        /// </summary>
        public BackupType ApplyToTypes { get; set; } = BackupType.Automatic;
    }

    /// <summary>
    /// Backup types
    /// </summary>
    [Flags]
    public enum BackupType
    {
        Manual = 1,
        Automatic = 2,
        Scheduled = 4,
        All = Manual | Automatic | Scheduled
    }

    /// <summary>
    /// Backup statistics
    /// </summary>
    public class BackupStatistics
    {
        /// <summary>
        /// Total number of backups
        /// </summary>
        public int TotalBackups { get; set; }

        /// <summary>
        /// Number of manual backups
        /// </summary>
        public int ManualBackups { get; set; }

        /// <summary>
        /// Number of automatic backups
        /// </summary>
        public int AutomaticBackups { get; set; }

        /// <summary>
        /// Total size of all backups
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// Average backup size
        /// </summary>
        public long AverageSize { get; set; }

        /// <summary>
        /// Date of the oldest backup
        /// </summary>
        public DateTime? OldestBackupDate { get; set; }

        /// <summary>
        /// Date of the newest backup
        /// </summary>
        public DateTime? NewestBackupDate { get; set; }

        /// <summary>
        /// Date of the last successful backup
        /// </summary>
        public DateTime? LastSuccessfulBackup { get; set; }

        /// <summary>
        /// Number of failed backup attempts
        /// </summary>
        public int FailedBackups { get; set; }

        /// <summary>
        /// Backup success rate (0-100)
        /// </summary>
        public double SuccessRate { get; set; }
    }

    /// <summary>
    /// Backup test result
    /// </summary>
    public class BackupTestResult
    {
        /// <summary>
        /// Whether the test was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Test error message if failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Time taken to restore the backup
        /// </summary>
        public TimeSpan RestoreTime { get; set; }

        /// <summary>
        /// Number of records restored
        /// </summary>
        public int RecordsRestored { get; set; }

        /// <summary>
        /// Test details
        /// </summary>
        public List<string> TestDetails { get; set; } = new List<string>();
    }

    /// <summary>
    /// Event arguments for backup events
    /// </summary>
    public class BackupEventArgs : EventArgs
    {
        /// <summary>
        /// Backup information
        /// </summary>
        public BackupInfo BackupInfo { get; set; }

        /// <summary>
        /// Error message if applicable
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Backup operation type
        /// </summary>
        public BackupOperationType OperationType { get; set; }
    }

    /// <summary>
    /// Event arguments for backup progress events
    /// </summary>
    public class BackupProgressEventArgs : EventArgs
    {
        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Current operation message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Estimated time remaining
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }

        /// <summary>
        /// Backup file path
        /// </summary>
        public string BackupPath { get; set; }
    }

    /// <summary>
    /// Backup operation types
    /// </summary>
    public enum BackupOperationType
    {
        Create,
        Restore,
        Verify,
        Delete,
        Compress,
        Decompress,
        Encrypt,
        Decrypt
    }
}
