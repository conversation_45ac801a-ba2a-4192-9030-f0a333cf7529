using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for validation service
    /// </summary>
    public interface IValidationService
    {
        /// <summary>
        /// Validates an entity and returns validation result
        /// </summary>
        /// <typeparam name="T">Type of entity to validate</typeparam>
        /// <param name="entity">Entity to validate</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateAsync<T>(T entity) where T : class;

        /// <summary>
        /// Validates a specific property of an entity
        /// </summary>
        /// <typeparam name="T">Type of entity</typeparam>
        /// <param name="entity">Entity containing the property</param>
        /// <param name="propertyName">Name of the property to validate</param>
        /// <param name="value">Value of the property</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidatePropertyAsync<T>(T entity, string propertyName, object value) where T : class;

        /// <summary>
        /// Checks if an entity is valid
        /// </summary>
        /// <typeparam name="T">Type of entity to validate</typeparam>
        /// <param name="entity">Entity to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        Task<bool> IsValidAsync<T>(T entity) where T : class;

        /// <summary>
        /// Gets validation errors for an entity
        /// </summary>
        /// <typeparam name="T">Type of entity to validate</typeparam>
        /// <param name="entity">Entity to validate</param>
        /// <returns>List of validation errors</returns>
        Task<List<ValidationError>> GetValidationErrorsAsync<T>(T entity) where T : class;

        /// <summary>
        /// Validates business rules for an entity
        /// </summary>
        /// <typeparam name="T">Type of entity to validate</typeparam>
        /// <param name="entity">Entity to validate</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateBusinessRulesAsync<T>(T entity) where T : class;

        /// <summary>
        /// Registers a custom validator for a specific type
        /// </summary>
        /// <typeparam name="T">Type to validate</typeparam>
        /// <param name="validator">Custom validator function</param>
        void RegisterCustomValidator<T>(Func<T, ValidationResult> validator) where T : class;
    }
}
