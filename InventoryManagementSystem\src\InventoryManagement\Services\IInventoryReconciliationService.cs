using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for managing inventory reconciliation
    /// </summary>
    public interface IInventoryReconciliationService
    {
        /// <summary>
        /// Creates a new inventory reconciliation session
        /// </summary>
        Task<InventoryReconciliation> CreateReconciliationAsync(string name, string description, string location, int userId);
        
        /// <summary>
        /// Gets an inventory reconciliation by ID
        /// </summary>
        Task<InventoryReconciliation> GetReconciliationAsync(int reconciliationId);
        
        /// <summary>
        /// Gets all inventory reconciliations, optionally filtered by status
        /// </summary>
        Task<List<InventoryReconciliation>> GetReconciliationsAsync(ReconciliationStatus? status = null);
        
        /// <summary>
        /// Gets reconciliations created by a specific user
        /// </summary>
        Task<List<InventoryReconciliation>> GetUserReconciliationsAsync(int userId);
        
        /// <summary>
        /// Updates the status of a reconciliation
        /// </summary>
        Task<bool> UpdateReconciliationStatusAsync(int reconciliationId, ReconciliationStatus status, int userId);
        
        /// <summary>
        /// Adds an inventory count entry to a reconciliation
        /// </summary>
        Task<InventoryCountEntry> AddCountEntryAsync(int reconciliationId, string sku, int actualQuantity, int countedById, string notes = null);
        
        /// <summary>
        /// Gets count entries for a reconciliation
        /// </summary>
        Task<List<InventoryCountEntry>> GetCountEntriesAsync(int reconciliationId);
        
        /// <summary>
        /// Gets discrepancies for a reconciliation
        /// </summary>
        Task<List<InventoryDiscrepancy>> GetDiscrepanciesAsync(int reconciliationId);
        
        /// <summary>
        /// Processes a count entry and creates a discrepancy if necessary
        /// </summary>
        Task<InventoryDiscrepancy> ProcessCountEntryAsync(int countEntryId);
        
        /// <summary>
        /// Resolves a discrepancy with the provided reason and action
        /// </summary>
        Task<bool> ResolveDiscrepancyAsync(int discrepancyId, DiscrepancyStatus status, string reason, string action, int resolvedById);
        
        /// <summary>
        /// Resolves a discrepancy with enhanced details including root cause analysis
        /// </summary>
        Task<bool> ResolveDiscrepancyWithDetailsAsync(int discrepancyId, DiscrepancyStatus status, DiscrepancyReason rootCause, string reason, string action, decimal financialImpact, string validationRules, int resolutionPriority, int resolvedById);
        
        /// <summary>
        /// Resolves multiple discrepancies in batch with the same resolution details
        /// </summary>
        Task<int> BatchResolveDiscrepanciesAsync(List<int> discrepancyIds, DiscrepancyStatus status, DiscrepancyReason rootCause, string reason, string action, int resolvedById);
        
        /// <summary>
        /// Completes a reconciliation by processing all count entries and updating status
        /// </summary>
        Task<ReconciliationResult> CompleteReconciliationAsync(int reconciliationId, int completedById, bool autoAdjustInventory = false);
        
        /// <summary>
        /// Processes a barcode scan during reconciliation
        /// </summary>
        Task<InventoryCountEntry> ProcessBarcodeScanAsync(int reconciliationId, string barcode, int quantity, int countedById);
        
        /// <summary>
        /// Generates a reconciliation report
        /// </summary>
        Task<ReconciliationReport> GenerateReconciliationReportAsync(int reconciliationId);
        
        /// <summary>
        /// Exports reconciliation data to CSV file
        /// </summary>
        Task<bool> ExportToCSVAsync(int reconciliationId, string filePath);
        
        /// <summary>
        /// Imports reconciliation data from CSV file
        /// </summary>
        Task<ImportResult> ImportFromCSVAsync(int reconciliationId, string filePath, int userId);
        
        /// <summary>
        /// Schedules a future reconciliation
        /// </summary>
        Task<InventoryReconciliation> ScheduleReconciliationAsync(string name, string description, string location, DateTime scheduledDate, int userId);
        
        /// <summary>
        /// Creates a partial reconciliation for selected items only
        /// </summary>
        Task<InventoryReconciliation> CreatePartialReconciliationAsync(string name, string description, string location, int userId);
        
        /// <summary>
        /// Gets previous reconciliations for comparison
        /// </summary>
        Task<List<InventoryReconciliation>> GetPreviousReconciliationsAsync(int currentReconciliationId, string location);
        
        /// <summary>
        /// Generates a comparison report between two reconciliations
        /// </summary>
        Task<ReconciliationReport> GenerateComparisonReportAsync(int currentReconciliationId, int previousReconciliationId);
    }
    
    /// <summary>
    /// Result of completing a reconciliation
    /// </summary>
    public class ReconciliationResult
    {
        /// <summary>
        /// Whether the reconciliation was completed successfully
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// Error message if not successful
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Total number of items counted
        /// </summary>
        public int TotalItemsCounted { get; set; }
        
        /// <summary>
        /// Total number of discrepancies found
        /// </summary>
        public int TotalDiscrepancies { get; set; }
        
        /// <summary>
        /// Number of discrepancies resolved by auto-adjustment
        /// </summary>
        public int DiscrepanciesAdjusted { get; set; }
        
        /// <summary>
        /// Number of discrepancies requiring manual resolution
        /// </summary>
        public int DiscrepanciesRequiringAction { get; set; }
        
        /// <summary>
        /// Total inventory value affected by discrepancies
        /// </summary>
        public decimal TotalValueAffected { get; set; }
    }
    
    /// <summary>
    /// Reconciliation report with summary information
    /// </summary>
    public class ReconciliationReport
    {
        /// <summary>
        /// Basic information about the reconciliation
        /// </summary>
        public InventoryReconciliation Reconciliation { get; set; }
        
        /// <summary>
        /// List of discrepancies found
        /// </summary>
        public List<InventoryDiscrepancy> Discrepancies { get; set; }
        
        /// <summary>
        /// Summary statistics
        /// </summary>
        public ReconciliationStatistics Statistics { get; set; }
    }
    
    /// <summary>
    /// Statistical information about a reconciliation
    /// </summary>
    public class ReconciliationStatistics
    {
        /// <summary>
        /// Total number of items counted
        /// </summary>
        public int TotalItemsCounted { get; set; }
        
        /// <summary>
        /// Total number of items with discrepancies
        /// </summary>
        public int ItemsWithDiscrepancies { get; set; }
        
        /// <summary>
        /// Percentage of items with discrepancies
        /// </summary>
        public decimal DiscrepancyPercentage { get; set; }
        
        /// <summary>
        /// Total positive discrepancies (excess)
        /// </summary>
        public int TotalExcess { get; set; }
        
        /// <summary>
        /// Total negative discrepancies (shortage)
        /// </summary>
        public int TotalShortage { get; set; }
        
        /// <summary>
        /// Value of excess inventory
        /// </summary>
        public decimal ExcessValue { get; set; }
        
        /// <summary>
        /// Value of shortage inventory
        /// </summary>
        public decimal ShortageValue { get; set; }
        
        /// <summary>
        /// Net value impact
        /// </summary>
        public decimal NetValueImpact { get; set; }
        
        /// <summary>
        /// Categories with most discrepancies
        /// </summary>
        public Dictionary<string, int> DiscrepanciesByCategory { get; set; }
        
        /// <summary>
        /// Locations with most discrepancies
        /// </summary>
        public Dictionary<string, int> DiscrepanciesByLocation { get; set; }
        
        /// <summary>
        /// Root causes of discrepancies
        /// </summary>
        public Dictionary<DiscrepancyReason, int> DiscrepanciesByRootCause { get; set; }
        
        /// <summary>
        /// Patterns of recurring discrepancies
        /// </summary>
        public List<DiscrepancyPattern> RecurringPatterns { get; set; }
        
        /// <summary>
        /// Historical comparison data (if available)
        /// </summary>
        public HistoricalComparisonData HistoricalComparison { get; set; }
    }



    /// <summary>
    /// Represents a pattern of recurring discrepancies
    /// </summary>
    public class DiscrepancyPattern
    {
        /// <summary>
        /// Pattern identifier
        /// </summary>
        public string PatternId { get; set; }
        
        /// <summary>
        /// Description of the pattern
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Root cause of the pattern
        /// </summary>
        public DiscrepancyReason RootCause { get; set; }
        
        /// <summary>
        /// Number of occurrences
        /// </summary>
        public int OccurrenceCount { get; set; }
        
        /// <summary>
        /// Affected items or categories
        /// </summary>
        public List<string> AffectedItems { get; set; }
        
        /// <summary>
        /// Suggested corrective action
        /// </summary>
        public string SuggestedAction { get; set; }
    }

    /// <summary>
    /// Historical comparison data between reconciliations
    /// </summary>
    public class HistoricalComparisonData
    {
        /// <summary>
        /// ID of the previous reconciliation used for comparison
        /// </summary>
        public int PreviousReconciliationId { get; set; }
        
        /// <summary>
        /// Date of the previous reconciliation
        /// </summary>
        public DateTime PreviousReconciliationDate { get; set; }
        
        /// <summary>
        /// Change in discrepancy count (positive means more discrepancies in current)
        /// </summary>
        public int DiscrepancyCountChange { get; set; }
        
        /// <summary>
        /// Change in discrepancy percentage
        /// </summary>
        public decimal DiscrepancyPercentageChange { get; set; }
        
        /// <summary>
        /// Change in financial impact
        /// </summary>
        public decimal FinancialImpactChange { get; set; }
        
        /// <summary>
        /// Items with recurring discrepancies
        /// </summary>
        public List<string> RecurringDiscrepancyItems { get; set; }
        
        /// <summary>
        /// Items with improved accuracy
        /// </summary>
        public List<string> ImprovedItems { get; set; }
        
        /// <summary>
        /// Items with worse accuracy
        /// </summary>
        public List<string> WorsenedItems { get; set; }
    }
}
