<Window x:Class="InventoryManagement.Views.SetupWizardWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Setup Wizard - Tom General Trading" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="WizardButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="120"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="StepHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="20" Background="{DynamicResource PrimaryHueMidBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <materialDesign:PackIcon Grid.Column="0" Kind="Settings" Width="32" Height="32" 
                                       Foreground="White" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Margin="15,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="Setup Wizard" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="Configure your system for optimal operation" FontSize="14" Foreground="White" Opacity="0.9"/>
                </StackPanel>
                
                <TextBlock Grid.Column="2" Text="{Binding StepText}" FontSize="14" Foreground="White" 
                           VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:Card>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                
                <!-- Step 1: Welcome -->
                <GroupBox Header="Step 1: Welcome" Visibility="{Binding Step1Visibility}" 
                          Style="{DynamicResource MaterialDesignGroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <TextBlock Style="{StaticResource StepHeaderStyle}" Text="Welcome to Tom General Trading!"/>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="14" Margin="0,0,0,20">
                            This setup wizard will help you configure your inventory management system for optimal operation.
                            The system is designed to work completely offline and be user-friendly.
                        </TextBlock>
                        
                        <TextBlock Text="What we'll set up:" FontWeight="Bold" Margin="0,0,0,10"/>
                        <StackPanel Margin="20,0,0,20">
                            <TextBlock Text="• Hardware devices (barcode scanners, receipt printers)" Margin="0,2"/>
                            <TextBlock Text="• Company information and branding" Margin="0,2"/>
                            <TextBlock Text="• Initial inventory categories" Margin="0,2"/>
                            <TextBlock Text="• User preferences and settings" Margin="0,2"/>
                            <TextBlock Text="• Backup and security options" Margin="0,2"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="Gray">
                            This process will take about 5-10 minutes. You can skip any step and configure it later from the Settings menu.
                        </TextBlock>
                    </StackPanel>
                </GroupBox>

                <!-- Step 2: Company Information -->
                <GroupBox Header="Step 2: Company Information" Visibility="{Binding Step2Visibility}" 
                          Style="{DynamicResource MaterialDesignGroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <TextBlock Style="{StaticResource StepHeaderStyle}" Text="Company Information"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBox Grid.Row="0" Grid.Column="0" Margin="0,0,10,15" 
                                     materialDesign:HintAssist.Hint="Company Name"
                                     Text="{Binding CompanyName}"
                                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>
                            
                            <TextBox Grid.Row="0" Grid.Column="1" Margin="10,0,0,15" 
                                     materialDesign:HintAssist.Hint="Phone Number"
                                     Text="{Binding CompanyPhone}"
                                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>
                            
                            <TextBox Grid.Row="1" Grid.Column="0" Margin="0,0,10,15" 
                                     materialDesign:HintAssist.Hint="Email Address"
                                     Text="{Binding CompanyEmail}"
                                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>
                            
                            <TextBox Grid.Row="1" Grid.Column="1" Margin="10,0,0,15" 
                                     materialDesign:HintAssist.Hint="Tax Number"
                                     Text="{Binding CompanyTaxNumber}"
                                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>
                            
                            <TextBox Grid.Row="2" Grid.ColumnSpan="2" Margin="0,0,0,15" 
                                     materialDesign:HintAssist.Hint="Address"
                                     Text="{Binding CompanyAddress}"
                                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Step 3: Hardware Setup -->
                <GroupBox Header="Step 3: Hardware Setup" Visibility="{Binding Step3Visibility}" 
                          Style="{DynamicResource MaterialDesignGroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <TextBlock Style="{StaticResource StepHeaderStyle}" Text="Hardware Configuration"/>
                        
                        <TextBlock Text="Barcode Scanner" FontWeight="Bold" Margin="0,0,0,10"/>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <ComboBox Grid.Column="0" materialDesign:HintAssist.Hint="Select Scanner"
                                      ItemsSource="{Binding AvailableScanners}"
                                      SelectedItem="{Binding SelectedScanner}"
                                      Style="{StaticResource MaterialDesignFloatingHintComboBox}"/>
                            
                            <Button Grid.Column="1" Content="Test" Margin="10,0,0,0" 
                                    Command="{Binding TestScannerCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
                        </Grid>
                        
                        <TextBlock Text="Receipt Printer" FontWeight="Bold" Margin="0,0,0,10"/>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <ComboBox Grid.Column="0" materialDesign:HintAssist.Hint="Select Printer"
                                      ItemsSource="{Binding AvailablePrinters}"
                                      SelectedItem="{Binding SelectedPrinter}"
                                      Style="{StaticResource MaterialDesignFloatingHintComboBox}"/>
                            
                            <Button Grid.Column="1" Content="Test" Margin="10,0,0,0" 
                                    Command="{Binding TestPrinterCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
                        </Grid>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="Gray">
                            Hardware devices will be automatically detected. If your device is not listed, 
                            make sure it's connected and powered on, then click the refresh button.
                        </TextBlock>
                    </StackPanel>
                </GroupBox>

                <!-- Step 4: Initial Categories -->
                <GroupBox Header="Step 4: Product Categories" Visibility="{Binding Step4Visibility}" 
                          Style="{DynamicResource MaterialDesignGroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <TextBlock Style="{StaticResource StepHeaderStyle}" Text="Initial Product Categories"/>
                        
                        <TextBlock TextWrapping="Wrap" Margin="0,0,0,15">
                            Create some basic categories to organize your inventory. You can add more categories later.
                        </TextBlock>
                        
                        <ItemsControl ItemsSource="{Binding InitialCategories}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" Margin="0,0,10,0"/>
                                        <TextBox Grid.Column="1" Text="{Binding Name}" 
                                                 Style="{StaticResource MaterialDesignTextBox}"/>
                                        <Button Grid.Column="2" Content="Remove" Margin="10,0,0,0"
                                                Command="{Binding DataContext.RemoveCategoryCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource MaterialDesignFlatButton}"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        
                        <Button Content="Add Category" Margin="0,10,0,0" 
                                Command="{Binding AddCategoryCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </GroupBox>

                <!-- Step 5: Completion -->
                <GroupBox Header="Step 5: Setup Complete" Visibility="{Binding Step5Visibility}" 
                          Style="{DynamicResource MaterialDesignGroupBox}" Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <TextBlock Style="{StaticResource StepHeaderStyle}" Text="Setup Complete!"/>
                        
                        <materialDesign:PackIcon Kind="CheckCircle" Width="64" Height="64" 
                                               Foreground="Green" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="14" TextAlignment="Center" Margin="0,0,0,20">
                            Your Tom General Trading Inventory Management System is now configured and ready to use!
                        </TextBlock>
                        
                        <TextBlock Text="What's Next:" FontWeight="Bold" Margin="0,0,0,10"/>
                        <StackPanel Margin="20,0,0,20">
                            <TextBlock Text="• Start adding your inventory items" Margin="0,2"/>
                            <TextBlock Text="• Add customer information" Margin="0,2"/>
                            <TextBlock Text="• Begin processing sales transactions" Margin="0,2"/>
                            <TextBlock Text="• Explore the reporting features" Margin="0,2"/>
                            <TextBlock Text="• Set up regular data backups" Margin="0,2"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="Gray" TextAlignment="Center">
                            You can always modify these settings later from the Settings menu.
                            Press F1 anytime for help and support.
                        </TextBlock>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Navigation Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Button Grid.Column="0" Content="Cancel" 
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource MaterialDesignFlatButton}"/>
                
                <Button Grid.Column="2" Content="Previous" 
                        Command="{Binding PreviousCommand}"
                        IsEnabled="{Binding CanGoPrevious}"
                        Style="{StaticResource WizardButtonStyle}"/>
                
                <Button Grid.Column="3" Content="Next" 
                        Command="{Binding NextCommand}"
                        IsEnabled="{Binding CanGoNext}"
                        Style="{StaticResource WizardButtonStyle}"/>
                
                <Button Grid.Column="4" Content="Finish" 
                        Command="{Binding FinishCommand}"
                        IsEnabled="{Binding CanFinish}"
                        Style="{StaticResource WizardButtonStyle}"
                        Background="Green"/>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
