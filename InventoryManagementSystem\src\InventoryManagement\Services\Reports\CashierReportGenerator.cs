using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
// Note: Data services are in InventoryManagement.Services namespace
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Report generator for cashier roles
    /// </summary>
    public class CashierReportGenerator
    {
        private readonly ISalesService _salesService;
        private readonly IUserService _userService;
        private readonly ILogger _logger;
        
        public CashierReportGenerator(
            ISalesService salesService,
            IUserService userService,
            ILogger logger)
        {
            _salesService = salesService;
            _userService = userService;
            _logger = logger;
        }
        
        /// <summary>
        /// Generates a sales report for a cashier for the specified date range
        /// </summary>
        public async Task<BaseReport> GetCashierSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating cashier sales report from {startDate} to {endDate}");
                
                // Get all sales in the date range
                var sales = await _salesService.GetSalesForPeriodAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Cashier Sales Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Summary of sales transactions by cashier"
                };
                
                // Group sales by cashier
                var salesByCashier = new Dictionary<int, decimal>();
                var transactionCountByCashier = new Dictionary<int, int>();
                
                foreach (var sale in sales)
                {
                    if (!salesByCashier.ContainsKey(sale.UserId))
                    {
                        salesByCashier[sale.UserId] = 0;
                        transactionCountByCashier[sale.UserId] = 0;
                    }
                    
                    salesByCashier[sale.UserId] += sale.TotalAmount;
                    transactionCountByCashier[sale.UserId]++;
                }
                
                // Add data points for each cashier
                foreach (var cashierId in salesByCashier.Keys)
                {
                    var user = await _userService.GetUserByIdAsync(cashierId);
                    var cashierName = user != null ? user.FullName : $"Cashier #{cashierId}";
                    
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "Cashier",
                        Value = salesByCashier[cashierId],
                        Metadata = new Dictionary<string, object>
                        {
                            { "CashierId", cashierId },
                            { "CashierName", cashierName },
                            { "TransactionCount", transactionCountByCashier[cashierId] },
                            { "AverageTransactionAmount", salesByCashier[cashierId] / transactionCountByCashier[cashierId] }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating cashier sales report: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Generates a report of sales by product for the specified date range
        /// </summary>
        public async Task<BaseReport> GetProductSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating product sales report from {startDate} to {endDate}");
                
                // Get all sales in the date range with product details
                var salesDetails = await _salesService.GetDetailedSalesForPeriodAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Product Sales Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Summary of sales by product"
                };
                
                // Group sales by product
                var salesByProduct = new Dictionary<int, SalesMetrics>();
                
                foreach (var detail in salesDetails)
                {
                    if (!salesByProduct.ContainsKey(detail.ItemId))
                    {
                        salesByProduct[detail.ItemId] = new SalesMetrics
                        {
                            ItemName = detail.ItemName,
                            Category = detail.Category
                        };
                    }
                    
                    salesByProduct[detail.ItemId].QuantitySold += detail.Quantity;
                    salesByProduct[detail.ItemId].TotalRevenue += detail.Quantity * detail.UnitPrice;
                }
                
                // Add data points for each product
                foreach (var productId in salesByProduct.Keys)
                {
                    var metrics = salesByProduct[productId];
                    
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = metrics.Category,
                        Value = metrics.TotalRevenue,
                        Metadata = new Dictionary<string, object>
                        {
                            { "ProductId", productId },
                            { "ProductName", metrics.ItemName },
                            { "QuantitySold", metrics.QuantitySold },
                            { "AveragePrice", metrics.QuantitySold > 0 ? metrics.TotalRevenue / metrics.QuantitySold : 0 }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating product sales report: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Generates a daily sales summary report for the specified date range
        /// </summary>
        public async Task<BaseReport> GetDailySalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating daily sales report from {startDate} to {endDate}");
                
                // Get all sales in the date range
                var sales = await _salesService.GetSalesForPeriodAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Daily Sales Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Summary of sales by day"
                };
                
                // Group sales by day
                var salesByDay = new Dictionary<DateTime, DailySalesMetrics>();
                
                foreach (var sale in sales)
                {
                    var saleDate = sale.TransactionDate.Date;
                    
                    if (!salesByDay.ContainsKey(saleDate))
                    {
                        salesByDay[saleDate] = new DailySalesMetrics();
                    }
                    
                    salesByDay[saleDate].TotalSales += sale.TotalAmount;
                    salesByDay[saleDate].TransactionCount++;
                    
                    if (sale.TotalAmount > salesByDay[saleDate].LargestTransaction)
                    {
                        salesByDay[saleDate].LargestTransaction = sale.TotalAmount;
                    }
                }
                
                // Add data points for each day
                foreach (var date in salesByDay.Keys)
                {
                    var metrics = salesByDay[date];
                    
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "Daily",
                        Value = metrics.TotalSales,
                        Date = date,
                        Metadata = new Dictionary<string, object>
                        {
                            { "TransactionCount", metrics.TransactionCount },
                            { "AverageTransactionAmount", metrics.TransactionCount > 0 ? metrics.TotalSales / metrics.TransactionCount : 0 },
                            { "LargestTransaction", metrics.LargestTransaction }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating daily sales report: {ex.Message}");
                throw;
            }
        }
        
        #region Helper Classes
        
        private class SalesMetrics
        {
            public string ItemName { get; set; }
            public string Category { get; set; }
            public int QuantitySold { get; set; }
            public decimal TotalRevenue { get; set; }
        }
        
        private class DailySalesMetrics
        {
            public decimal TotalSales { get; set; }
            public int TransactionCount { get; set; }
            public decimal LargestTransaction { get; set; }
        }
        
        #endregion
    }
}
