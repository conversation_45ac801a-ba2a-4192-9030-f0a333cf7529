using InventoryManagement.DataAccess;
using InventoryManagement.Infrastructure.DependencyInjection;
using InventoryManagement.Infrastructure.ErrorHandling;
using InventoryManagement.Infrastructure.MVVM;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;

namespace InventoryManagement.Infrastructure.OfflineMode
{
    /// <summary>
    /// Extensions for configuring the service collection with services optimized for offline operation
    /// </summary>
    public static class OfflineServiceCollectionExtensions
    {
        /// <summary>
        /// Adds all core services with proper lifetimes optimized for offline operation
        /// </summary>
        public static IServiceCollection AddOfflineCoreServices(this IServiceCollection services, IConfiguration configuration)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));
            if (configuration == null) throw new ArgumentNullException(nameof(configuration));
            
            // Add strongly-typed settings optimized for offline use
            var offlineSettings = OfflineAppSettings.Load(configuration);
            services.AddSingleton(offlineSettings);
            services.AddSingleton(offlineSettings.Database);
            services.AddSingleton(offlineSettings.Application);
            services.AddSingleton(offlineSettings.Security);
            services.AddSingleton(offlineSettings.Backup);

            // Add pure offline configuration
            PureOfflineConfiguration.ConfigurePureOfflineServices(services, configuration);
            
            // Configure database access
            ConfigureOfflineDatabaseAccess(services, offlineSettings.Database);
            
            // Register core services optimized for offline operation
            RegisterOfflineDataServices(services);
            RegisterOfflineBusinessServices(services);
            RegisterOfflineInfrastructureServices(services);
            RegisterOfflineUIServices(services);
            
            return services;
        }

        private static void ConfigureOfflineDatabaseAccess(IServiceCollection services, OfflineDatabaseSettings dbSettings)
        {
            // Add PostgreSQL database context with configuration optimized for offline use
            services.AddDbContext<ApplicationDbContext>(options => 
                options.UseNpgsql(dbSettings.GetConnectionString(), npgsqlOptions =>
                {
                    // Enable retry on failure for better resilience with offline database
                    npgsqlOptions.EnableRetryOnFailure(3, TimeSpan.FromSeconds(3), null);
                    npgsqlOptions.CommandTimeout(dbSettings.CommandTimeout);
                    
                    // Migration history table configuration
                    npgsqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "public");
                }));
            
            // Add database diagnostic/management services for offline mode
            services.AddSingleton<IOfflineDatabaseManager, OfflineDatabaseManager>();
        }

        private static void RegisterOfflineDataServices(IServiceCollection services)
        {
            // Repository pattern implementations - Scoped lifetime for database operations
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IItemRepository, ItemRepository>();
            services.AddScoped<IInventoryRepository, InventoryRepository>();
            services.AddScoped<ITransactionRepository, TransactionRepository>();
            services.AddScoped<IDefectiveItemRepository, DefectiveItemRepository>();
            services.AddScoped<IItemExchangeRepository, ItemExchangeRepository>();
            services.AddScoped<ISupplierRepository, SupplierRepository>();
            
            // Unit of Work pattern for transaction management
            services.AddScoped<IUnitOfWork, UnitOfWork>();
        }

        private static void RegisterOfflineBusinessServices(IServiceCollection services)
        {
            // CONSOLIDATED SERVICES - Using Smart Consolidation Approach
            // These services merge functionality from multiple similar services while preserving all methods

            // Core consolidated services
            services.AddScoped<IAuthService, ConsolidatedAuthService>();
            services.AddScoped<Infrastructure.Authentication.IAuthService, ConsolidatedAuthService>();
            services.AddScoped<INotificationService, ConsolidatedNotificationService>();
            services.AddScoped<IDashboardService, ConsolidatedDashboardService>();

            // Register consolidated services as their own types for direct injection
            services.AddScoped<ConsolidatedAuthService>();
            services.AddScoped<ConsolidatedNotificationService>();
            services.AddScoped<ConsolidatedDashboardService>();

            // Remaining business services (to be consolidated in future phases)
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IItemService, ItemService>();
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<ITransactionService, TransactionService>();
            services.AddScoped<IDefectiveItemService, DefectiveItemService>();
            services.AddScoped<IItemExchangeService, ItemExchangeService>();
            services.AddScoped<ISalesService, OfflineSalesService>();

            // Additional business services
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<IBarcodeService, BarcodeService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<IFinancialService, FinancialService>();

            // Services required by consolidated services
            services.AddScoped<IUserPreferencesService, UserPreferencesService>();
            services.AddScoped<IOfflineReportingService, OfflineReportingService>();
        }

        private static void RegisterOfflineInfrastructureServices(IServiceCollection services)
        {
            // PHASE 2: CONSOLIDATED INFRASTRUCTURE SERVICES
            // Using Smart Consolidation Approach - 6 Core Infrastructure Areas

            // 1. Data Access Consolidation
            services.AddScoped<Infrastructure.Consolidated.DataAccess.ConsolidatedDataService>();

            // 2. Security Consolidation
            services.AddScoped<Infrastructure.Consolidated.Security.ConsolidatedSecurityService>();

            // 3. Configuration Consolidation
            services.AddSingleton<Infrastructure.Consolidated.Configuration.ConsolidatedConfigurationService>();

            // 4. UI Consolidation
            services.AddScoped<Infrastructure.Consolidated.UI.ConsolidatedUIService>();

            // 5. Cross-Cutting Concerns Consolidation
            services.AddScoped<Infrastructure.Consolidated.CrossCutting.ConsolidatedCrossCuttingService>();

            // 6. Offline Operations Consolidation
            services.AddSingleton<Infrastructure.Consolidated.Offline.ConsolidatedOfflineService>();

            // Essential infrastructure services (kept for dependencies)
            services.AddSingleton<ICacheService, OfflineMemoryCacheService>();
            services.AddSingleton<ILocalizationService, OfflineLocalizationService>();
            services.AddSingleton<IAuditService, OfflineAuditService>();

            // Register our consolidated EventBus from Events namespace
            services.AddSingleton<Events.EventBus>();
            
            // Simple database backup service for offline mode
            services.AddScoped<IDatabaseBackupService, OfflineDatabaseBackupService>();
            
            // Local reporting services (no external dependencies)
            services.AddScoped<IReportingService, OfflineReportingService>();
            services.AddScoped<IBasementManagerReportGenerator, BasementManagerReportGenerator>();
            services.AddScoped<ICashierReportGenerator, CashierReportGenerator>();

            // Hardware services for POS devices
            services.AddScoped<IBarcodeHardwareService, BarcodeHardwareService>();
            services.AddScoped<IPrinterHardwareService, PrinterHardwareService>();

            // Additional infrastructure services
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<IUserFriendlyMessageService, UserFriendlyMessageService>();
        }

        private static void RegisterOfflineUIServices(IServiceCollection services)
        {
            // UI services optimized for offline mode
            services.AddSingleton<IDialogService, DialogService>();
            services.AddScoped<INavigationService, NavigationService>();
            services.AddScoped<INotificationService, OfflineNotificationService>();
            
            // Register ViewModels as transient (created fresh each time)
            RegisterViewModels(services);
        }

        private static void RegisterViewModels(IServiceCollection services)
        {
            // CONSOLIDATED VIEWMODELS - Using Smart Consolidation Approach
            // These ViewModels merge functionality from multiple similar ViewModels using composition patterns

            // Main window ViewModel
            services.AddTransient<MainWindowViewModel>();

            // Login ViewModel
            services.AddTransient<LoginViewModel>();

            // CONSOLIDATED Dashboard ViewModel - Replaces multiple dashboard ViewModels
            services.AddTransient<ConsolidatedDashboardViewModel>();

            // Register consolidated ViewModel for all dashboard interfaces
            services.AddTransient<ComprehensiveMainDashboardViewModel>(provider =>
                provider.GetRequiredService<ConsolidatedDashboardViewModel>());

            // Keep original ViewModels for backward compatibility (will be phased out)
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<SalesDashboardViewModel>();
            services.AddTransient<ProductsDashboardViewModel>();
            services.AddTransient<InventoryDashboardViewModel>();
            services.AddTransient<TransfersDashboardViewModel>();
            services.AddTransient<DefectiveItemsDashboardViewModel>();
            services.AddTransient<ExchangeDashboardViewModel>();
            services.AddTransient<SettingsDashboardViewModel>();

            // Management ViewModels (to be consolidated in future phases)
            services.AddTransient<UserManagementViewModel>();
            services.AddTransient<ItemManagementViewModel>();
            services.AddTransient<InventoryManagementViewModel>();
            services.AddTransient<SalesManagementViewModel>();

            // Reports ViewModels (to be consolidated in future phases)
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SalesReportViewModel>();
            services.AddTransient<InventoryReportViewModel>();
            services.AddTransient<DefectiveItemsReportViewModel>();

            // Settings ViewModels (to be consolidated in future phases)
            services.AddTransient<BackupRestoreViewModel>();
            services.AddTransient<DatabaseSettingsViewModel>();
            services.AddTransient<UserPreferencesViewModel>();
        }
    }

    /// <summary>
    /// Simple event bus for loosely coupled communication between components
    /// </summary>
    public class EventBus : IEventBus
    {
        private readonly Dictionary<Type, List<Delegate>> _handlers = new Dictionary<Type, List<Delegate>>();

        public void Subscribe<TEvent>(Action<TEvent> handler)
        {
            var eventType = typeof(TEvent);

            if (!_handlers.ContainsKey(eventType))
            {
                _handlers[eventType] = new List<Delegate>();
            }

            _handlers[eventType].Add(handler);
        }

        public void Unsubscribe<TEvent>(Action<TEvent> handler)
        {
            var eventType = typeof(TEvent);

            if (_handlers.ContainsKey(eventType))
            {
                _handlers[eventType].Remove(handler);
            }
        }

        public void Publish<TEvent>(TEvent eventData)
        {
            var eventType = typeof(TEvent);

            if (_handlers.ContainsKey(eventType))
            {
                foreach (var handler in _handlers[eventType])
                {
                    ((Action<TEvent>)handler)(eventData);
                }
            }
        }
    }

    /// <summary>
    /// Interface for the event bus
    /// </summary>
    public interface IEventBus
    {
        void Subscribe<TEvent>(Action<TEvent> handler);
        void Unsubscribe<TEvent>(Action<TEvent> handler);
        void Publish<TEvent>(TEvent eventData);
    }
}
