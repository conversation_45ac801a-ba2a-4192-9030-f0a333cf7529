using InventoryManagement.Infrastructure.Configuration;
using InventoryManagement.Infrastructure.OfflineMode;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Consolidated.Configuration
{
    /// <summary>
    /// Consolidated configuration service that merges functionality from multiple configuration services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - Application configuration
    /// - Database settings
    /// - Security settings
    /// - UI settings
    /// - Offline configuration
    /// - User preferences
    /// </summary>
    public class ConsolidatedConfigurationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ConsolidatedConfigurationService> _logger;
        private readonly string _configurationPath;
        private readonly Dictionary<string, object> _runtimeSettings = new Dictionary<string, object>();

        public ConsolidatedConfigurationService(
            IConfiguration configuration,
            ILogger<ConsolidatedConfigurationService> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configurationPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
        }

        #region Application Configuration

        /// <summary>
        /// Gets application configuration settings
        /// </summary>
        /// <returns>Application configuration</returns>
        public AppConfiguration GetAppConfiguration()
        {
            try
            {
                var config = new AppConfiguration
                {
                    ApplicationName = _configuration["Application:Name"] ?? "Tom General Trading Inventory System",
                    Version = _configuration["Application:Version"] ?? "1.0.0",
                    Environment = _configuration["Application:Environment"] ?? "Production",
                    IsOfflineMode = bool.Parse(_configuration["Application:OfflineMode"] ?? "true"),
                    DataDirectory = _configuration["Application:DataDirectory"] ?? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "TomGeneralTrading"),
                    LogLevel = _configuration["Logging:LogLevel:Default"] ?? "Information",
                    EnableAuditLogging = bool.Parse(_configuration["Application:EnableAuditLogging"] ?? "true"),
                    SessionTimeoutMinutes = int.Parse(_configuration["Application:SessionTimeoutMinutes"] ?? "60")
                };

                _logger.LogDebug("Application configuration loaded successfully");
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading application configuration");
                throw;
            }
        }

        /// <summary>
        /// Updates application configuration
        /// </summary>
        /// <param name="config">Updated configuration</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UpdateAppConfigurationAsync(AppConfiguration config)
        {
            try
            {
                _logger.LogInformation("Updating application configuration");

                // Update runtime settings
                _runtimeSettings["Application:Name"] = config.ApplicationName;
                _runtimeSettings["Application:Version"] = config.Version;
                _runtimeSettings["Application:Environment"] = config.Environment;
                _runtimeSettings["Application:OfflineMode"] = config.IsOfflineMode.ToString();
                _runtimeSettings["Application:DataDirectory"] = config.DataDirectory;
                _runtimeSettings["Logging:LogLevel:Default"] = config.LogLevel;
                _runtimeSettings["Application:EnableAuditLogging"] = config.EnableAuditLogging.ToString();
                _runtimeSettings["Application:SessionTimeoutMinutes"] = config.SessionTimeoutMinutes.ToString();

                await SaveConfigurationAsync();
                
                _logger.LogInformation("Application configuration updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating application configuration");
                throw;
            }
        }

        #endregion

        #region Database Configuration

        /// <summary>
        /// Gets database configuration settings
        /// </summary>
        /// <returns>Database settings</returns>
        public DatabaseSettings GetDatabaseSettings()
        {
            try
            {
                var settings = new DatabaseSettings
                {
                    ConnectionString = _configuration.GetConnectionString("DefaultConnection") ?? 
                        "Host=localhost;Database=TomGeneralTradingDB;Username=postgres;Password=****",
                    Provider = _configuration["Database:Provider"] ?? "PostgreSQL",
                    CommandTimeout = int.Parse(_configuration["Database:CommandTimeout"] ?? "30"),
                    EnableRetryOnFailure = bool.Parse(_configuration["Database:EnableRetryOnFailure"] ?? "true"),
                    MaxRetryCount = int.Parse(_configuration["Database:MaxRetryCount"] ?? "3"),
                    MaxRetryDelay = TimeSpan.FromSeconds(int.Parse(_configuration["Database:MaxRetryDelaySeconds"] ?? "30")),
                    EnableSensitiveDataLogging = bool.Parse(_configuration["Database:EnableSensitiveDataLogging"] ?? "false"),
                    EnableDetailedErrors = bool.Parse(_configuration["Database:EnableDetailedErrors"] ?? "true")
                };

                _logger.LogDebug("Database settings loaded successfully");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading database settings");
                throw;
            }
        }

        /// <summary>
        /// Updates database configuration
        /// </summary>
        /// <param name="settings">Updated database settings</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UpdateDatabaseSettingsAsync(DatabaseSettings settings)
        {
            try
            {
                _logger.LogInformation("Updating database settings");

                _runtimeSettings["ConnectionStrings:DefaultConnection"] = settings.ConnectionString;
                _runtimeSettings["Database:Provider"] = settings.Provider;
                _runtimeSettings["Database:CommandTimeout"] = settings.CommandTimeout.ToString();
                _runtimeSettings["Database:EnableRetryOnFailure"] = settings.EnableRetryOnFailure.ToString();
                _runtimeSettings["Database:MaxRetryCount"] = settings.MaxRetryCount.ToString();
                _runtimeSettings["Database:MaxRetryDelaySeconds"] = settings.MaxRetryDelay.TotalSeconds.ToString();
                _runtimeSettings["Database:EnableSensitiveDataLogging"] = settings.EnableSensitiveDataLogging.ToString();
                _runtimeSettings["Database:EnableDetailedErrors"] = settings.EnableDetailedErrors.ToString();

                await SaveConfigurationAsync();
                
                _logger.LogInformation("Database settings updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating database settings");
                throw;
            }
        }

        #endregion

        #region Security Configuration

        /// <summary>
        /// Gets security configuration settings
        /// </summary>
        /// <returns>Security settings</returns>
        public SecuritySettings GetSecuritySettings()
        {
            try
            {
                var settings = new SecuritySettings
                {
                    PasswordMinLength = int.Parse(_configuration["Security:PasswordMinLength"] ?? "8"),
                    PasswordRequireUppercase = bool.Parse(_configuration["Security:PasswordRequireUppercase"] ?? "true"),
                    PasswordRequireLowercase = bool.Parse(_configuration["Security:PasswordRequireLowercase"] ?? "true"),
                    PasswordRequireDigit = bool.Parse(_configuration["Security:PasswordRequireDigit"] ?? "true"),
                    PasswordRequireSpecialChar = bool.Parse(_configuration["Security:PasswordRequireSpecialChar"] ?? "false"),
                    MaxLoginAttempts = int.Parse(_configuration["Security:MaxLoginAttempts"] ?? "5"),
                    LockoutDurationMinutes = int.Parse(_configuration["Security:LockoutDurationMinutes"] ?? "15"),
                    SessionTimeoutMinutes = int.Parse(_configuration["Security:SessionTimeoutMinutes"] ?? "60"),
                    EnableDataEncryption = bool.Parse(_configuration["Security:EnableDataEncryption"] ?? "true"),
                    EncryptionKey = _configuration["Security:EncryptionKey"] ?? GenerateDefaultEncryptionKey()
                };

                _logger.LogDebug("Security settings loaded successfully");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading security settings");
                throw;
            }
        }

        #endregion

        #region UI Configuration

        /// <summary>
        /// Gets UI configuration settings
        /// </summary>
        /// <returns>UI settings</returns>
        public UISettings GetUISettings()
        {
            try
            {
                var settings = new UISettings
                {
                    Theme = _configuration["UI:Theme"] ?? "Light",
                    Language = _configuration["UI:Language"] ?? "en-US",
                    DateFormat = _configuration["UI:DateFormat"] ?? "MM/dd/yyyy",
                    TimeFormat = _configuration["UI:TimeFormat"] ?? "HH:mm:ss",
                    CurrencySymbol = _configuration["UI:CurrencySymbol"] ?? "$",
                    DecimalPlaces = int.Parse(_configuration["UI:DecimalPlaces"] ?? "2"),
                    PageSize = int.Parse(_configuration["UI:PageSize"] ?? "25"),
                    EnableAnimations = bool.Parse(_configuration["UI:EnableAnimations"] ?? "true"),
                    ShowTooltips = bool.Parse(_configuration["UI:ShowTooltips"] ?? "true"),
                    AutoSaveInterval = int.Parse(_configuration["UI:AutoSaveIntervalSeconds"] ?? "300")
                };

                _logger.LogDebug("UI settings loaded successfully");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading UI settings");
                throw;
            }
        }

        #endregion

        #region Offline Configuration

        /// <summary>
        /// Gets offline mode configuration
        /// </summary>
        /// <returns>Offline configuration</returns>
        public OfflineConfiguration GetOfflineConfiguration()
        {
            try
            {
                var config = new OfflineConfiguration
                {
                    IsOfflineMode = bool.Parse(_configuration["Offline:IsOfflineMode"] ?? "true"),
                    DataSyncEnabled = bool.Parse(_configuration["Offline:DataSyncEnabled"] ?? "false"),
                    CacheExpirationHours = int.Parse(_configuration["Offline:CacheExpirationHours"] ?? "24"),
                    MaxOfflineTransactions = int.Parse(_configuration["Offline:MaxOfflineTransactions"] ?? "1000"),
                    EnableOfflineReports = bool.Parse(_configuration["Offline:EnableOfflineReports"] ?? "true"),
                    OfflineDataRetentionDays = int.Parse(_configuration["Offline:OfflineDataRetentionDays"] ?? "90"),
                    EnableOfflineBackup = bool.Parse(_configuration["Offline:EnableOfflineBackup"] ?? "true"),
                    BackupIntervalHours = int.Parse(_configuration["Offline:BackupIntervalHours"] ?? "24")
                };

                _logger.LogDebug("Offline configuration loaded successfully");
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading offline configuration");
                throw;
            }
        }

        #endregion

        #region Configuration Management

        /// <summary>
        /// Gets a configuration value by key
        /// </summary>
        /// <param name="key">Configuration key</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>Configuration value</returns>
        public string GetConfigurationValue(string key, string defaultValue = null)
        {
            try
            {
                // Check runtime settings first
                if (_runtimeSettings.TryGetValue(key, out var runtimeValue))
                {
                    return runtimeValue?.ToString();
                }

                // Check configuration
                var value = _configuration[key];
                return value ?? defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting configuration value for key: {Key}", key);
                return defaultValue;
            }
        }

        /// <summary>
        /// Sets a configuration value at runtime
        /// </summary>
        /// <param name="key">Configuration key</param>
        /// <param name="value">Configuration value</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SetConfigurationValueAsync(string key, string value)
        {
            try
            {
                _runtimeSettings[key] = value;
                await SaveConfigurationAsync();
                
                _logger.LogDebug("Configuration value set for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting configuration value for key: {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Reloads configuration from file
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task ReloadConfigurationAsync()
        {
            try
            {
                _logger.LogInformation("Reloading configuration from file");
                
                // Clear runtime settings
                _runtimeSettings.Clear();
                
                // Configuration will be automatically reloaded by the framework
                await Task.CompletedTask;
                
                _logger.LogInformation("Configuration reloaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reloading configuration");
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task SaveConfigurationAsync()
        {
            try
            {
                // For offline mode, we'll save runtime settings to a separate file
                var runtimeConfigPath = Path.Combine(Path.GetDirectoryName(_configurationPath), "runtime-settings.json");
                
                var json = JsonSerializer.Serialize(_runtimeSettings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                await File.WriteAllTextAsync(runtimeConfigPath, json);
                
                _logger.LogDebug("Runtime configuration saved to: {Path}", runtimeConfigPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving configuration");
                throw;
            }
        }

        private string GenerateDefaultEncryptionKey()
        {
            // Generate a default encryption key for offline mode
            // In production, this should be properly managed
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("TomGeneralTradingDefaultKey2024"));
        }

        #endregion
    }
}
