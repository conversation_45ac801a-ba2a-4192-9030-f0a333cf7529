using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using InventoryManagement.Services.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Unified interface for sales-related operations
    /// Combines functionality from multiple sales service interfaces
    /// </summary>
    public interface ISalesService
    {
        // Basic Sales Operations
        /// <summary>
        /// Gets all sales transactions for a specific period
        /// </summary>
        Task<List<SalesTransaction>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Gets detailed sales data including item information for a specific period
        /// </summary>
        Task<List<SalesDetail>> GetDetailedSalesForPeriodAsync(DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Gets sales transactions for a specific cashier in a period
        /// </summary>
        Task<List<SalesTransaction>> GetSalesByCashierAsync(int cashierId, DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Gets sales transactions for a specific location in a period
        /// </summary>
        Task<List<SalesTransaction>> GetSalesByLocationAsync(int locationId, DateTime startDate, DateTime endDate);

        // Sales by Date Range
        /// <summary>
        /// Gets sales by date range
        /// </summary>
        Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate);

        // Sales Summary Operations
        /// <summary>
        /// Gets sales summary for a specific period
        /// </summary>
        Task<SalesSummary> GetSalesSummaryAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        // Transaction Operations
        /// <summary>
        /// Creates a new sale transaction
        /// </summary>
        Task<(bool Success, string Message, Transaction Transaction)> CreateSaleAsync(
            List<SaleItem> items, 
            PaymentMethod paymentMethod, 
            decimal amountPaid, 
            string customerName = null, 
            string customerPhone = null, 
            string notes = null, 
            int userId = 0);

        /// <summary>
        /// Voids a transaction
        /// </summary>
        Task<(bool Success, string Message)> VoidTransactionAsync(
            int transactionId, 
            string reason, 
            int userId);

        // Analytics and Reporting
        /// <summary>
        /// Get sales performance metrics for a specific period
        /// </summary>
        Task<SalesPerformanceMetrics> GetSalesPerformanceAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Get customer analytics
        /// </summary>
        Task<(int UniqueCustomers, int NewCustomers, int ReturningCustomers, List<TopCustomer> TopCustomers)> GetCustomerAnalyticsAsync(DateTime fromDate, DateTime toDate, int count = 10);

        /// <summary>
        /// Generate daily sales summary
        /// </summary>
        Task<DailySales> GenerateDailySalesSummaryAsync(DateTime date, List<int>? locationIds = null);

        /// <summary>
        /// Generate comprehensive sales report
        /// </summary>
        Task<Models.SalesReport> GenerateSalesReportAsync(Models.SalesReportParameters parameters);

        /// <summary>
        /// Export sales report to specified format
        /// </summary>
        Task<byte[]> ExportSalesReportAsync(Models.SalesReport report, Models.SalesReportFormat format);
    }

    /// <summary>
    /// Implementation of the unified sales service
    /// Delegates to appropriate specialized services
    /// </summary>
    public class UnifiedSalesService : ISalesService
    {
        private readonly ISalesDataService _dataSalesService;
        private readonly Reports.ISalesReportService _reportService;
        private readonly IPosService _posService;
        private readonly IOfflineReportingService _offlineReportingService;

        public UnifiedSalesService(
            ISalesDataService dataSalesService,
            Reports.ISalesReportService reportService,
            IPosService posService,
            IOfflineReportingService offlineReportingService)
        {
            _dataSalesService = dataSalesService;
            _reportService = reportService;
            _posService = posService;
            _offlineReportingService = offlineReportingService;
        }

        // Delegate to Data.ISalesService
        public Task<List<SalesTransaction>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate)
            => _dataSalesService.GetSalesForPeriodAsync(startDate, endDate);

        public Task<List<SalesDetail>> GetDetailedSalesForPeriodAsync(DateTime startDate, DateTime endDate)
            => _dataSalesService.GetDetailedSalesForPeriodAsync(startDate, endDate);

        public Task<List<SalesTransaction>> GetSalesByCashierAsync(int cashierId, DateTime startDate, DateTime endDate)
            => _dataSalesService.GetSalesByCashierAsync(cashierId, startDate, endDate);

        public Task<List<SalesTransaction>> GetSalesByLocationAsync(int locationId, DateTime startDate, DateTime endDate)
            => _dataSalesService.GetSalesByLocationAsync(locationId, startDate, endDate);

        // Delegate to Reports.ISalesReportService
        public Task<DailySales> GenerateDailySalesSummaryAsync(DateTime date, List<int>? locationIds = null)
            => _reportService.GenerateDailySalesSummaryAsync(date, locationIds);

        public Task<Models.SalesReport> GenerateSalesReportAsync(Models.SalesReportParameters parameters)
            => _reportService.GenerateSalesReportAsync(parameters);

        public Task<byte[]> ExportSalesReportAsync(Models.SalesReport report, Models.SalesReportFormat format)
            => _reportService.ExportSalesReportAsync(report, format);

        public Task<(int UniqueCustomers, int NewCustomers, int ReturningCustomers, List<TopCustomer> TopCustomers)> GetCustomerAnalyticsAsync(DateTime fromDate, DateTime toDate, int count = 10)
            => _reportService.GetCustomerAnalyticsAsync(fromDate, toDate, count);

        // Delegate to IPosService
        public Task<(bool Success, string Message, Transaction Transaction)> CreateSaleAsync(
            List<SaleItem> items, 
            PaymentMethod paymentMethod, 
            decimal amountPaid, 
            string customerName = null, 
            string customerPhone = null, 
            string notes = null, 
            int userId = 0)
            => _posService.CreateSaleAsync(items, paymentMethod, amountPaid, customerName, customerPhone, notes, userId);

        public Task<(bool Success, string Message)> VoidTransactionAsync(int transactionId, string reason, int userId)
            => _posService.VoidTransactionAsync(transactionId, reason, userId);

        // Delegate to IOfflineReportingService
        public Task<SalesSummary> GetSalesSummaryAsync(DateTime startDate, DateTime endDate, int? locationId = null)
            => _offlineReportingService.GetSalesSummaryAsync(startDate, endDate, locationId);

        // Placeholder implementations for missing methods
        public async Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            // This would need to be implemented based on your Sale model
            // For now, return empty collection
            return new List<Sale>();
        }

        public async Task<SalesPerformanceMetrics> GetSalesPerformanceAsync(DateTime startDate, DateTime endDate, int? locationId = null)
        {
            // This would need to be implemented based on your analytics requirements
            // For now, return default metrics
            return new SalesPerformanceMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSales = 0,
                TotalTransactions = 0,
                AverageTransactionValue = 0,
                YearOverYearGrowth = 0
            };
        }
    }
}
