using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for report export services
    /// </summary>
    public interface IReportExporter
    {
        /// <summary>
        /// Exports a report to PDF format
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>PDF file as byte array</returns>
        Task<byte[]> ExportToPdfAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to Excel format
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>Excel file as byte array</returns>
        Task<byte[]> ExportToExcelAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to CSV format
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>CSV file as byte array</returns>
        Task<byte[]> ExportToCsvAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to HTML format
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>HTML content as string</returns>
        Task<string> ExportToHtmlAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to XML format
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>XML content as string</returns>
        Task<string> ExportToXmlAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to JSON format
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>JSON content as string</returns>
        Task<string> ExportToJsonAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to a file
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="filePath">Path where to save the file</param>
        /// <param name="format">Export format</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>Task representing the async operation</returns>
        Task ExportToFileAsync(object reportData, string filePath, ExportFormat format, ReportFormatting formatting = null);

        /// <summary>
        /// Exports a report to a stream
        /// </summary>
        /// <param name="reportData">Report data to export</param>
        /// <param name="stream">Stream to write to</param>
        /// <param name="format">Export format</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>Task representing the async operation</returns>
        Task ExportToStreamAsync(object reportData, Stream stream, ExportFormat format, ReportFormatting formatting = null);

        /// <summary>
        /// Creates a preview of the report
        /// </summary>
        /// <param name="reportData">Report data to preview</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>Report preview</returns>
        Task<ReportPreview> CreatePreviewAsync(object reportData, ReportFormatting formatting = null);

        /// <summary>
        /// Gets supported export formats
        /// </summary>
        /// <returns>List of supported formats</returns>
        List<ExportFormat> GetSupportedFormats();

        /// <summary>
        /// Validates report data before export
        /// </summary>
        /// <param name="reportData">Report data to validate</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateReportDataAsync(object reportData);

        /// <summary>
        /// Gets the MIME type for a specific export format
        /// </summary>
        /// <param name="format">Export format</param>
        /// <returns>MIME type</returns>
        string GetMimeType(ExportFormat format);

        /// <summary>
        /// Gets the file extension for a specific export format
        /// </summary>
        /// <param name="format">Export format</param>
        /// <returns>File extension (including the dot)</returns>
        string GetFileExtension(ExportFormat format);

        /// <summary>
        /// Estimates the file size for an export
        /// </summary>
        /// <param name="reportData">Report data</param>
        /// <param name="format">Export format</param>
        /// <returns>Estimated file size in bytes</returns>
        Task<long> EstimateFileSizeAsync(object reportData, ExportFormat format);

        /// <summary>
        /// Exports multiple reports as a batch
        /// </summary>
        /// <param name="reports">List of reports to export</param>
        /// <param name="format">Export format</param>
        /// <param name="formatting">Formatting options</param>
        /// <returns>Batch export result</returns>
        Task<BatchExportResult> ExportBatchAsync(List<ReportExportRequest> reports, ExportFormat format, ReportFormatting formatting = null);

        /// <summary>
        /// Cancels an ongoing export operation
        /// </summary>
        /// <param name="exportId">Export operation ID</param>
        /// <returns>Task representing the async operation</returns>
        Task CancelExportAsync(string exportId);

        /// <summary>
        /// Gets the status of an export operation
        /// </summary>
        /// <param name="exportId">Export operation ID</param>
        /// <returns>Export status</returns>
        Task<ExportStatus> GetExportStatusAsync(string exportId);

        /// <summary>
        /// Event fired when an export operation starts
        /// </summary>
        event EventHandler<ExportEventArgs> ExportStarted;

        /// <summary>
        /// Event fired when an export operation completes
        /// </summary>
        event EventHandler<ExportEventArgs> ExportCompleted;

        /// <summary>
        /// Event fired when an export operation fails
        /// </summary>
        event EventHandler<ExportEventArgs> ExportFailed;

        /// <summary>
        /// Event fired to report export progress
        /// </summary>
        event EventHandler<ExportProgressEventArgs> ExportProgress;
    }

    /// <summary>
    /// Export formats supported by the system
    /// </summary>
    public enum ExportFormat
    {
        PDF,
        Excel,
        CSV,
        HTML,
        XML,
        JSON,
        Word,
        PowerPoint,
        Text
    }

    /// <summary>
    /// Validation result for report data
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Whether the validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation error messages
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Validation warning messages
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Additional validation metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Request for exporting a report
    /// </summary>
    public class ReportExportRequest
    {
        /// <summary>
        /// Unique identifier for the export request
        /// </summary>
        public string RequestId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Report data to export
        /// </summary>
        public object ReportData { get; set; }

        /// <summary>
        /// Report title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Report description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Formatting options
        /// </summary>
        public ReportFormatting Formatting { get; set; }

        /// <summary>
        /// Export parameters
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Result of a batch export operation
    /// </summary>
    public class BatchExportResult
    {
        /// <summary>
        /// Batch export ID
        /// </summary>
        public string BatchId { get; set; }

        /// <summary>
        /// Number of reports successfully exported
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// Number of reports that failed to export
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// Total size of all exported files
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// Export results for individual reports
        /// </summary>
        public List<IndividualExportResult> Results { get; set; } = new List<IndividualExportResult>();

        /// <summary>
        /// Path to the batch export file (if applicable)
        /// </summary>
        public string BatchFilePath { get; set; }
    }

    /// <summary>
    /// Result of an individual export within a batch
    /// </summary>
    public class IndividualExportResult
    {
        /// <summary>
        /// Request ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// Whether the export was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if export failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// File path of the exported report
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }
    }

    /// <summary>
    /// Status of an export operation
    /// </summary>
    public class ExportStatus
    {
        /// <summary>
        /// Export operation ID
        /// </summary>
        public string ExportId { get; set; }

        /// <summary>
        /// Current status
        /// </summary>
        public ExportState State { get; set; }

        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Status message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Start time of the export
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End time of the export (if completed)
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Error message if export failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// File path of the exported report (if completed)
        /// </summary>
        public string FilePath { get; set; }
    }

    /// <summary>
    /// Export operation states
    /// </summary>
    public enum ExportState
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// Event arguments for export events
    /// </summary>
    public class ExportEventArgs : EventArgs
    {
        /// <summary>
        /// Export operation ID
        /// </summary>
        public string ExportId { get; set; }

        /// <summary>
        /// Export format
        /// </summary>
        public ExportFormat Format { get; set; }

        /// <summary>
        /// Export status
        /// </summary>
        public ExportStatus Status { get; set; }

        /// <summary>
        /// Error message if applicable
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Event arguments for export progress events
    /// </summary>
    public class ExportProgressEventArgs : EventArgs
    {
        /// <summary>
        /// Export operation ID
        /// </summary>
        public string ExportId { get; set; }

        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Current operation message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Estimated time remaining
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }
}
