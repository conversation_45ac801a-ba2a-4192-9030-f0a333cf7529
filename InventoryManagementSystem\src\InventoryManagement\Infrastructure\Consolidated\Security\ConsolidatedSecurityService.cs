using InventoryManagement.Infrastructure.Auth;
using InventoryManagement.Infrastructure.Security;
using InventoryManagement.Infrastructure.Validation;
using InventoryManagement.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Consolidated.Security
{
    /// <summary>
    /// Consolidated security service that merges functionality from multiple security-related services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - Authentication services
    /// - Authorization services
    /// - Data encryption services
    /// - Validation services
    /// - Permission management
    /// </summary>
    public class ConsolidatedSecurityService
    {
        private readonly IDataEncryptionService _encryptionService;
        private readonly IPermissionManager _permissionManager;
        private readonly IValidationManager _validationManager;
        private readonly ILoginRateLimitService _rateLimitService;
        private readonly ILogger<ConsolidatedSecurityService> _logger;

        public ConsolidatedSecurityService(
            IDataEncryptionService encryptionService,
            IPermissionManager permissionManager,
            IValidationManager validationManager,
            ILoginRateLimitService rateLimitService,
            ILogger<ConsolidatedSecurityService> logger)
        {
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            _permissionManager = permissionManager ?? throw new ArgumentNullException(nameof(permissionManager));
            _validationManager = validationManager ?? throw new ArgumentNullException(nameof(validationManager));
            _rateLimitService = rateLimitService ?? throw new ArgumentNullException(nameof(rateLimitService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Authentication Security

        /// <summary>
        /// Validates login attempt with rate limiting
        /// </summary>
        /// <param name="username">Username attempting login</param>
        /// <param name="ipAddress">IP address of the login attempt</param>
        /// <returns>True if login attempt is allowed, false if rate limited</returns>
        public async Task<bool> ValidateLoginAttemptAsync(string username, string ipAddress)
        {
            try
            {
                _logger.LogDebug("Validating login attempt for user: {Username} from IP: {IpAddress}", username, ipAddress);

                var isAllowed = await _rateLimitService.IsLoginAllowedAsync(username, ipAddress);
                
                if (!isAllowed)
                {
                    _logger.LogWarning("Login attempt blocked due to rate limiting for user: {Username}", username);
                }

                return isAllowed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating login attempt for user: {Username}", username);
                return false;
            }
        }

        /// <summary>
        /// Records a failed login attempt
        /// </summary>
        /// <param name="username">Username that failed login</param>
        /// <param name="ipAddress">IP address of the failed attempt</param>
        /// <returns>Task representing the async operation</returns>
        public async Task RecordFailedLoginAsync(string username, string ipAddress)
        {
            try
            {
                await _rateLimitService.RecordFailedAttemptAsync(username, ipAddress);
                _logger.LogWarning("Failed login attempt recorded for user: {Username} from IP: {IpAddress}", username, ipAddress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording failed login attempt for user: {Username}", username);
            }
        }

        /// <summary>
        /// Resets login attempts for a user after successful login
        /// </summary>
        /// <param name="username">Username that successfully logged in</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ResetLoginAttemptsAsync(string username)
        {
            try
            {
                await _rateLimitService.ResetAttemptsAsync(username);
                _logger.LogDebug("Login attempts reset for user: {Username}", username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting login attempts for user: {Username}", username);
            }
        }

        #endregion

        #region Authorization and Permissions

        /// <summary>
        /// Checks if a user has permission to perform a specific action
        /// </summary>
        /// <param name="user">User to check permissions for</param>
        /// <param name="permission">Permission to check</param>
        /// <returns>True if user has permission, false otherwise</returns>
        public bool HasPermission(User user, string permission)
        {
            try
            {
                if (user == null)
                {
                    _logger.LogWarning("Permission check failed: User is null");
                    return false;
                }

                var hasPermission = _permissionManager.HasPermission(user, permission);
                
                _logger.LogDebug("Permission check for user {Username}, permission {Permission}: {Result}", 
                    user.Username, permission, hasPermission);

                return hasPermission;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission {Permission} for user {Username}", permission, user?.Username);
                return false;
            }
        }

        /// <summary>
        /// Gets all permissions for a user
        /// </summary>
        /// <param name="user">User to get permissions for</param>
        /// <returns>List of permissions</returns>
        public List<string> GetUserPermissions(User user)
        {
            try
            {
                if (user == null)
                {
                    _logger.LogWarning("Cannot get permissions: User is null");
                    return new List<string>();
                }

                var permissions = _permissionManager.GetUserPermissions(user);
                
                _logger.LogDebug("Retrieved {Count} permissions for user {Username}", permissions.Count, user.Username);

                return permissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permissions for user {Username}", user?.Username);
                return new List<string>();
            }
        }

        /// <summary>
        /// Validates if a user can access a specific resource
        /// </summary>
        /// <param name="user">User requesting access</param>
        /// <param name="resourceType">Type of resource</param>
        /// <param name="resourceId">ID of the resource</param>
        /// <param name="action">Action to perform on the resource</param>
        /// <returns>True if access is allowed, false otherwise</returns>
        public bool CanAccessResource(User user, string resourceType, int resourceId, string action)
        {
            try
            {
                if (user == null)
                {
                    _logger.LogWarning("Resource access denied: User is null");
                    return false;
                }

                // Check basic permission first
                var permission = $"{resourceType}.{action}";
                if (!HasPermission(user, permission))
                {
                    _logger.LogWarning("User {Username} denied access to {ResourceType}:{ResourceId} for action {Action} - insufficient permissions", 
                        user.Username, resourceType, resourceId, action);
                    return false;
                }

                // Additional resource-specific checks can be added here
                // For now, we'll allow access if the user has the basic permission

                _logger.LogDebug("User {Username} granted access to {ResourceType}:{ResourceId} for action {Action}", 
                    user.Username, resourceType, resourceId, action);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking resource access for user {Username}", user?.Username);
                return false;
            }
        }

        #endregion

        #region Data Encryption and Security

        /// <summary>
        /// Encrypts sensitive data for storage
        /// </summary>
        /// <param name="plainText">Plain text to encrypt</param>
        /// <returns>Encrypted data</returns>
        public string EncryptData(string plainText)
        {
            try
            {
                if (string.IsNullOrEmpty(plainText))
                {
                    return plainText;
                }

                var encryptedData = _encryptionService.Encrypt(plainText);
                _logger.LogDebug("Data encrypted successfully");
                
                return encryptedData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error encrypting data");
                throw;
            }
        }

        /// <summary>
        /// Decrypts sensitive data from storage
        /// </summary>
        /// <param name="encryptedText">Encrypted text to decrypt</param>
        /// <returns>Decrypted plain text</returns>
        public string DecryptData(string encryptedText)
        {
            try
            {
                if (string.IsNullOrEmpty(encryptedText))
                {
                    return encryptedText;
                }

                var decryptedData = _encryptionService.Decrypt(encryptedText);
                _logger.LogDebug("Data decrypted successfully");
                
                return decryptedData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting data");
                throw;
            }
        }

        /// <summary>
        /// Generates a secure hash for passwords
        /// </summary>
        /// <param name="password">Password to hash</param>
        /// <param name="salt">Salt for the hash (optional, will generate if not provided)</param>
        /// <returns>Secure password hash</returns>
        public string HashPassword(string password, string salt = null)
        {
            try
            {
                if (string.IsNullOrEmpty(password))
                {
                    throw new ArgumentException("Password cannot be null or empty", nameof(password));
                }

                // Generate salt if not provided
                if (string.IsNullOrEmpty(salt))
                {
                    salt = GenerateSalt();
                }

                // Use PBKDF2 for secure password hashing
                using (var pbkdf2 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes(salt), 10000))
                {
                    var hash = pbkdf2.GetBytes(32);
                    var hashString = Convert.ToBase64String(hash);
                    
                    _logger.LogDebug("Password hashed successfully");
                    
                    return $"{salt}:{hashString}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error hashing password");
                throw;
            }
        }

        /// <summary>
        /// Verifies a password against a hash
        /// </summary>
        /// <param name="password">Password to verify</param>
        /// <param name="hash">Hash to verify against</param>
        /// <returns>True if password matches hash, false otherwise</returns>
        public bool VerifyPassword(string password, string hash)
        {
            try
            {
                if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash))
                {
                    return false;
                }

                var parts = hash.Split(':');
                if (parts.Length != 2)
                {
                    return false;
                }

                var salt = parts[0];
                var storedHash = parts[1];

                var computedHash = HashPassword(password, salt);
                var computedHashParts = computedHash.Split(':');
                
                if (computedHashParts.Length != 2)
                {
                    return false;
                }

                var isValid = storedHash == computedHashParts[1];
                
                _logger.LogDebug("Password verification result: {IsValid}", isValid);
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying password");
                return false;
            }
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates an object using the validation manager
        /// </summary>
        /// <typeparam name="T">Type of object to validate</typeparam>
        /// <param name="obj">Object to validate</param>
        /// <returns>Validation result</returns>
        public ValidationResult ValidateObject<T>(T obj)
        {
            try
            {
                var result = _validationManager.Validate(obj);
                
                _logger.LogDebug("Object validation completed. IsValid: {IsValid}, Errors: {ErrorCount}", 
                    result.IsValid, result.Errors.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating object of type {Type}", typeof(T).Name);
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private string GenerateSalt()
        {
            var saltBytes = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        #endregion
    }
}
