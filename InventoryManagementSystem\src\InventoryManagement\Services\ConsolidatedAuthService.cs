using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Infrastructure.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Consolidated authentication service that merges functionality from multiple auth services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - AuthService
    /// - EnhancedAuthService
    /// - OfflineAuthService
    /// - Infrastructure.Authentication.IAuthService implementations
    /// </summary>
    public class ConsolidatedAuthService : IAuthService, Infrastructure.Authentication.IAuthService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ICacheService _cacheService;
        private readonly ILogger<ConsolidatedAuthService> _logger;
        private readonly IAuditService _auditService;
        private User _currentUser;

        public ConsolidatedAuthService(
            ApplicationDbContext dbContext,
            ICacheService cacheService,
            ILogger<ConsolidatedAuthService> logger,
            IAuditService auditService)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
        }

        #region Properties

        /// <summary>
        /// Gets the currently authenticated user
        /// </summary>
        public User CurrentUser => _currentUser;

        /// <summary>
        /// Gets a value indicating whether a user is currently authenticated
        /// </summary>
        public bool IsAuthenticated => _currentUser != null;

        #endregion

        #region Core Authentication Methods

        /// <summary>
        /// Authenticate a user with username and password
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Authentication result</returns>
        public async Task<AuthResult> AuthenticateAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("Attempting authentication for user: {Username}", username);

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new AuthResult 
                    { 
                        Success = false, 
                        ErrorMessage = "Username and password are required" 
                    };
                }

                var user = await _dbContext.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("Authentication failed: User not found - {Username}", username);
                    await _auditService.LogActivityAsync(0, "Authentication", 
                        $"Failed login attempt for username: {username}", "User not found");
                    
                    return new AuthResult 
                    { 
                        Success = false, 
                        ErrorMessage = "Invalid username or password" 
                    };
                }

                if (!VerifyPassword(password, user.PasswordHash))
                {
                    _logger.LogWarning("Authentication failed: Invalid password for user {Username}", username);
                    await _auditService.LogActivityAsync(user.Id, "Authentication", 
                        $"Failed login attempt", "Invalid password");
                    
                    return new AuthResult 
                    { 
                        Success = false, 
                        ErrorMessage = "Invalid username or password" 
                    };
                }

                // Update last login
                user.LastLoginDate = DateTime.Now;
                await _dbContext.SaveChangesAsync();

                // Set current user
                _currentUser = user;

                // Cache user for performance
                await _cacheService.SetAsync($"user_{user.Id}", user, TimeSpan.FromHours(1));

                // Log successful authentication
                await _auditService.LogActivityAsync(user.Id, "Authentication", 
                    "Successful login", $"User {username} logged in successfully");

                _logger.LogInformation("Authentication successful for user: {Username}", username);

                return new AuthResult 
                { 
                    Success = true, 
                    User = user,
                    Message = "Authentication successful"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for user: {Username}", username);
                return new AuthResult 
                { 
                    Success = false, 
                    ErrorMessage = "An error occurred during authentication" 
                };
            }
        }

        /// <summary>
        /// Enhanced authentication with additional security features
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Enhanced authentication result</returns>
        public async Task<AuthenticationResult> AuthenticateBasicAsync(string username, string password)
        {
            var authResult = await AuthenticateAsync(username, password);
            
            return new AuthenticationResult
            {
                IsSuccessful = authResult.Success,
                User = authResult.User,
                ErrorMessage = authResult.ErrorMessage,
                RequiresTwoFactor = false, // Simplified for offline mode
                SessionToken = authResult.Success ? GenerateSessionToken() : null
            };
        }

        /// <summary>
        /// Logout the current user
        /// </summary>
        /// <returns>Task</returns>
        public async Task LogoutAsync()
        {
            try
            {
                if (_currentUser != null)
                {
                    _logger.LogInformation("Logging out user: {Username}", _currentUser.Username);

                    // Log logout activity
                    await _auditService.LogActivityAsync(_currentUser.Id, "Authentication", 
                        "User logout", $"User {_currentUser.Username} logged out");

                    // Clear cache
                    await _cacheService.RemoveAsync($"user_{_currentUser.Id}");

                    // Clear current user
                    _currentUser = null;

                    _logger.LogInformation("User logged out successfully");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                throw;
            }
        }

        /// <summary>
        /// Get the current authenticated user
        /// </summary>
        /// <returns>Current user or null if not authenticated</returns>
        public User GetCurrentUser()
        {
            return _currentUser;
        }

        #endregion

        #region User Management Methods

        /// <summary>
        /// Gets all users in the system
        /// </summary>
        /// <returns>List of all users</returns>
        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                return await _dbContext.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                throw;
            }
        }

        /// <summary>
        /// Creates a new user
        /// </summary>
        /// <param name="user">User to create</param>
        /// <param name="password">User's password</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> CreateUserAsync(User user, string password)
        {
            try
            {
                _logger.LogInformation("Creating new user: {Username}", user.Username);

                // Check if username already exists
                var existingUser = await _dbContext.Users
                    .FirstOrDefaultAsync(u => u.Username == user.Username);

                if (existingUser != null)
                {
                    _logger.LogWarning("User creation failed: Username already exists - {Username}", user.Username);
                    return false;
                }

                // Hash password
                user.PasswordHash = HashPassword(password);
                user.CreatedDate = DateTime.Now;
                user.IsActive = true;

                _dbContext.Users.Add(user);
                await _dbContext.SaveChangesAsync();

                // Log user creation
                await _auditService.LogActivityAsync(_currentUser?.Id ?? 0, "User Management", 
                    $"Created new user: {user.Username}", $"User {user.FullName} created with role {user.Role}");

                _logger.LogInformation("User created successfully: {Username}", user.Username);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user: {Username}", user.Username);
                return false;
            }
        }

        /// <summary>
        /// Updates an existing user
        /// </summary>
        /// <param name="user">User to update</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                _logger.LogInformation("Updating user: {Username}", user.Username);

                var existingUser = await _dbContext.Users.FindAsync(user.Id);
                if (existingUser == null)
                {
                    _logger.LogWarning("User update failed: User not found - {UserId}", user.Id);
                    return false;
                }

                // Update properties
                existingUser.FullName = user.FullName;
                existingUser.Role = user.Role;
                existingUser.IsActive = user.IsActive;
                existingUser.LastModifiedDate = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                // Log user update
                await _auditService.LogActivityAsync(_currentUser?.Id ?? 0, "User Management", 
                    $"Updated user: {user.Username}", $"User {user.FullName} updated");

                _logger.LogInformation("User updated successfully: {Username}", user.Username);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {Username}", user.Username);
                return false;
            }
        }

        /// <summary>
        /// Resets a user's password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newPassword">New password</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            try
            {
                _logger.LogInformation("Resetting password for user: {UserId}", userId);

                var user = await _dbContext.Users.FindAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("Password reset failed: User not found - {UserId}", userId);
                    return false;
                }

                user.PasswordHash = HashPassword(newPassword);
                user.LastModifiedDate = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                // Log password reset
                await _auditService.LogActivityAsync(_currentUser?.Id ?? 0, "User Management", 
                    $"Reset password for user: {user.Username}", "Password reset completed");

                _logger.LogInformation("Password reset successfully for user: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password for user: {UserId}", userId);
                return false;
            }
        }

        #endregion

        #region Private Helper Methods

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private bool VerifyPassword(string password, string hash)
        {
            var hashedPassword = HashPassword(password);
            return hashedPassword == hash;
        }

        private string GenerateSessionToken()
        {
            return Guid.NewGuid().ToString("N");
        }

        #endregion
    }
}
