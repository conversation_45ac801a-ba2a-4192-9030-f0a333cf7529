using System;
using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents user notification preferences
    /// </summary>
    public class NotificationPreferences
    {
        /// <summary>
        /// Unique identifier for the notification preferences
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// User ID these preferences belong to
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// Navigation property to the user
        /// </summary>
        public virtual User? User { get; set; }

        /// <summary>
        /// Whether desktop notifications are enabled
        /// </summary>
        public bool DesktopNotificationsEnabled { get; set; } = true;

        /// <summary>
        /// Whether email notifications are enabled
        /// </summary>
        public bool EmailNotificationsEnabled { get; set; } = true;

        /// <summary>
        /// Whether SMS notifications are enabled
        /// </summary>
        public bool SmsNotificationsEnabled { get; set; } = false;

        /// <summary>
        /// Whether to receive notifications for low stock alerts
        /// </summary>
        public bool LowStockAlertsEnabled { get; set; } = true;

        /// <summary>
        /// Whether to receive notifications for sales alerts
        /// </summary>
        public bool SalesAlertsEnabled { get; set; } = true;

        /// <summary>
        /// Whether to receive notifications for system alerts
        /// </summary>
        public bool SystemAlertsEnabled { get; set; } = true;

        /// <summary>
        /// Whether to receive notifications for error alerts
        /// </summary>
        public bool ErrorAlertsEnabled { get; set; } = true;

        /// <summary>
        /// Whether to receive notifications for security alerts
        /// </summary>
        public bool SecurityAlertsEnabled { get; set; } = true;

        /// <summary>
        /// Whether to receive notifications for backup alerts
        /// </summary>
        public bool BackupAlertsEnabled { get; set; } = true;

        /// <summary>
        /// Minimum priority level for notifications to be sent
        /// </summary>
        public NotificationPriority MinimumPriority { get; set; } = NotificationPriority.Normal;

        /// <summary>
        /// Quiet hours start time (24-hour format)
        /// </summary>
        public TimeSpan? QuietHoursStart { get; set; }

        /// <summary>
        /// Quiet hours end time (24-hour format)
        /// </summary>
        public TimeSpan? QuietHoursEnd { get; set; }

        /// <summary>
        /// Whether quiet hours are enabled
        /// </summary>
        public bool QuietHoursEnabled { get; set; } = false;

        /// <summary>
        /// Email address for email notifications (if different from user email)
        /// </summary>
        [MaxLength(255)]
        public string? NotificationEmail { get; set; }

        /// <summary>
        /// Phone number for SMS notifications
        /// </summary>
        [MaxLength(20)]
        public string? NotificationPhone { get; set; }

        /// <summary>
        /// How long to display desktop notifications (in seconds)
        /// </summary>
        public int DesktopNotificationDuration { get; set; } = 5;

        /// <summary>
        /// Whether to play sound with desktop notifications
        /// </summary>
        public bool PlayNotificationSound { get; set; } = true;

        /// <summary>
        /// Maximum number of notifications to show per hour
        /// </summary>
        public int MaxNotificationsPerHour { get; set; } = 10;

        /// <summary>
        /// Whether to group similar notifications
        /// </summary>
        public bool GroupSimilarNotifications { get; set; } = true;

        /// <summary>
        /// When these preferences were created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// When these preferences were last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
