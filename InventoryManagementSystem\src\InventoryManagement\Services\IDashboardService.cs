using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for generating dashboard data and metrics
    /// </summary>
    public interface IDashboardService
    {
        /// <summary>
        /// Gets summary metrics for the dashboard
        /// </summary>
        /// <param name="userId">ID of the user requesting the dashboard</param>
        /// <returns>Dashboard summary data</returns>
        Task<DashboardSummary> GetDashboardSummaryAsync(int userId);
        
        /// <summary>
        /// Gets inventory metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Inventory metrics</returns>
        Task<InventoryMetrics> GetInventoryMetricsAsync(TimeSpan timeSpan);
        
        /// <summary>
        /// Gets sales metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Sales metrics</returns>
        Task<SalesMetrics> GetSalesMetricsAsync(TimeSpan timeSpan);
        
        /// <summary>
        /// Gets financial metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Financial metrics</returns>
        Task<FinancialMetrics> GetFinancialMetricsAsync(TimeSpan timeSpan);
        
        /// <summary>
        /// Gets supplier metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Supplier metrics</returns>
        Task<SupplierMetrics> GetSupplierMetricsAsync(TimeSpan timeSpan);
        
        /// <summary>
        /// Gets top selling items for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <param name="count">Number of items to return</param>
        /// <returns>List of top selling items</returns>
        Task<List<TopSellingItem>> GetTopSellingItemsAsync(TimeSpan timeSpan, int count = 10);
        
        /// <summary>
        /// Gets top revenue generating items for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <param name="count">Number of items to return</param>
        /// <returns>List of top revenue items</returns>
        Task<List<TopRevenueItem>> GetTopRevenueItemsAsync(TimeSpan timeSpan, int count = 10);
        
        /// <summary>
        /// Gets inventory alerts for the dashboard
        /// </summary>
        /// <param name="count">Number of alerts to return</param>
        /// <returns>List of inventory alerts</returns>
        Task<List<InventoryAlert>> GetInventoryAlertsAsync(int count = 10);
        
        /// <summary>
        /// Gets recent activity for the dashboard
        /// </summary>
        /// <param name="userId">ID of the user requesting the dashboard</param>
        /// <param name="count">Number of activities to return</param>
        /// <returns>List of recent activities</returns>
        Task<List<RecentActivity>> GetRecentActivityAsync(int userId, int count = 20);
        
        /// <summary>
        /// Saves user dashboard preferences
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <param name="preferences">Dashboard preferences</param>
        /// <returns>True if successful</returns>
        Task<bool> SaveDashboardPreferencesAsync(int userId, DashboardPreferences preferences);
        
        /// <summary>
        /// Gets user dashboard preferences
        /// </summary>
        /// <param name="userId">ID of the user</param>
        /// <returns>Dashboard preferences</returns>
        Task<DashboardPreferences> GetDashboardPreferencesAsync(int userId);
    }
    
    /// <summary>
    /// Dashboard summary data
    /// </summary>
    public class DashboardSummary
    {
        /// <summary>
        /// Total inventory value
        /// </summary>
        public decimal TotalInventoryValue { get; set; }
        
        /// <summary>
        /// Total inventory items count
        /// </summary>
        public int TotalItemsCount { get; set; }
        
        /// <summary>
        /// Low stock items count
        /// </summary>
        public int LowStockItemsCount { get; set; }
        
        /// <summary>
        /// Out of stock items count
        /// </summary>
        public int OutOfStockItemsCount { get; set; }
        
        /// <summary>
        /// Expiring items count
        /// </summary>
        public int ExpiringItemsCount { get; set; }
        
        /// <summary>
        /// Today's sales amount
        /// </summary>
        public decimal TodaySales { get; set; }
        
        /// <summary>
        /// Today's transactions count
        /// </summary>
        public int TodayTransactionsCount { get; set; }
        
        /// <summary>
        /// Pending orders count
        /// </summary>
        public int PendingOrdersCount { get; set; }
        
        /// <summary>
        /// Unread notifications count
        /// </summary>
        public int UnreadNotificationsCount { get; set; }
        
        /// <summary>
        /// Pending tasks count
        /// </summary>
        public int PendingTasksCount { get; set; }
        
        /// <summary>
        /// Latest inventory alerts
        /// </summary>
        public List<InventoryAlert> LatestAlerts { get; set; } = new List<InventoryAlert>();
        
        /// <summary>
        /// Recent activities
        /// </summary>
        public List<RecentActivity> RecentActivities { get; set; } = new List<RecentActivity>();
    }
    
    /// <summary>
    /// Inventory metrics
    /// </summary>
    public class InventoryMetrics
    {
        /// <summary>
        /// Total inventory value
        /// </summary>
        public decimal TotalValue { get; set; }
        
        /// <summary>
        /// Change in inventory value
        /// </summary>
        public decimal ValueChange { get; set; }
        
        /// <summary>
        /// Percentage change in inventory value
        /// </summary>
        public decimal ValueChangePercentage { get; set; }
        
        /// <summary>
        /// Total inventory items count
        /// </summary>
        public int TotalItemsCount { get; set; }
        
        /// <summary>
        /// Average item value
        /// </summary>
        public decimal AverageItemValue { get; set; }
        
        /// <summary>
        /// Inventory turnover rate
        /// </summary>
        public decimal TurnoverRate { get; set; }
        
        /// <summary>
        /// Low stock items count
        /// </summary>
        public int LowStockCount { get; set; }
        
        /// <summary>
        /// Low stock items percentage
        /// </summary>
        public decimal LowStockPercentage { get; set; }
        
        /// <summary>
        /// Out of stock items count
        /// </summary>
        public int OutOfStockCount { get; set; }
        
        /// <summary>
        /// Items with no sales count
        /// </summary>
        public int NoSalesCount { get; set; }
        
        /// <summary>
        /// Expiring items count
        /// </summary>
        public int ExpiringItemsCount { get; set; }
        
        /// <summary>
        /// Inventory value by category
        /// </summary>
        public Dictionary<string, decimal> ValueByCategory { get; set; } = new Dictionary<string, decimal>();
        
        /// <summary>
        /// Inventory count by location
        /// </summary>
        public Dictionary<string, int> CountByLocation { get; set; } = new Dictionary<string, int>();
        
        /// <summary>
        /// Time period for these metrics
        /// </summary>
        public TimeSpan TimePeriod { get; set; }
    }
    
    /// <summary>
    /// Sales metrics
    /// </summary>
    public class SalesMetrics
    {
        /// <summary>
        /// Total sales amount
        /// </summary>
        public decimal TotalSales { get; set; }
        
        /// <summary>
        /// Change in sales amount
        /// </summary>
        public decimal SalesChange { get; set; }
        
        /// <summary>
        /// Percentage change in sales
        /// </summary>
        public decimal SalesChangePercentage { get; set; }
        
        /// <summary>
        /// Total transactions count
        /// </summary>
        public int TransactionsCount { get; set; }
        
        /// <summary>
        /// Change in transactions count
        /// </summary>
        public int TransactionsCountChange { get; set; }
        
        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }
        
        /// <summary>
        /// Total units sold
        /// </summary>
        public int UnitsSold { get; set; }
        
        /// <summary>
        /// Change in units sold
        /// </summary>
        public int UnitsSoldChange { get; set; }
        
        /// <summary>
        /// Sales by day
        /// </summary>
        public Dictionary<DateTime, decimal> SalesByDay { get; set; } = new Dictionary<DateTime, decimal>();
        
        /// <summary>
        /// Sales by category
        /// </summary>
        public Dictionary<string, decimal> SalesByCategory { get; set; } = new Dictionary<string, decimal>();
        
        /// <summary>
        /// Sales by payment method
        /// </summary>
        public Dictionary<string, decimal> SalesByPaymentMethod { get; set; } = new Dictionary<string, decimal>();
        
        /// <summary>
        /// Time period for these metrics
        /// </summary>
        public TimeSpan TimePeriod { get; set; }
    }
    
    /// <summary>
    /// Financial metrics
    /// </summary>
    public class FinancialMetrics
    {
        /// <summary>
        /// Total revenue
        /// </summary>
        public decimal TotalRevenue { get; set; }
        
        /// <summary>
        /// Total cost of goods sold
        /// </summary>
        public decimal TotalCogs { get; set; }
        
        /// <summary>
        /// Gross profit
        /// </summary>
        public decimal GrossProfit { get; set; }
        
        /// <summary>
        /// Gross profit margin
        /// </summary>
        public decimal GrossProfitMargin { get; set; }
        
        /// <summary>
        /// Total expenses
        /// </summary>
        public decimal TotalExpenses { get; set; }
        
        /// <summary>
        /// Net profit
        /// </summary>
        public decimal NetProfit { get; set; }
        
        /// <summary>
        /// Net profit margin
        /// </summary>
        public decimal NetProfitMargin { get; set; }
        
        /// <summary>
        /// Accounts receivable
        /// </summary>
        public decimal AccountsReceivable { get; set; }
        
        /// <summary>
        /// Accounts payable
        /// </summary>
        public decimal AccountsPayable { get; set; }
        
        /// <summary>
        /// Cash flow
        /// </summary>
        public decimal CashFlow { get; set; }
        
        /// <summary>
        /// Revenue by day
        /// </summary>
        public Dictionary<DateTime, decimal> RevenueByDay { get; set; } = new Dictionary<DateTime, decimal>();
        
        /// <summary>
        /// Expenses by category
        /// </summary>
        public Dictionary<string, decimal> ExpensesByCategory { get; set; } = new Dictionary<string, decimal>();
        
        /// <summary>
        /// Time period for these metrics
        /// </summary>
        public TimeSpan TimePeriod { get; set; }
    }
    
    /// <summary>
    /// Supplier metrics
    /// </summary>
    public class SupplierMetrics
    {
        /// <summary>
        /// Total suppliers count
        /// </summary>
        public int TotalSuppliersCount { get; set; }
        
        /// <summary>
        /// Active suppliers count
        /// </summary>
        public int ActiveSuppliersCount { get; set; }
        
        /// <summary>
        /// Total purchase orders count
        /// </summary>
        public int TotalPurchaseOrdersCount { get; set; }
        
        /// <summary>
        /// Open purchase orders count
        /// </summary>
        public int OpenPurchaseOrdersCount { get; set; }
        
        /// <summary>
        /// Total purchases amount
        /// </summary>
        public decimal TotalPurchasesAmount { get; set; }
        
        /// <summary>
        /// Average order value
        /// </summary>
        public decimal AverageOrderValue { get; set; }
        
        /// <summary>
        /// Average delivery time (days)
        /// </summary>
        public double AverageDeliveryTimeDays { get; set; }
        
        /// <summary>
        /// On-time delivery percentage
        /// </summary>
        public decimal OnTimeDeliveryPercentage { get; set; }
        
        /// <summary>
        /// Order defect rate
        /// </summary>
        public decimal OrderDefectRate { get; set; }
        
        /// <summary>
        /// Top suppliers by purchase amount
        /// </summary>
        public Dictionary<string, decimal> TopSuppliersByAmount { get; set; } = new Dictionary<string, decimal>();
        
        /// <summary>
        /// Time period for these metrics
        /// </summary>
        public TimeSpan TimePeriod { get; set; }
    }
    
    // Note: TopSellingItem class is defined in Models/InventoryModels.cs
    
    /// <summary>
    /// Top revenue item
    /// </summary>
    public class TopRevenueItem
    {
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Item name
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// Item SKU
        /// </summary>
        public string Sku { get; set; }
        
        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// Quantity sold
        /// </summary>
        public int QuantitySold { get; set; }
        
        /// <summary>
        /// Sales amount
        /// </summary>
        public decimal SalesAmount { get; set; }
        
        /// <summary>
        /// Profit amount
        /// </summary>
        public decimal ProfitAmount { get; set; }
        
        /// <summary>
        /// Percentage of total revenue
        /// </summary>
        public decimal PercentageOfTotal { get; set; }
    }
    
    /// <summary>
    /// Inventory alert
    /// </summary>
    public class InventoryAlert
    {
        /// <summary>
        /// Alert ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Alert type
        /// </summary>
        public InventoryAlertType Type { get; set; }
        
        /// <summary>
        /// Alert severity
        /// </summary>
        public AlertSeverity Severity { get; set; }
        
        /// <summary>
        /// Alert message
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// Related item ID
        /// </summary>
        public int? ItemId { get; set; }
        
        /// <summary>
        /// Related item name
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// Current stock level
        /// </summary>
        public int? CurrentStock { get; set; }
        
        /// <summary>
        /// Threshold value that triggered the alert
        /// </summary>
        public int? Threshold { get; set; }
        
        /// <summary>
        /// Date and time of the alert
        /// </summary>
        public DateTime AlertDate { get; set; }
        
        /// <summary>
        /// Whether the alert has been read
        /// </summary>
        public bool IsRead { get; set; }
        
        /// <summary>
        /// URL for the action to resolve the alert
        /// </summary>
        public string ActionUrl { get; set; }
    }
    
    /// <summary>
    /// Recent activity
    /// </summary>
    public class RecentActivity
    {
        /// <summary>
        /// Activity ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Activity type
        /// </summary>
        public ActivityType Type { get; set; }
        
        /// <summary>
        /// Activity description
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// ID of the user who performed the activity
        /// </summary>
        public int? UserId { get; set; }
        
        /// <summary>
        /// Name of the user who performed the activity
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// Type of entity related to the activity
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// ID of the entity related to the activity
        /// </summary>
        public int? EntityId { get; set; }
        
        /// <summary>
        /// Date and time of the activity
        /// </summary>
        public DateTime ActivityDate { get; set; }
        
        /// <summary>
        /// URL for the action related to the activity
        /// </summary>
        public string ActionUrl { get; set; }
    }
    
    /// <summary>
    /// Inventory alert types
    /// </summary>
    public enum InventoryAlertType
    {
        /// <summary>
        /// Low stock alert
        /// </summary>
        LowStock,
        
        /// <summary>
        /// Out of stock alert
        /// </summary>
        OutOfStock,
        
        /// <summary>
        /// Expiring items alert
        /// </summary>
        ExpiringItems,
        
        /// <summary>
        /// Overstocked items alert
        /// </summary>
        OverstockedItems,
        
        /// <summary>
        /// Items with no sales alert
        /// </summary>
        NoSales,
        
        /// <summary>
        /// Reorder needed alert
        /// </summary>
        ReorderNeeded,
        
        /// <summary>
        /// Price change alert
        /// </summary>
        PriceChange,
        
        /// <summary>
        /// Incoming shipment alert
        /// </summary>
        IncomingShipment
    }
    
    /// <summary>
    /// Alert severity levels
    /// </summary>
    public enum AlertSeverity
    {
        /// <summary>
        /// Low severity
        /// </summary>
        Low,
        
        /// <summary>
        /// Medium severity
        /// </summary>
        Medium,
        
        /// <summary>
        /// High severity
        /// </summary>
        High,
        
        /// <summary>
        /// Critical severity
        /// </summary>
        Critical
    }
    
    /// <summary>
    /// Activity types
    /// </summary>
    public enum ActivityType
    {
        /// <summary>
        /// Created new item
        /// </summary>
        Created,
        
        /// <summary>
        /// Updated existing item
        /// </summary>
        Updated,
        
        /// <summary>
        /// Deleted item
        /// </summary>
        Deleted,
        
        /// <summary>
        /// Processed sale
        /// </summary>
        Sale,
        
        /// <summary>
        /// Received inventory
        /// </summary>
        Received,
        
        /// <summary>
        /// Transferred inventory
        /// </summary>
        Transferred,
        
        /// <summary>
        /// Adjusted inventory
        /// </summary>
        Adjusted,
        
        /// <summary>
        /// Marked item as defective
        /// </summary>
        MarkedDefective,
        
        /// <summary>
        /// Created purchase order
        /// </summary>
        PurchaseOrder,
        
        /// <summary>
        /// System alert
        /// </summary>
        Alert,
        
        /// <summary>
        /// User login
        /// </summary>
        Login,
        
        /// <summary>
        /// Other activity
        /// </summary>
        Other
    }
    
    /// <summary>
    /// Time period enum for dashboard metrics
    /// </summary>
    public enum TimePeriod
    {
        /// <summary>
        /// Today only
        /// </summary>
        Today,
        
        /// <summary>
        /// Yesterday only
        /// </summary>
        Yesterday,
        
        /// <summary>
        /// Last 7 days
        /// </summary>
        Last7Days,
        
        /// <summary>
        /// Last 30 days
        /// </summary>
        Last30Days,
        
        /// <summary>
        /// This month
        /// </summary>
        ThisMonth,
        
        /// <summary>
        /// Last month
        /// </summary>
        LastMonth,
        
        /// <summary>
        /// This quarter
        /// </summary>
        ThisQuarter,
        
        /// <summary>
        /// Last quarter
        /// </summary>
        LastQuarter,
        
        /// <summary>
        /// This year
        /// </summary>
        ThisYear,
        
        /// <summary>
        /// Last year
        /// </summary>
        LastYear,
        
        /// <summary>
        /// Custom time period
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// Dashboard widgets
    /// </summary>
    public enum DashboardWidget
    {
        /// <summary>
        /// Summary metrics widget
        /// </summary>
        SummaryMetrics,
        
        /// <summary>
        /// Sales chart widget
        /// </summary>
        SalesChart,
        
        /// <summary>
        /// Inventory value widget
        /// </summary>
        InventoryValue,
        
        /// <summary>
        /// Top selling items widget
        /// </summary>
        TopSellingItems,
        
        /// <summary>
        /// Top revenue items widget
        /// </summary>
        TopRevenueItems,
        
        /// <summary>
        /// Inventory alerts widget
        /// </summary>
        InventoryAlerts,
        
        /// <summary>
        /// Recent activity widget
        /// </summary>
        RecentActivity,
        
        /// <summary>
        /// Financial metrics widget
        /// </summary>
        FinancialMetrics,
        
        /// <summary>
        /// Supplier metrics widget
        /// </summary>
        SupplierMetrics,
        
        /// <summary>
        /// Category breakdown widget
        /// </summary>
        CategoryBreakdown,
        
        /// <summary>
        /// Notifications widget
        /// </summary>
        Notifications
    }
    
    /// <summary>
    /// Widget sizes
    /// </summary>
    public enum WidgetSize
    {
        /// <summary>
        /// Small widget size
        /// </summary>
        Small,
        
        /// <summary>
        /// Medium widget size
        /// </summary>
        Medium,
        
        /// <summary>
        /// Large widget size
        /// </summary>
        Large,
        
        /// <summary>
        /// Full width widget size
        /// </summary>
        FullWidth
    }
}
