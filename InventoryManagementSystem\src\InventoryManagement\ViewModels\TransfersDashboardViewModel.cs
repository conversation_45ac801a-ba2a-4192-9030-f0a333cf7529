using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using InventoryManagement.Commands;
using InventoryManagement.Infrastructure.Data;
using InventoryManagement.Models;
using InventoryManagement.Services;
// Note: Data services are in InventoryManagement.Services namespace
using InventoryManagement.DataAccess;
using Microsoft.EntityFrameworkCore;
using IAuditService = InventoryManagement.Services.IAuditService;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Transfers section of the main dashboard
    /// </summary>
    public class TransfersDashboardViewModel : ViewModelBase
    {
        private readonly IDataContext _dataContext;
        private readonly IAuditService _auditService;
        private readonly IUserService _userService;
        private readonly INotificationService _notificationService;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        private ObservableCollection<TransferViewModel> _transfers;
        private ICollectionView _filteredTransfers;
        private string _selectedTransferType;
        private DateTime _startDate;
        private DateTime _endDate;
        private bool _isLoading;
        private int _totalTransfers;
        private int _basementToMainCount;
        private int _mainToBasementCount;
        private int _externalTransfers;
        private int _incomingExternals;
        private int _outgoingExternals;
        
        public TransfersDashboardViewModel(
            IDataContext dataContext,
            IAuditService auditService,
            IUserService userService,
            INotificationService notificationService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _dataContext = dataContext ?? throw new ArgumentNullException(nameof(dataContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            // Initialize commands
            ApplyFilterCommand = new RelayCommand(param => ApplyFilters());
            NewTransferCommand = new RelayCommand(param => OpenNewTransferDialog());
            RefreshCommand = new RelayCommand(async param => await LoadTransfersAsync());
            
            // Initialize collections
            Transfers = new ObservableCollection<TransferViewModel>();
            
            // Set default date range to last 30 days
            EndDate = DateTime.Now;
            StartDate = DateTime.Now.AddDays(-30);
            
            // Initial load
            _ = InitializeAsync();
        }
        
        #region Properties
        
        public ObservableCollection<TransferViewModel> Transfers
        {
            get => _transfers;
            set => SetProperty(ref _transfers, value);
        }
        
        public ICollectionView FilteredTransfers
        {
            get => _filteredTransfers;
            set => SetProperty(ref _filteredTransfers, value);
        }
        
        public string SelectedTransferType
        {
            get => _selectedTransferType;
            set
            {
                if (SetProperty(ref _selectedTransferType, value))
                {
                    ApplyFilters();
                }
            }
        }
        
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    OnPropertyChanged(nameof(DateRangeText));
                }
            }
        }
        
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    OnPropertyChanged(nameof(DateRangeText));
                }
            }
        }
        
        public string DateRangeText => $"{StartDate:MM/dd/yyyy} - {EndDate:MM/dd/yyyy}";
        
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }
        
        public int TotalTransfers
        {
            get => _totalTransfers;
            set => SetProperty(ref _totalTransfers, value);
        }
        
        public int BasementToMainCount
        {
            get => _basementToMainCount;
            set => SetProperty(ref _basementToMainCount, value);
        }
        
        public int MainToBasementCount
        {
            get => _mainToBasementCount;
            set => SetProperty(ref _mainToBasementCount, value);
        }
        
        public int ExternalTransfers
        {
            get => _externalTransfers;
            set => SetProperty(ref _externalTransfers, value);
        }
        
        public int IncomingExternals
        {
            get => _incomingExternals;
            set => SetProperty(ref _incomingExternals, value);
        }
        
        public int OutgoingExternals
        {
            get => _outgoingExternals;
            set => SetProperty(ref _outgoingExternals, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand ApplyFilterCommand { get; }
        public ICommand NewTransferCommand { get; }
        public ICommand RefreshCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                await LoadTransfersAsync();
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error initializing Transfers Dashboard");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private async Task LoadTransfersAsync()
        {
            try
            {
                IsLoading = true;
                
                // Query the database for inventory transfers within the date range
                var transfers = await _dataContext.InventoryTransfers
                    .Where(t => t.TransferDate >= StartDate && t.TransferDate <= EndDate)
                    .OrderByDescending(t => t.TransferDate)
                    .ToListAsync();
                
                // Clear existing transfers
                Transfers.Clear();
                
                // Calculate statistics
                TotalTransfers = transfers.Count;
                
                // Count by transfer type
                BasementToMainCount = transfers.Count(t => 
                    t.FromLocationId == (int)LocationType.Basement && 
                    t.ToLocationId == (int)LocationType.MainStore);
                    
                MainToBasementCount = transfers.Count(t => 
                    t.FromLocationId == (int)LocationType.MainStore && 
                    t.ToLocationId == (int)LocationType.Basement);
                    
                IncomingExternals = transfers.Count(t => 
                    t.FromLocationId == (int)LocationType.External && 
                    t.ToLocationId != (int)LocationType.External);
                    
                OutgoingExternals = transfers.Count(t => 
                    t.FromLocationId != (int)LocationType.External && 
                    t.ToLocationId == (int)LocationType.External);
                    
                ExternalTransfers = IncomingExternals + OutgoingExternals;
                
                // Add transfers to collection
                foreach (var transfer in transfers)
                {
                    Transfers.Add(new TransferViewModel(
                        transfer, 
                        _notificationService,
                        _auditService,
                        _exceptionHandler));
                }
                
                // Set up filtering
                FilteredTransfers = CollectionViewSource.GetDefaultView(Transfers);
                FilteredTransfers.Filter = TransferFilter;
                
                // Log audit trail for viewing transfers
                await _auditService.LogActionAsync(
                    "Transfers", 
                    "View", 
                    $"Viewed {Transfers.Count} transfers for period {StartDate:MM/dd/yyyy} to {EndDate:MM/dd/yyyy}", 
                    null);
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading transfers");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private bool TransferFilter(object item)
        {
            if (item is not TransferViewModel transfer)
                return false;
            
            // Apply transfer type filter
            if (!string.IsNullOrWhiteSpace(SelectedTransferType) && SelectedTransferType != "All Transfers")
            {
                switch (SelectedTransferType)
                {
                    case "Basement to Main":
                        if (transfer.TransferType != "Basement to Main")
                            return false;
                        break;
                        
                    case "Main to Basement":
                        if (transfer.TransferType != "Main to Basement")
                            return false;
                        break;
                        
                    case "External Incoming":
                        if (transfer.TransferType != "External Incoming")
                            return false;
                        break;
                        
                    case "External Outgoing":
                        if (transfer.TransferType != "External Outgoing")
                            return false;
                        break;
                }
            }
            
            // Transfer date is already filtered in the database query
            
            return true;
        }
        
        private void ApplyFilters()
        {
            FilteredTransfers?.Refresh();
        }
        
        private void OpenNewTransferDialog()
        {
            // Implementation would open a dialog for creating a new transfer
            // This would typically use a dialog service
        }
        
        #endregion
    }
    
    /// <summary>
    /// ViewModel for an individual inventory transfer in the UI
    /// </summary>
    public class TransferViewModel : ViewModelBase
    {
        private readonly InventoryTransfer _transfer;
        private readonly INotificationService _notificationService;
        private readonly IAuditService _auditService;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        public TransferViewModel(
            InventoryTransfer transfer,
            INotificationService notificationService,
            IAuditService auditService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _transfer = transfer ?? throw new ArgumentNullException(nameof(transfer));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            // Initialize commands
            DetailsCommand = new RelayCommand(param => ViewDetails());
        }
        
        #region Properties
        
        public int TransferId => _transfer.Id;
        public DateTime TransferDate => _transfer.TransferDate;
        public string ItemName => _transfer.Item?.Name ?? string.Empty;
        public decimal Quantity => _transfer.Quantity;
        public string FromLocation => GetLocationName(_transfer.FromLocationId);
        public string ToLocation => GetLocationName(_transfer.ToLocationId);
        public string Status => _transfer.Status.ToString();
        public string UserName => _transfer.User?.FullName ?? string.Empty;
        
        public string TransferType
        {
            get
            {
                if (_transfer.FromLocationId == (int)LocationType.Basement && 
                    _transfer.ToLocationId == (int)LocationType.MainStore)
                    return "Basement to Main";
                    
                if (_transfer.FromLocationId == (int)LocationType.MainStore && 
                    _transfer.ToLocationId == (int)LocationType.Basement)
                    return "Main to Basement";
                    
                if (_transfer.FromLocationId == (int)LocationType.External)
                    return "External Incoming";
                    
                if (_transfer.ToLocationId == (int)LocationType.External)
                    return "External Outgoing";
                    
                return "Other";
            }
        }
        
        #endregion
        
        #region Commands
        
        public ICommand DetailsCommand { get; }
        
        #endregion
        
        #region Methods
        
        private void ViewDetails()
        {
            // Implementation would navigate to transfer details view
            // This would typically use a navigation service
        }
        
        private string GetLocationName(int locationId)
        {
            return locationId switch
            {
                (int)LocationType.MainStore => "Main Store",
                (int)LocationType.Basement => "Basement",
                (int)LocationType.External => "External",
                _ => $"Location {locationId}"
            };
        }
        
        #endregion
    }
    
    /// <summary>
    /// Enum for location types
    /// </summary>
    public enum LocationType
    {
        MainStore = 1,
        Basement = 2,
        External = 3
    }
}
