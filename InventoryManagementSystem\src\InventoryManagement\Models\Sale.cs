using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a sale transaction in the system
    /// </summary>
    public class Sale
    {
        /// <summary>
        /// Unique identifier for the sale
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Sale number for reference
        /// </summary>
        [Required]
        [StringLength(50)]
        public string SaleNumber { get; set; }

        /// <summary>
        /// Date and time when the sale was made
        /// </summary>
        [Required]
        public DateTime SaleDate { get; set; }

        /// <summary>
        /// ID of the customer (optional for walk-in customers)
        /// </summary>
        public int? CustomerId { get; set; }

        /// <summary>
        /// Customer information
        /// </summary>
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; }

        /// <summary>
        /// ID of the user who made the sale
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// User who made the sale
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        /// <summary>
        /// Total amount before tax and discounts
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        /// <summary>
        /// Total discount amount applied
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Tax amount
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Final total amount
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Amount paid by customer
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// Change given to customer
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ChangeAmount { get; set; }

        /// <summary>
        /// Payment method used
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Status of the sale
        /// </summary>
        [Required]
        public SaleStatus Status { get; set; }

        /// <summary>
        /// Notes or comments about the sale
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Date and time when the sale was created
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date and time when the sale was last modified
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        /// <summary>
        /// Collection of sale items
        /// </summary>
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

        /// <summary>
        /// Collection of payments for this sale
        /// </summary>
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }

    /// <summary>
    /// Represents an item in a sale
    /// </summary>
    public class SaleItem
    {
        /// <summary>
        /// Unique identifier for the sale item
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// ID of the sale this item belongs to
        /// </summary>
        [Required]
        public int SaleId { get; set; }

        /// <summary>
        /// Sale this item belongs to
        /// </summary>
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; }

        /// <summary>
        /// ID of the item being sold
        /// </summary>
        [Required]
        public int ItemId { get; set; }

        /// <summary>
        /// Item being sold
        /// </summary>
        [ForeignKey("ItemId")]
        public virtual Item Item { get; set; }

        /// <summary>
        /// Quantity sold
        /// </summary>
        [Required]
        public int Quantity { get; set; }

        /// <summary>
        /// Unit price at the time of sale
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Discount applied to this item
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Total amount for this item (quantity * unit price - discount)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
    }

    /// <summary>
    /// Status of a sale
    /// </summary>
    public enum SaleStatus
    {
        /// <summary>
        /// Sale is pending completion
        /// </summary>
        Pending = 0,

        /// <summary>
        /// Sale has been completed
        /// </summary>
        Completed = 1,

        /// <summary>
        /// Sale has been cancelled
        /// </summary>
        Cancelled = 2,

        /// <summary>
        /// Sale has been refunded
        /// </summary>
        Refunded = 3,

        /// <summary>
        /// Sale has been partially refunded
        /// </summary>
        PartiallyRefunded = 4
    }

    /// <summary>
    /// Sales performance metrics
    /// </summary>
    public class SalesPerformanceMetrics
    {
        /// <summary>
        /// Total number of sales
        /// </summary>
        public int TotalSales { get; set; }

        /// <summary>
        /// Total sales amount
        /// </summary>
        public decimal TotalSalesAmount { get; set; }

        /// <summary>
        /// Average sale amount
        /// </summary>
        public decimal AverageSaleAmount { get; set; }

        /// <summary>
        /// Total number of items sold
        /// </summary>
        public int TotalItemsSold { get; set; }

        /// <summary>
        /// Total discount given
        /// </summary>
        public decimal TotalDiscountGiven { get; set; }

        /// <summary>
        /// Sales by payment method
        /// </summary>
        public Dictionary<string, decimal> SalesByPaymentMethod { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// Sales by user
        /// </summary>
        public Dictionary<string, decimal> SalesByUser { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// Top selling items
        /// </summary>
        public List<TopSellingItem> TopSellingItems { get; set; } = new List<TopSellingItem>();

        /// <summary>
        /// Date range for these metrics
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// End date for these metrics
        /// </summary>
        public DateTime ToDate { get; set; }
    }

    /// <summary>
    /// Represents a top selling item
    /// </summary>
    public class TopSellingItem
    {
        /// <summary>
        /// Item ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// Item name
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// Quantity sold
        /// </summary>
        public int QuantitySold { get; set; }

        /// <summary>
        /// Total revenue from this item
        /// </summary>
        public decimal TotalRevenue { get; set; }
    }
}
