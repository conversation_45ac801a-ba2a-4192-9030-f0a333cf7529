using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for sales data access services
    /// </summary>
    public interface ISalesDataService
    {
        /// <summary>
        /// Gets sales transactions for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of sales transactions</returns>
        Task<List<SalesTransaction>> GetSalesForPeriodAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets detailed sales data for a specific period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of sales details</returns>
        Task<List<SalesDetail>> GetDetailedSalesForPeriodAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets sales transactions by cashier
        /// </summary>
        /// <param name="cashierId">Cashier ID</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of sales transactions</returns>
        Task<List<SalesTransaction>> GetSalesByCashierAsync(int cashierId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets sales transactions by location
        /// </summary>
        /// <param name="locationId">Location ID</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of sales transactions</returns>
        Task<List<SalesTransaction>> GetSalesByLocationAsync(int locationId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets sales transactions by customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of sales transactions</returns>
        Task<List<SalesTransaction>> GetSalesByCustomerAsync(int customerId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets sales transactions by payment method
        /// </summary>
        /// <param name="paymentMethod">Payment method</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of sales transactions</returns>
        Task<List<SalesTransaction>> GetSalesByPaymentMethodAsync(string paymentMethod, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets top selling items for a period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="count">Number of top items to return</param>
        /// <returns>List of top selling items</returns>
        Task<List<TopSellingItem>> GetTopSellingItemsAsync(DateTime startDate, DateTime endDate, int count = 10);

        /// <summary>
        /// Gets sales summary for a period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Sales summary</returns>
        Task<SalesSummary> GetSalesSummaryAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets hourly sales data for a specific date
        /// </summary>
        /// <param name="date">Date to analyze</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Hourly sales data</returns>
        Task<List<HourlySales>> GetHourlySalesAsync(DateTime date, int? locationId = null);

        /// <summary>
        /// Gets daily sales data for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Daily sales data</returns>
        Task<List<DailySales>> GetDailySalesAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets monthly sales data for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Monthly sales data</returns>
        Task<List<MonthlySales>> GetMonthlySalesAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets sales by category for a period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Sales by category</returns>
        Task<List<CategorySales>> GetSalesByCategoryAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets refund data for a period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Refund data</returns>
        Task<List<RefundData>> GetRefundsAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets void transaction data for a period
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Void transaction data</returns>
        Task<List<VoidTransactionData>> GetVoidTransactionsAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets sales performance metrics
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Performance metrics</returns>
        Task<SalesPerformanceMetrics> GetPerformanceMetricsAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets customer analytics data
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Customer analytics</returns>
        Task<CustomerAnalytics> GetCustomerAnalyticsAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets inventory impact from sales
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Inventory impact data</returns>
        Task<List<InventoryImpact>> GetInventoryImpactAsync(DateTime startDate, DateTime endDate, int? locationId = null);

        /// <summary>
        /// Gets sales forecast data
        /// </summary>
        /// <param name="forecastDays">Number of days to forecast</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Sales forecast</returns>
        Task<SalesForecast> GetSalesForecastAsync(int forecastDays = 30, int? locationId = null);

        /// <summary>
        /// Gets sales comparison data between two periods
        /// </summary>
        /// <param name="period1Start">Period 1 start date</param>
        /// <param name="period1End">Period 1 end date</param>
        /// <param name="period2Start">Period 2 start date</param>
        /// <param name="period2End">Period 2 end date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Sales comparison data</returns>
        Task<SalesComparison> GetSalesComparisonAsync(
            DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End,
            int? locationId = null);

        /// <summary>
        /// Exports sales data to a file
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="format">Export format</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Exported file data</returns>
        Task<byte[]> ExportSalesDataAsync(DateTime startDate, DateTime endDate, string format = "CSV", int? locationId = null);

        /// <summary>
        /// Gets sales data for tax reporting
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>Tax reporting data</returns>
        Task<TaxReportData> GetTaxReportDataAsync(DateTime startDate, DateTime endDate, int? locationId = null);
    }

    /// <summary>
    /// Top selling item data
    /// </summary>
    public class TopSellingItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public string SKU { get; set; }
        public int QuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AveragePrice { get; set; }
        public int TransactionCount { get; set; }
    }

    /// <summary>
    /// Hourly sales data
    /// </summary>
    public class HourlySales
    {
        public DateTime Hour { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }

    /// <summary>
    /// Monthly sales data
    /// </summary>
    public class MonthlySales
    {
        public DateTime Month { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal GrowthPercentage { get; set; }
    }

    /// <summary>
    /// Category sales data
    /// </summary>
    public class CategorySales
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public decimal TotalSales { get; set; }
        public int QuantitySold { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// Refund data
    /// </summary>
    public class RefundData
    {
        public int RefundId { get; set; }
        public int OriginalTransactionId { get; set; }
        public DateTime RefundDate { get; set; }
        public decimal RefundAmount { get; set; }
        public string Reason { get; set; }
        public string ProcessedBy { get; set; }
    }

    /// <summary>
    /// Void transaction data
    /// </summary>
    public class VoidTransactionData
    {
        public int TransactionId { get; set; }
        public DateTime VoidDate { get; set; }
        public decimal VoidAmount { get; set; }
        public string Reason { get; set; }
        public string VoidedBy { get; set; }
    }

    /// <summary>
    /// Customer analytics data
    /// </summary>
    public class CustomerAnalytics
    {
        public int TotalCustomers { get; set; }
        public int NewCustomers { get; set; }
        public int ReturningCustomers { get; set; }
        public decimal AverageSpendPerCustomer { get; set; }
        public List<TopCustomer> TopCustomers { get; set; } = new List<TopCustomer>();
    }

    /// <summary>
    /// Inventory impact data
    /// </summary>
    public class InventoryImpact
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public int QuantitySold { get; set; }
        public int RemainingStock { get; set; }
        public decimal StockTurnover { get; set; }
        public bool NeedsReorder { get; set; }
    }

    /// <summary>
    /// Sales forecast data
    /// </summary>
    public class SalesForecast
    {
        public List<ForecastPoint> DailyForecast { get; set; } = new List<ForecastPoint>();
        public decimal PredictedTotalSales { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public string ForecastMethod { get; set; }
    }

    /// <summary>
    /// Forecast data point
    /// </summary>
    public class ForecastPoint
    {
        public DateTime Date { get; set; }
        public decimal PredictedSales { get; set; }
        public decimal LowerBound { get; set; }
        public decimal UpperBound { get; set; }
    }

    /// <summary>
    /// Sales comparison data
    /// </summary>
    public class SalesComparison
    {
        public SalesSummary Period1 { get; set; }
        public SalesSummary Period2 { get; set; }
        public decimal GrowthPercentage { get; set; }
        public decimal DifferenceAmount { get; set; }
        public string TrendDirection { get; set; }
    }

    /// <summary>
    /// Tax report data
    /// </summary>
    public class TaxReportData
    {
        public decimal TotalSales { get; set; }
        public decimal TotalTax { get; set; }
        public decimal NetSales { get; set; }
        public List<TaxBreakdown> TaxBreakdown { get; set; } = new List<TaxBreakdown>();
    }

    /// <summary>
    /// Tax breakdown by rate
    /// </summary>
    public class TaxBreakdown
    {
        public decimal TaxRate { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
    }
}
