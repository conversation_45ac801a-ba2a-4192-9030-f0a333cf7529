using System;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Event arguments for export events
    /// </summary>
    public class ExportEventArgs : EventArgs
    {
        /// <summary>
        /// Export operation ID
        /// </summary>
        public string ExportId { get; set; }
        
        /// <summary>
        /// Export format
        /// </summary>
        public ExportFormat Format { get; set; }
        
        /// <summary>
        /// Export status
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// Error message if export failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Timestamp of the event
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Event arguments for export progress events
    /// </summary>
    public class ExportProgressEventArgs : EventArgs
    {
        /// <summary>
        /// Export operation ID
        /// </summary>
        public string ExportId { get; set; }
        
        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int Progress { get; set; }
        
        /// <summary>
        /// Current operation description
        /// </summary>
        public string CurrentOperation { get; set; }
        
        /// <summary>
        /// Timestamp of the progress update
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Export status information
    /// </summary>
    public class ExportStatus
    {
        /// <summary>
        /// Export operation ID
        /// </summary>
        public string ExportId { get; set; }
        
        /// <summary>
        /// Current status
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int Progress { get; set; }
        
        /// <summary>
        /// Error message if failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Start time of the export
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// Completion time of the export
        /// </summary>
        public DateTime? CompletionTime { get; set; }
    }
    
    /// <summary>
    /// Batch export result
    /// </summary>
    public class BatchExportResult
    {
        /// <summary>
        /// Total number of export requests
        /// </summary>
        public int TotalRequests { get; set; }
        
        /// <summary>
        /// Number of successful exports
        /// </summary>
        public int SuccessfulExports { get; set; }
        
        /// <summary>
        /// Number of failed exports
        /// </summary>
        public int FailedExports { get; set; }
        
        /// <summary>
        /// Individual export results
        /// </summary>
        public List<ExportResult> Results { get; set; } = new List<ExportResult>();
    }
    
    /// <summary>
    /// Individual export result
    /// </summary>
    public class ExportResult
    {
        /// <summary>
        /// Request ID
        /// </summary>
        public string RequestId { get; set; }
        
        /// <summary>
        /// Whether the export was successful
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// Exported data (if successful)
        /// </summary>
        public byte[] Data { get; set; }
        
        /// <summary>
        /// Error message (if failed)
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
