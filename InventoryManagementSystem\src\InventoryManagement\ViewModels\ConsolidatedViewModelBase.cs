using InventoryManagement.Commands;
using InventoryManagement.Infrastructure.MVVM;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// Consolidated base class for all ViewModels that merges functionality from multiple base ViewModels
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This class consolidates:
    /// - ViewModelBase
    /// - Base.BaseViewModel
    /// - Infrastructure.MVVM.ViewModelBase
    /// </summary>
    public abstract class ConsolidatedViewModelBase : INotifyPropertyChanged, IDisposable
    {
        private readonly Dictionary<string, object> _propertyValues = new Dictionary<string, object>();
        private bool _isLoading;
        private bool _isBusy;
        private string _busyMessage = string.Empty;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private string _warningMessage = string.Empty;
        private string _infoMessage = string.Empty;
        private string _statusMessage = string.Empty;
        private bool _hasError;
        private bool _disposed;

        #region Properties

        /// <summary>
        /// Indicates if the ViewModel is currently loading data
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Indicates if the ViewModel is currently busy performing an operation
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Message to display when the ViewModel is busy
        /// </summary>
        public string BusyMessage
        {
            get => _busyMessage;
            set => SetProperty(ref _busyMessage, value);
        }

        /// <summary>
        /// Error message to display to the user
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                SetProperty(ref _errorMessage, value);
                HasError = !string.IsNullOrEmpty(value);
                OnPropertyChanged(nameof(HasMessages));
            }
        }

        /// <summary>
        /// Success message to display to the user
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set
            {
                SetProperty(ref _successMessage, value);
                OnPropertyChanged(nameof(HasMessages));
            }
        }

        /// <summary>
        /// Warning message to display to the user
        /// </summary>
        public string WarningMessage
        {
            get => _warningMessage;
            set
            {
                SetProperty(ref _warningMessage, value);
                OnPropertyChanged(nameof(HasMessages));
            }
        }

        /// <summary>
        /// Information message to display to the user
        /// </summary>
        public string InfoMessage
        {
            get => _infoMessage;
            set
            {
                SetProperty(ref _infoMessage, value);
                OnPropertyChanged(nameof(HasMessages));
            }
        }

        /// <summary>
        /// Status message for the ViewModel
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Indicates if there is an error
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            private set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// Indicates if there are any messages to display
        /// </summary>
        public bool HasMessages => !string.IsNullOrEmpty(ErrorMessage) || 
                                  !string.IsNullOrEmpty(SuccessMessage) || 
                                  !string.IsNullOrEmpty(WarningMessage) || 
                                  !string.IsNullOrEmpty(InfoMessage);

        #endregion

        #region Commands

        /// <summary>
        /// Command to clear all messages
        /// </summary>
        public ICommand ClearMessagesCommand { get; }

        /// <summary>
        /// Command to refresh the ViewModel data
        /// </summary>
        public ICommand RefreshCommand { get; protected set; }

        #endregion

        #region Constructor

        protected ConsolidatedViewModelBase()
        {
            ClearMessagesCommand = new RelayCommand(() => ClearMessages());
            RefreshCommand = new AsyncRelayCommand(async () => await RefreshAsync());
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets a property value and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value</param>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>True if the value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Sets a property value using a dictionary and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="value">New value</param>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>True if the value was changed, false otherwise</returns>
        protected bool SetProperty<T>(T value, [CallerMemberName] string propertyName = null)
        {
            if (_propertyValues.TryGetValue(propertyName, out var currentValue) && 
                EqualityComparer<T>.Default.Equals((T)currentValue, value))
                return false;

            _propertyValues[propertyName] = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Gets a property value from the dictionary
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="defaultValue">Default value if property is not set</param>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>Property value</returns>
        protected T GetProperty<T>(T defaultValue = default, [CallerMemberName] string propertyName = null)
        {
            if (_propertyValues.TryGetValue(propertyName, out var value))
                return (T)value;

            return defaultValue;
        }

        #endregion

        #region Message Management

        /// <summary>
        /// Clears all messages
        /// </summary>
        public void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
            WarningMessage = string.Empty;
            InfoMessage = string.Empty;
        }

        /// <summary>
        /// Sets an error message and clears other messages
        /// </summary>
        /// <param name="message">Error message</param>
        protected void SetError(string message)
        {
            ClearMessages();
            ErrorMessage = message;
        }

        /// <summary>
        /// Sets a success message and clears other messages
        /// </summary>
        /// <param name="message">Success message</param>
        protected void SetSuccess(string message)
        {
            ClearMessages();
            SuccessMessage = message;
        }

        /// <summary>
        /// Sets a warning message and clears other messages
        /// </summary>
        /// <param name="message">Warning message</param>
        protected void SetWarning(string message)
        {
            ClearMessages();
            WarningMessage = message;
        }

        /// <summary>
        /// Sets an information message and clears other messages
        /// </summary>
        /// <param name="message">Information message</param>
        protected void SetInfo(string message)
        {
            ClearMessages();
            InfoMessage = message;
        }

        #endregion

        #region Async Operations

        /// <summary>
        /// Executes an async operation with loading state management
        /// </summary>
        /// <param name="operation">Operation to execute</param>
        /// <param name="loadingMessage">Message to display while loading</param>
        /// <returns>Task representing the operation</returns>
        protected async Task ExecuteAsync(Func<Task> operation, string loadingMessage = "Loading...")
        {
            try
            {
                IsLoading = true;
                BusyMessage = loadingMessage;
                ClearMessages();

                await operation();
            }
            catch (Exception ex)
            {
                SetError($"An error occurred: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
                BusyMessage = string.Empty;
            }
        }

        /// <summary>
        /// Executes an async operation with loading state management and returns a result
        /// </summary>
        /// <typeparam name="T">Type of the result</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="loadingMessage">Message to display while loading</param>
        /// <returns>Task representing the operation with result</returns>
        protected async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string loadingMessage = "Loading...")
        {
            try
            {
                IsLoading = true;
                BusyMessage = loadingMessage;
                ClearMessages();

                return await operation();
            }
            catch (Exception ex)
            {
                SetError($"An error occurred: {ex.Message}");
                return default(T);
            }
            finally
            {
                IsLoading = false;
                BusyMessage = string.Empty;
            }
        }

        /// <summary>
        /// Virtual method for refreshing ViewModel data
        /// </summary>
        /// <returns>Task representing the refresh operation</returns>
        protected virtual async Task RefreshAsync()
        {
            // Override in derived classes to implement refresh logic
            await Task.CompletedTask;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the ViewModel
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the ViewModel
        /// </summary>
        /// <param name="disposing">True if disposing, false if finalizing</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose managed resources
                _propertyValues.Clear();
                _disposed = true;
            }
        }

        #endregion
    }
}
