using System;
using System.Collections.Generic;
using InventoryManagement.Models.Reports;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a comprehensive sales report
    /// </summary>
    public class SalesReport
    {
        /// <summary>
        /// Unique identifier for the report
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Date when the report was generated
        /// </summary>
        public DateTime ReportDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Start date of the report period
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// End date of the report period
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Total sales amount for the period
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// Total number of transactions
        /// </summary>
        public int TotalTransactions { get; set; }

        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }

        /// <summary>
        /// Total number of items sold
        /// </summary>
        public int TotalItemsSold { get; set; }

        /// <summary>
        /// Total number of unique customers
        /// </summary>
        public int UniqueCustomers { get; set; }

        /// <summary>
        /// List of top customers by sales volume
        /// </summary>
        public List<TopCustomer> TopCustomers { get; set; } = new List<TopCustomer>();

        /// <summary>
        /// List of top products by sales volume
        /// </summary>
        public List<TopProduct> TopProducts { get; set; } = new List<TopProduct>();

        /// <summary>
        /// Daily sales breakdown
        /// </summary>
        public List<DailySales> DailySalesBreakdown { get; set; } = new List<DailySales>();

        /// <summary>
        /// Sales by payment method
        /// </summary>
        public Dictionary<string, decimal> SalesByPaymentMethod { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// Sales by category
        /// </summary>
        public Dictionary<string, decimal> SalesByCategory { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// Sales by user/employee
        /// </summary>
        public Dictionary<string, decimal> SalesByUser { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// Hourly sales distribution
        /// </summary>
        public Dictionary<int, decimal> HourlySales { get; set; } = new Dictionary<int, decimal>();

        /// <summary>
        /// Growth percentage compared to previous period
        /// </summary>
        public decimal? GrowthPercentage { get; set; }

        /// <summary>
        /// Number of voided transactions
        /// </summary>
        public int VoidedTransactions { get; set; }

        /// <summary>
        /// Total amount of voided transactions
        /// </summary>
        public decimal VoidedAmount { get; set; }

        /// <summary>
        /// Number of refunded transactions
        /// </summary>
        public int RefundedTransactions { get; set; }

        /// <summary>
        /// Total amount of refunded transactions
        /// </summary>
        public decimal RefundedAmount { get; set; }

        /// <summary>
        /// Best performing day
        /// </summary>
        public DateTime? BestDay { get; set; }

        /// <summary>
        /// Best day sales amount
        /// </summary>
        public decimal BestDayAmount { get; set; }

        /// <summary>
        /// Worst performing day
        /// </summary>
        public DateTime? WorstDay { get; set; }

        /// <summary>
        /// Worst day sales amount
        /// </summary>
        public decimal WorstDayAmount { get; set; }

        /// <summary>
        /// Report parameters used to generate this report
        /// </summary>
        public SalesReportParameters? Parameters { get; set; }

        /// <summary>
        /// User who generated the report
        /// </summary>
        public int? GeneratedByUserId { get; set; }

        /// <summary>
        /// Additional notes or comments about the report
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Report format
        /// </summary>
        public SalesReportFormat Format { get; set; } = SalesReportFormat.Standard;

        /// <summary>
        /// Whether this report has been exported
        /// </summary>
        public bool IsExported { get; set; } = false;

        /// <summary>
        /// Export format if exported
        /// </summary>
        public ExportFormat? ExportFormat { get; set; }

        /// <summary>
        /// Export file path if exported to file
        /// </summary>
        public string? ExportFilePath { get; set; }

        /// <summary>
        /// When the report was exported
        /// </summary>
        public DateTime? ExportedAt { get; set; }
    }

    /// <summary>
    /// Represents a top product in sales reports
    /// </summary>
    public class TopProduct
    {
        /// <summary>
        /// Product ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Product name
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// Product SKU
        /// </summary>
        public string? SKU { get; set; }

        /// <summary>
        /// Total quantity sold
        /// </summary>
        public int QuantitySold { get; set; }

        /// <summary>
        /// Total sales amount for this product
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// Average selling price
        /// </summary>
        public decimal AveragePrice { get; set; }

        /// <summary>
        /// Number of transactions involving this product
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// Percentage of total sales
        /// </summary>
        public decimal SalesPercentage { get; set; }

        /// <summary>
        /// Product category
        /// </summary>
        public string? Category { get; set; }
    }
}
