using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Infrastructure.Notifications;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Consolidated dashboard service that merges functionality from multiple dashboard services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - DashboardService
    /// - BaseDashboardService  
    /// - SalesDashboardService
    /// - InventoryDashboardService
    /// - ProductsDashboardService
    /// - TransfersDashboardService
    /// - DefectiveItemsDashboardService
    /// - ExchangeDashboardService
    /// - SettingsDashboardService
    /// </summary>
    public class ConsolidatedDashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly INotificationService _notificationService;
        private readonly IOfflineInventoryMonitoringService _inventoryMonitoringService;
        private readonly IAuditService _auditService;
        private readonly IUserPreferencesService _userPreferencesService;
        private readonly ICacheService _cacheService;
        private readonly IOfflineReportingService _reportingService;
        private readonly ILogger<ConsolidatedDashboardService> _logger;

        public ConsolidatedDashboardService(
            ApplicationDbContext dbContext,
            INotificationService notificationService,
            IOfflineInventoryMonitoringService inventoryMonitoringService,
            IAuditService auditService,
            IUserPreferencesService userPreferencesService,
            ICacheService cacheService,
            IOfflineReportingService reportingService,
            ILogger<ConsolidatedDashboardService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _inventoryMonitoringService = inventoryMonitoringService ?? throw new ArgumentNullException(nameof(inventoryMonitoringService));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _userPreferencesService = userPreferencesService ?? throw new ArgumentNullException(nameof(userPreferencesService));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _reportingService = reportingService ?? throw new ArgumentNullException(nameof(reportingService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Core Dashboard Methods (from BaseDashboardService)

        /// <summary>
        /// Gets summary statistics common to all dashboards
        /// </summary>
        /// <param name="userId">ID of the user requesting the dashboard</param>
        /// <returns>Dashboard summary data</returns>
        public virtual async Task<DashboardSummary> GetDashboardSummaryAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Getting dashboard summary for user {UserId}", userId);

                var summary = new DashboardSummary
                {
                    TotalItems = await _dbContext.Items.CountAsync(i => i.IsActive),
                    LowStockItems = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity <= i.MinimumStockLevel)),
                    TotalLocations = await _dbContext.Locations.CountAsync(l => l.IsActive),
                    LastRefreshed = DateTime.Now
                };
                
                // Get today's transactions
                var today = DateTime.Today;
                summary.TodayTransactions = await _dbContext.Transactions
                    .CountAsync(t => t.TransactionDate.Date == today);
                
                // Get user preferences for dashboard customization
                var preferences = await _userPreferencesService.GetUserPreferencesAsync(userId);
                summary.UserPreferences = preferences?.DashboardPreferences;
                
                return summary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary for user {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Gets inventory metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Inventory metrics</returns>
        public async Task<InventoryMetrics> GetInventoryMetricsAsync(TimeSpan timeSpan)
        {
            try
            {
                _logger.LogInformation("Getting inventory metrics for timespan {TimeSpan}", timeSpan);

                var cutoffDate = DateTime.Now.Subtract(timeSpan);
                
                var metrics = new InventoryMetrics
                {
                    TotalItems = await _dbContext.Items.CountAsync(i => i.IsActive),
                    TotalValue = await _dbContext.Items
                        .Where(i => i.IsActive)
                        .SumAsync(i => i.StockByLocation.Sum(s => s.Quantity * i.UnitPrice)),
                    LowStockCount = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity <= i.MinimumStockLevel)),
                    OutOfStockCount = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.All(s => s.Quantity == 0)),
                    RecentMovements = await _dbContext.Transactions
                        .Where(t => t.TransactionDate >= cutoffDate)
                        .CountAsync(),
                    TimePeriod = timeSpan
                };

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory metrics");
                throw;
            }
        }

        #endregion

        #region Sales Dashboard Methods

        /// <summary>
        /// Gets sales metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Sales metrics</returns>
        public async Task<SalesMetrics> GetSalesMetricsAsync(TimeSpan timeSpan)
        {
            try
            {
                _logger.LogInformation("Getting sales metrics for timespan {TimeSpan}", timeSpan);

                var cutoffDate = DateTime.Now.Subtract(timeSpan);
                
                var salesTransactions = await _dbContext.Transactions
                    .Where(t => t.TransactionDate >= cutoffDate && t.TransactionType == TransactionType.Sale)
                    .ToListAsync();

                var metrics = new SalesMetrics
                {
                    TotalSales = salesTransactions.Count,
                    TotalRevenue = salesTransactions.Sum(t => t.TotalAmount),
                    AverageTransactionValue = salesTransactions.Any() ? 
                        salesTransactions.Average(t => t.TotalAmount) : 0,
                    TotalItemsSold = salesTransactions.Sum(t => t.TransactionDetails.Sum(d => d.Quantity)),
                    TimePeriod = timeSpan
                };

                // Get top selling items
                metrics.TopSellingItems = await GetTopSellingItemsAsync(cutoffDate, DateTime.Now, 10);

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales metrics");
                throw;
            }
        }

        /// <summary>
        /// Gets top selling items for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="count">Number of items to return</param>
        /// <returns>List of top selling items</returns>
        public async Task<List<TopSellingItem>> GetTopSellingItemsAsync(DateTime startDate, DateTime endDate, int count)
        {
            try
            {
                var topItems = await _dbContext.TransactionDetails
                    .Where(td => td.Transaction.TransactionDate >= startDate && 
                                td.Transaction.TransactionDate <= endDate &&
                                td.Transaction.TransactionType == TransactionType.Sale)
                    .GroupBy(td => new { td.ItemId, td.Item.Name, td.Item.SKU })
                    .Select(g => new TopSellingItem
                    {
                        ItemId = g.Key.ItemId,
                        ItemName = g.Key.Name,
                        SKU = g.Key.SKU,
                        QuantitySold = g.Sum(td => td.Quantity),
                        TotalRevenue = g.Sum(td => td.Quantity * td.UnitPrice)
                    })
                    .OrderByDescending(item => item.QuantitySold)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top selling items");
                throw;
            }
        }

        #endregion

        #region Inventory Dashboard Methods

        /// <summary>
        /// Gets low stock items for inventory dashboard
        /// </summary>
        /// <param name="locationId">Optional location filter</param>
        /// <returns>List of low stock items</returns>
        public async Task<List<LowStockItem>> GetLowStockItemsAsync(int? locationId = null)
        {
            try
            {
                var query = _dbContext.Items
                    .Where(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity <= i.MinimumStockLevel));

                if (locationId.HasValue)
                {
                    query = query.Where(i => i.StockByLocation.Any(s => s.LocationId == locationId.Value));
                }

                var lowStockItems = await query
                    .Select(i => new LowStockItem
                    {
                        ItemId = i.Id,
                        ItemName = i.Name,
                        SKU = i.SKU,
                        CurrentStock = i.StockByLocation.Sum(s => s.Quantity),
                        MinimumStock = i.MinimumStockLevel,
                        ReorderLevel = i.ReorderLevel,
                        Category = i.Category.Name
                    })
                    .OrderBy(i => i.CurrentStock)
                    .ToListAsync();

                return lowStockItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                throw;
            }
        }

        /// <summary>
        /// Gets inventory status summary
        /// </summary>
        /// <returns>Inventory status summary</returns>
        public async Task<InventoryStatusSummary> GetInventoryStatusSummaryAsync()
        {
            try
            {
                var summary = new InventoryStatusSummary
                {
                    TotalItems = await _dbContext.Items.CountAsync(i => i.IsActive),
                    InStockItems = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity > 0)),
                    OutOfStockItems = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.All(s => s.Quantity == 0)),
                    LowStockItems = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity <= i.MinimumStockLevel)),
                    TotalValue = await _dbContext.Items
                        .Where(i => i.IsActive)
                        .SumAsync(i => i.StockByLocation.Sum(s => s.Quantity * i.UnitPrice)),
                    LastUpdated = DateTime.Now
                };

                return summary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory status summary");
                throw;
            }
        }

        #endregion

        #region Transfer Dashboard Methods

        /// <summary>
        /// Gets pending transfers for transfers dashboard
        /// </summary>
        /// <returns>List of pending transfers</returns>
        public async Task<List<PendingTransfer>> GetPendingTransfersAsync()
        {
            try
            {
                var pendingTransfers = await _dbContext.InventoryTransfers
                    .Where(t => t.Status == TransferStatus.Pending || t.Status == TransferStatus.InTransit)
                    .Select(t => new PendingTransfer
                    {
                        TransferId = t.Id,
                        TransferNumber = t.TransferNumber,
                        FromLocation = t.FromLocation.Name,
                        ToLocation = t.ToLocation.Name,
                        ItemCount = t.TransferItems.Count,
                        TotalQuantity = t.TransferItems.Sum(ti => ti.Quantity),
                        RequestedDate = t.RequestedDate,
                        Status = t.Status,
                        RequestedBy = t.RequestedByUser.FullName
                    })
                    .OrderBy(t => t.RequestedDate)
                    .ToListAsync();

                return pendingTransfers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending transfers");
                throw;
            }
        }

        /// <summary>
        /// Gets transfer metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Transfer metrics</returns>
        public async Task<TransferMetrics> GetTransferMetricsAsync(TimeSpan timeSpan)
        {
            try
            {
                var cutoffDate = DateTime.Now.Subtract(timeSpan);

                var metrics = new TransferMetrics
                {
                    TotalTransfers = await _dbContext.InventoryTransfers
                        .CountAsync(t => t.RequestedDate >= cutoffDate),
                    CompletedTransfers = await _dbContext.InventoryTransfers
                        .CountAsync(t => t.RequestedDate >= cutoffDate && t.Status == TransferStatus.Completed),
                    PendingTransfers = await _dbContext.InventoryTransfers
                        .CountAsync(t => t.Status == TransferStatus.Pending || t.Status == TransferStatus.InTransit),
                    TotalItemsTransferred = await _dbContext.InventoryTransfers
                        .Where(t => t.RequestedDate >= cutoffDate && t.Status == TransferStatus.Completed)
                        .SelectMany(t => t.TransferItems)
                        .SumAsync(ti => ti.Quantity),
                    TimePeriod = timeSpan
                };

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transfer metrics");
                throw;
            }
        }

        #endregion

        #region Defective Items Dashboard Methods

        /// <summary>
        /// Gets defective items metrics for the dashboard
        /// </summary>
        /// <param name="timeSpan">Time period for metrics</param>
        /// <returns>Defective items metrics</returns>
        public async Task<DefectiveItemsMetrics> GetDefectiveItemsMetricsAsync(TimeSpan timeSpan)
        {
            try
            {
                var cutoffDate = DateTime.Now.Subtract(timeSpan);

                var metrics = new DefectiveItemsMetrics
                {
                    TotalDefectiveItems = await _dbContext.DefectiveItems
                        .CountAsync(d => d.ReportedDate >= cutoffDate),
                    PendingInspection = await _dbContext.DefectiveItems
                        .CountAsync(d => d.Status == DefectiveItemStatus.Reported),
                    UnderRepair = await _dbContext.DefectiveItems
                        .CountAsync(d => d.Status == DefectiveItemStatus.UnderRepair),
                    Disposed = await _dbContext.DefectiveItems
                        .CountAsync(d => d.ReportedDate >= cutoffDate && d.Status == DefectiveItemStatus.Disposed),
                    TotalLossValue = await _dbContext.DefectiveItems
                        .Where(d => d.ReportedDate >= cutoffDate && d.Status == DefectiveItemStatus.Disposed)
                        .SumAsync(d => d.EstimatedLoss ?? 0),
                    TimePeriod = timeSpan
                };

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting defective items metrics");
                throw;
            }
        }

        #endregion
    }
}
