using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Data import service interface
    /// </summary>
    public interface IDataImportService
    {
        Task<ImportResult> ImportDataAsync(string filePath);
        Task<ImportResult> ImportProductsAsync(string filePath);
        Task<ImportResult> ImportCustomersAsync(string filePath);
    }

    /// <summary>
    /// Excel template service interface
    /// </summary>
    public interface IExcelTemplateService
    {
        Task<string> GenerateProductTemplateAsync();
        Task<string> GenerateCustomerTemplateAsync();
        Task<bool> ValidateTemplateAsync(string filePath);
    }

    /// <summary>
    /// View factory interface
    /// </summary>
    public interface IViewFactory
    {
        object CreateView(string viewName);
        T CreateView<T>() where T : class;
    }

    /// <summary>
    /// Navigation aware interface
    /// </summary>
    public interface INavigationAware
    {
        void OnNavigatedTo(object parameter);
        void OnNavigatedFrom();
    }

    /// <summary>
    /// Network status service interface
    /// </summary>
    public interface INetworkStatusService
    {
        bool IsConnected { get; }
        event EventHandler<NetworkStatusChangedEventArgs> NetworkStatusChanged;
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
    }

    /// <summary>
    /// Localization service interface
    /// </summary>
    public interface ILocalizationService
    {
        string GetString(string key);
        string GetString(string key, params object[] args);
        void SetLanguage(string languageCode);
    }

    /// <summary>
    /// Network status changed event args
    /// </summary>
    public class NetworkStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string ConnectionType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Connection status changed event args
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Simple localization service implementation
    /// </summary>
    public class LocalizationService : ILocalizationService
    {
        private readonly Dictionary<string, string> _strings = new Dictionary<string, string>();

        public string GetString(string key)
        {
            return _strings.TryGetValue(key, out var value) ? value : key;
        }

        public string GetString(string key, params object[] args)
        {
            var format = GetString(key);
            return string.Format(format, args);
        }

        public void SetLanguage(string languageCode)
        {
            // Simple implementation - no language switching in offline mode
        }
    }
}
