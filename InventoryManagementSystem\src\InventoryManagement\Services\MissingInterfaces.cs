using InventoryManagement.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Data import service interface
    /// </summary>
    public interface IDataImportService
    {
        Task<ImportResult> ImportDataAsync(string filePath);
        Task<ImportResult> ImportProductsAsync(string filePath);
        Task<ImportResult> ImportCustomersAsync(string filePath);
    }

    /// <summary>
    /// Excel template service interface
    /// </summary>
    public interface IExcelTemplateService
    {
        Task<string> GenerateProductTemplateAsync();
        Task<string> GenerateCustomerTemplateAsync();
        Task<bool> ValidateTemplateAsync(string filePath);
    }

    /// <summary>
    /// View factory interface
    /// </summary>
    public interface IViewFactory
    {
        object CreateView(string viewName);
        T CreateView<T>() where T : class;
    }

    /// <summary>
    /// Navigation aware interface
    /// </summary>
    public interface INavigationAware
    {
        void OnNavigatedTo(object parameter);
        void OnNavigatedFrom();
    }

    /// <summary>
    /// Network status service interface
    /// </summary>
    public interface INetworkStatusService
    {
        bool IsConnected { get; }
        event EventHandler<NetworkStatusChangedEventArgs> NetworkStatusChanged;
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
    }

    // Note: ILocalizationService interface is defined in Services/ILocalizationService.cs

    /// <summary>
    /// Network status changed event args
    /// </summary>
    public class NetworkStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string ConnectionType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Connection status changed event args
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Simple localization service implementation
    /// </summary>
    public class LocalizationService : ILocalizationService
    {
        private readonly Dictionary<string, string> _strings = new Dictionary<string, string>();
        private CultureInfo _currentCulture = CultureInfo.CurrentCulture;

        public string GetString(string key, params object[] args)
        {
            var format = _strings.TryGetValue(key, out var value) ? value : key;
            return args?.Length > 0 ? string.Format(format, args) : format;
        }

        public string GetString(string key, CultureInfo culture, params object[] args)
        {
            // Simple implementation - ignore culture for offline mode
            return GetString(key, args);
        }

        public CultureInfo GetCurrentCulture()
        {
            return _currentCulture;
        }

        public void SetCurrentCulture(string cultureName)
        {
            try
            {
                _currentCulture = CultureInfo.GetCultureInfo(cultureName);
            }
            catch
            {
                // Fallback to current culture if invalid
                _currentCulture = CultureInfo.CurrentCulture;
            }
        }

        public List<CultureInfo> GetAvailableCultures()
        {
            return new List<CultureInfo> { CultureInfo.GetCultureInfo("en-US"), CultureInfo.GetCultureInfo("ar-SA") };
        }

        public string FormatNumber(decimal value, string format = null)
        {
            return value.ToString(format ?? "N2", _currentCulture);
        }

        public string FormatDate(DateTime date, string format = null)
        {
            return date.ToString(format ?? "d", _currentCulture);
        }

        public string FormatCurrency(decimal value, string currencyCode = null)
        {
            return value.ToString("C", _currentCulture);
        }

        public string GetLocalizedProperty(Type modelType, string propertyName)
        {
            return propertyName; // Simple implementation
        }

        public async Task SaveUserLanguagePreferenceAsync(int userId, string languageCode)
        {
            // Simple implementation - no persistence in offline mode
            await Task.CompletedTask;
        }

        public async Task<string> GetUserLanguagePreferenceAsync(int userId)
        {
            // Simple implementation - return current culture
            await Task.CompletedTask;
            return _currentCulture.Name;
        }
    }
}
