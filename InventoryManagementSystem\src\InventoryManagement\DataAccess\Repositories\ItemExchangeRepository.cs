using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Repository implementation for ItemExchange operations
    /// </summary>
    public class ItemExchangeRepository : Repository<ItemExchange>, IItemExchangeRepository
    {
        private readonly ILogger<ItemExchangeRepository> _logger;

        public ItemExchangeRepository(ApplicationDbContext context, ILogger<ItemExchangeRepository> logger) 
            : base(context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets item exchanges by basement manager ID
        /// </summary>
        /// <param name="basementManagerId">Basement manager ID</param>
        /// <returns>Item exchanges managed by the specified user</returns>
        public async Task<IEnumerable<ItemExchange>> GetByBasementManagerIdAsync(int basementManagerId)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.BasementManagerId == basementManagerId)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by basement manager ID: {BasementManagerId}", basementManagerId);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchanges by cashier ID
        /// </summary>
        /// <param name="cashierId">Cashier ID</param>
        /// <returns>Item exchanges processed by the specified cashier</returns>
        public async Task<IEnumerable<ItemExchange>> GetByCashierIdAsync(int cashierId)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.CashierId == cashierId)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by cashier ID: {CashierId}", cashierId);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchanges for a specific date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Item exchanges in the specified date range</returns>
        public async Task<IEnumerable<ItemExchange>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.CreatedDate >= startDate && e.CreatedDate <= endDate)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by date range: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchanges by status
        /// </summary>
        /// <param name="status">Exchange status</param>
        /// <returns>Item exchanges with the specified status</returns>
        public async Task<IEnumerable<ItemExchange>> GetByStatusAsync(ExchangeStatus status)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.Status == status)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by status: {Status}", status);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchange by exchange number
        /// </summary>
        /// <param name="exchangeNumber">Exchange number</param>
        /// <returns>Item exchange with the specified number</returns>
        public async Task<ItemExchange> GetByExchangeNumberAsync(string exchangeNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(exchangeNumber))
                    return null;

                return await _context.ItemExchanges
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(e => e.ExchangeNumber == exchangeNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchange by number: {ExchangeNumber}", exchangeNumber);
                throw;
            }
        }

        /// <summary>
        /// Gets pending item exchanges (not yet completed)
        /// </summary>
        /// <returns>Item exchanges that are pending completion</returns>
        public async Task<IEnumerable<ItemExchange>> GetPendingAsync()
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.Status == ExchangeStatus.Initiated)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .AsNoTracking()
                    .OrderBy(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending item exchanges");
                throw;
            }
        }

        /// <summary>
        /// Gets completed item exchanges
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Completed item exchanges in the specified date range</returns>
        public async Task<IEnumerable<ItemExchange>> GetCompletedAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.Status == ExchangeStatus.Completed && 
                               e.CompletedDate >= startDate && 
                               e.CompletedDate <= endDate)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CompletedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting completed item exchanges: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchanges involving a specific item
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Item exchanges involving the specified item</returns>
        public async Task<IEnumerable<ItemExchange>> GetByItemIdAsync(int itemId)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.ExchangeDetails.Any(d => d.ItemId == itemId))
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by item ID: {ItemId}", itemId);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchange statistics for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary with status counts and total values</returns>
        public async Task<Dictionary<string, object>> GetStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var exchanges = await _context.ItemExchanges
                    .Where(e => e.CreatedDate >= startDate && e.CreatedDate <= endDate)
                    .Include(e => e.ExchangeDetails)
                    .AsNoTracking()
                    .ToListAsync();

                var statistics = new Dictionary<string, object>
                {
                    ["TotalExchanges"] = exchanges.Count,
                    ["PendingExchanges"] = exchanges.Count(e => e.Status == ExchangeStatus.Initiated),
                    ["CompletedExchanges"] = exchanges.Count(e => e.Status == ExchangeStatus.Completed),
                    ["CancelledExchanges"] = exchanges.Count(e => e.Status == ExchangeStatus.Cancelled),
                    ["TotalValue"] = exchanges.Sum(e => e.TotalDifference),
                    ["AverageValue"] = exchanges.Any() ? exchanges.Average(e => e.TotalDifference) : 0
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchange statistics: {StartDate} - {EndDate}", startDate, endDate);
                throw;
            }
        }

        /// <summary>
        /// Gets unpaid item exchanges
        /// </summary>
        /// <returns>Item exchanges with outstanding payments</returns>
        public async Task<IEnumerable<ItemExchange>> GetUnpaidAsync()
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => !e.IsPaid && e.TotalDifference > 0)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderBy(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unpaid item exchanges");
                throw;
            }
        }

        /// <summary>
        /// Gets item exchanges by customer name
        /// </summary>
        /// <param name="customerName">Customer name</param>
        /// <returns>Exchanges for the specified customer</returns>
        public async Task<IEnumerable<ItemExchange>> GetByCustomerNameAsync(string customerName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(customerName))
                    return new List<ItemExchange>();

                return await _context.ItemExchanges
                    .Where(e => e.CustomerName.ToLower().Contains(customerName.ToLower()))
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by customer name: {CustomerName}", customerName);
                throw;
            }
        }

        /// <summary>
        /// Gets item exchanges by processed by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Exchanges processed by the specified user</returns>
        public async Task<IEnumerable<ItemExchange>> GetByProcessedByAsync(int userId)
        {
            try
            {
                return await _context.ItemExchanges
                    .Where(e => e.CreatedById == userId)
                    .Include(e => e.ExchangeDetails)
                        .ThenInclude(d => d.Item)
                    .Include(e => e.BasementManager)
                    .Include(e => e.Cashier)
                    .Include(e => e.Payments)
                    .AsNoTracking()
                    .OrderByDescending(e => e.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item exchanges by processed by user: {UserId}", userId);
                throw;
            }
        }
    }
}
