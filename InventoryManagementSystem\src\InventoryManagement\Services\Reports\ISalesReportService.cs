using InventoryManagement.Models;
using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Interface for sales reporting services
    /// </summary>
    public interface ISalesReportService
    {
        /// <summary>
        /// Generate comprehensive sales report
        /// </summary>
        /// <param name="parameters">Report parameters</param>
        /// <returns>Sales report</returns>
        Task<SalesReport> GenerateSalesReportAsync(SalesReportParameters parameters);

        /// <summary>
        /// Generate daily sales summary
        /// </summary>
        /// <param name="date">Date for the report</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Daily sales summary</returns>
        Task<DailySales> GenerateDailySalesSummaryAsync(DateTime date, List<int>? locationIds = null);

        /// <summary>
        /// Generate sales report for date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="grouping">How to group the data</param>
        /// <returns>Sales report</returns>
        Task<SalesReport> GenerateSalesReportByDateRangeAsync(DateTime fromDate, DateTime toDate, SalesReportGrouping grouping = SalesReportGrouping.Daily);

        /// <summary>
        /// Get top selling items for a period
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="count">Number of items to return</param>
        /// <param name="categoryFilter">Category filter (optional)</param>
        /// <returns>List of top selling items</returns>
        Task<List<TopSellingItem>> GetTopSellingItemsAsync(DateTime fromDate, DateTime toDate, int count = 10, string? categoryFilter = null);

        /// <summary>
        /// Get cashier performance report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="cashierIds">Specific cashier IDs (optional)</param>
        /// <returns>List of cashier performance data</returns>
        Task<List<CashierPerformance>> GetCashierPerformanceAsync(DateTime fromDate, DateTime toDate, List<int>? cashierIds = null);

        /// <summary>
        /// Get sales by category
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Category sales breakdown</returns>
        Task<List<CategorySales>> GetSalesByCategoryAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get hourly sales breakdown
        /// </summary>
        /// <param name="date">Date for analysis</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Hourly sales data</returns>
        Task<List<HourlySales>> GetHourlySalesBreakdownAsync(DateTime date, List<int>? locationIds = null);

        /// <summary>
        /// Get sales trends over time
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="grouping">Time grouping</param>
        /// <returns>Sales trend data</returns>
        Task<List<DailySales>> GetSalesTrendsAsync(DateTime fromDate, DateTime toDate, SalesReportGrouping grouping);

        /// <summary>
        /// Get discount usage analytics
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Discount usage data</returns>
        Task<List<DiscountUsage>> GetDiscountUsageAnalyticsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get return analytics
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Return analytics data</returns>
        Task<List<ReturnReason>> GetReturnAnalyticsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get customer analytics
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="count">Number of top customers to return</param>
        /// <returns>Customer analytics data</returns>
        Task<(int UniqueCustomers, int NewCustomers, int ReturningCustomers, List<TopCustomer> TopCustomers)> GetCustomerAnalyticsAsync(DateTime fromDate, DateTime toDate, int count = 10);

        /// <summary>
        /// Export sales report to specified format
        /// </summary>
        /// <param name="report">Sales report to export</param>
        /// <param name="format">Export format</param>
        /// <returns>Exported file content as byte array</returns>
        Task<byte[]> ExportSalesReportAsync(SalesReport report, SalesReportFormat format);

        /// <summary>
        /// Get sales summary for dashboard
        /// </summary>
        /// <param name="date">Date for summary</param>
        /// <returns>Sales summary data</returns>
        Task<SalesSummaryDashboard> GetSalesSummaryForDashboardAsync(DateTime date);

        /// <summary>
        /// Compare sales performance between periods
        /// </summary>
        /// <param name="currentFromDate">Current period start</param>
        /// <param name="currentToDate">Current period end</param>
        /// <param name="previousFromDate">Previous period start</param>
        /// <param name="previousToDate">Previous period end</param>
        /// <returns>Sales comparison data</returns>
        Task<SalesComparison> CompareSalesPerformanceAsync(DateTime currentFromDate, DateTime currentToDate, DateTime previousFromDate, DateTime previousToDate);

        /// <summary>
        /// Get sales forecast based on historical data
        /// </summary>
        /// <param name="forecastDays">Number of days to forecast</param>
        /// <param name="historicalDays">Number of historical days to analyze</param>
        /// <returns>Sales forecast data</returns>
        Task<List<SalesForecast>> GetSalesForecastAsync(int forecastDays = 7, int historicalDays = 30);
    }

    /// <summary>
    /// Sales summary for dashboard display
    /// </summary>
    public class SalesSummaryDashboard
    {
        public DateTime Date { get; set; }
        public decimal TodaysSales { get; set; }
        public decimal YesterdaysSales { get; set; }
        public decimal SalesGrowth { get; set; }
        public int TodaysTransactions { get; set; }
        public int YesterdaysTransactions { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalRefunds { get; set; }
        public int ItemsSold { get; set; }
        public List<TopSellingItem> TopItems { get; set; } = new List<TopSellingItem>();
        public List<HourlySales> HourlyBreakdown { get; set; } = new List<HourlySales>();
    }

    /// <summary>
    /// Sales comparison between periods
    /// </summary>
    public class SalesComparison
    {
        public string CurrentPeriod { get; set; } = string.Empty;
        public string PreviousPeriod { get; set; } = string.Empty;
        public decimal CurrentSales { get; set; }
        public decimal PreviousSales { get; set; }
        public decimal SalesChange { get; set; }
        public decimal SalesChangePercentage { get; set; }
        public int CurrentTransactions { get; set; }
        public int PreviousTransactions { get; set; }
        public int TransactionChange { get; set; }
        public decimal TransactionChangePercentage { get; set; }
        public decimal CurrentAverageTransaction { get; set; }
        public decimal PreviousAverageTransaction { get; set; }
        public decimal AverageTransactionChange { get; set; }
        public decimal AverageTransactionChangePercentage { get; set; }
        public List<CategorySalesComparison> CategoryComparisons { get; set; } = new List<CategorySalesComparison>();
    }

    /// <summary>
    /// Category sales comparison
    /// </summary>
    public class CategorySalesComparison
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal CurrentSales { get; set; }
        public decimal PreviousSales { get; set; }
        public decimal SalesChange { get; set; }
        public decimal SalesChangePercentage { get; set; }
    }

    /// <summary>
    /// Sales forecast data
    /// </summary>
    public class SalesForecast
    {
        public DateTime Date { get; set; }
        public decimal ForecastedSales { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public decimal LowerBound { get; set; }
        public decimal UpperBound { get; set; }
        public string ForecastMethod { get; set; } = string.Empty;
        public List<string> Factors { get; set; } = new List<string>();
    }
}
