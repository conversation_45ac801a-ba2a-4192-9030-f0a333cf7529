using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for data access services
    /// </summary>
    public interface IDataAccessService
    {
        /// <summary>
        /// Gets an entity by its ID
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id">Entity ID</param>
        /// <returns>Entity or null if not found</returns>
        Task<T> GetByIdAsync<T>(int id) where T : class;

        /// <summary>
        /// Gets all entities of a specific type
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <returns>List of entities</returns>
        Task<List<T>> GetAllAsync<T>() where T : class;

        /// <summary>
        /// Gets entities that match a predicate
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="predicate">Filter predicate</param>
        /// <returns>List of matching entities</returns>
        Task<List<T>> GetWhereAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

        /// <summary>
        /// Gets a single entity that matches a predicate
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="predicate">Filter predicate</param>
        /// <returns>Entity or null if not found</returns>
        Task<T> GetSingleAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

        /// <summary>
        /// Gets the first entity that matches a predicate
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="predicate">Filter predicate</param>
        /// <returns>Entity or null if not found</returns>
        Task<T> GetFirstAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

        /// <summary>
        /// Checks if any entity matches a predicate
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="predicate">Filter predicate</param>
        /// <returns>True if any entity matches</returns>
        Task<bool> AnyAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

        /// <summary>
        /// Counts entities that match a predicate
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="predicate">Filter predicate</param>
        /// <returns>Number of matching entities</returns>
        Task<int> CountAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

        /// <summary>
        /// Gets entities with pagination
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="predicate">Optional filter predicate</param>
        /// <returns>Paged result</returns>
        Task<PagedResult<T>> GetPagedAsync<T>(int pageNumber, int pageSize, Expression<Func<T, bool>> predicate = null) where T : class;

        /// <summary>
        /// Adds a new entity
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to add</param>
        /// <returns>Added entity with generated ID</returns>
        Task<T> AddAsync<T>(T entity) where T : class;

        /// <summary>
        /// Adds multiple entities
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities">Entities to add</param>
        /// <returns>Task representing the async operation</returns>
        Task AddRangeAsync<T>(IEnumerable<T> entities) where T : class;

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to update</param>
        /// <returns>Updated entity</returns>
        Task<T> UpdateAsync<T>(T entity) where T : class;

        /// <summary>
        /// Updates multiple entities
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities">Entities to update</param>
        /// <returns>Task representing the async operation</returns>
        Task UpdateRangeAsync<T>(IEnumerable<T> entities) where T : class;

        /// <summary>
        /// Deletes an entity by ID
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id">Entity ID</param>
        /// <returns>True if entity was deleted</returns>
        Task<bool> DeleteAsync<T>(int id) where T : class;

        /// <summary>
        /// Deletes an entity
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to delete</param>
        /// <returns>True if entity was deleted</returns>
        Task<bool> DeleteAsync<T>(T entity) where T : class;

        /// <summary>
        /// Deletes multiple entities
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities">Entities to delete</param>
        /// <returns>Number of entities deleted</returns>
        Task<int> DeleteRangeAsync<T>(IEnumerable<T> entities) where T : class;

        /// <summary>
        /// Deletes entities that match a predicate
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="predicate">Filter predicate</param>
        /// <returns>Number of entities deleted</returns>
        Task<int> DeleteWhereAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

        /// <summary>
        /// Executes a raw SQL query
        /// </summary>
        /// <param name="sql">SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <returns>Number of rows affected</returns>
        Task<int> ExecuteSqlAsync(string sql, params object[] parameters);

        /// <summary>
        /// Executes a raw SQL query and returns results
        /// </summary>
        /// <typeparam name="T">Result type</typeparam>
        /// <param name="sql">SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <returns>Query results</returns>
        Task<List<T>> ExecuteSqlQueryAsync<T>(string sql, params object[] parameters) where T : class;

        /// <summary>
        /// Begins a database transaction
        /// </summary>
        /// <returns>Transaction object</returns>
        Task<IDataTransaction> BeginTransactionAsync();

        /// <summary>
        /// Saves all pending changes
        /// </summary>
        /// <returns>Number of entities affected</returns>
        Task<int> SaveChangesAsync();

        /// <summary>
        /// Reloads an entity from the database
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to reload</param>
        /// <returns>Task representing the async operation</returns>
        Task ReloadAsync<T>(T entity) where T : class;

        /// <summary>
        /// Detaches an entity from the context
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to detach</param>
        void Detach<T>(T entity) where T : class;

        /// <summary>
        /// Attaches an entity to the context
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to attach</param>
        /// <returns>Attached entity</returns>
        T Attach<T>(T entity) where T : class;

        /// <summary>
        /// Gets the current state of an entity
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity to check</param>
        /// <returns>Entity state</returns>
        EntityState GetEntityState<T>(T entity) where T : class;

        /// <summary>
        /// Sets the state of an entity
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity</param>
        /// <param name="state">New state</param>
        void SetEntityState<T>(T entity, EntityState state) where T : class;

        /// <summary>
        /// Checks if the database connection is available
        /// </summary>
        /// <returns>True if connection is available</returns>
        Task<bool> IsConnectionAvailableAsync();

        /// <summary>
        /// Gets database statistics
        /// </summary>
        /// <returns>Database statistics</returns>
        Task<DatabaseStatistics> GetDatabaseStatisticsAsync();

        /// <summary>
        /// Optimizes the database (runs maintenance tasks)
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task OptimizeDatabaseAsync();

        /// <summary>
        /// Backs up the database
        /// </summary>
        /// <param name="backupPath">Path for the backup file</param>
        /// <returns>Task representing the async operation</returns>
        Task BackupDatabaseAsync(string backupPath);

        /// <summary>
        /// Restores the database from a backup
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <returns>Task representing the async operation</returns>
        Task RestoreDatabaseAsync(string backupPath);
    }

    /// <summary>
    /// Interface for database transactions
    /// </summary>
    public interface IDataTransaction : IDisposable
    {
        /// <summary>
        /// Commits the transaction
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task CommitAsync();

        /// <summary>
        /// Rolls back the transaction
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task RollbackAsync();
    }

    /// <summary>
    /// Entity states
    /// </summary>
    public enum EntityState
    {
        Detached,
        Unchanged,
        Added,
        Deleted,
        Modified
    }

    /// <summary>
    /// Database statistics
    /// </summary>
    public class DatabaseStatistics
    {
        /// <summary>
        /// Database size in bytes
        /// </summary>
        public long DatabaseSize { get; set; }

        /// <summary>
        /// Number of tables
        /// </summary>
        public int TableCount { get; set; }

        /// <summary>
        /// Table statistics
        /// </summary>
        public Dictionary<string, TableStatistics> TableStats { get; set; } = new Dictionary<string, TableStatistics>();

        /// <summary>
        /// Last backup date
        /// </summary>
        public DateTime? LastBackupDate { get; set; }

        /// <summary>
        /// Database version
        /// </summary>
        public string DatabaseVersion { get; set; }

        /// <summary>
        /// Connection pool statistics
        /// </summary>
        public ConnectionPoolStatistics ConnectionPool { get; set; } = new ConnectionPoolStatistics();
    }

    /// <summary>
    /// Statistics for a database table
    /// </summary>
    public class TableStatistics
    {
        /// <summary>
        /// Table name
        /// </summary>
        public string TableName { get; set; }

        /// <summary>
        /// Number of rows
        /// </summary>
        public long RowCount { get; set; }

        /// <summary>
        /// Table size in bytes
        /// </summary>
        public long TableSize { get; set; }

        /// <summary>
        /// Index size in bytes
        /// </summary>
        public long IndexSize { get; set; }

        /// <summary>
        /// Last update time
        /// </summary>
        public DateTime? LastUpdated { get; set; }
    }

    /// <summary>
    /// Connection pool statistics
    /// </summary>
    public class ConnectionPoolStatistics
    {
        /// <summary>
        /// Maximum pool size
        /// </summary>
        public int MaxPoolSize { get; set; }

        /// <summary>
        /// Current active connections
        /// </summary>
        public int ActiveConnections { get; set; }

        /// <summary>
        /// Current idle connections
        /// </summary>
        public int IdleConnections { get; set; }

        /// <summary>
        /// Total connections created
        /// </summary>
        public long TotalConnectionsCreated { get; set; }

        /// <summary>
        /// Average connection lifetime
        /// </summary>
        public TimeSpan AverageConnectionLifetime { get; set; }
    }
}
