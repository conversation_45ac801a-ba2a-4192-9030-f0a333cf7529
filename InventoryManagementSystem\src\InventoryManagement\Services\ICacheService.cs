using System;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for cache service
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Gets a cached value by key
        /// </summary>
        /// <typeparam name="T">Type of cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default if not found</returns>
        Task<T> GetAsync<T>(string key);

        /// <summary>
        /// Sets a value in cache
        /// </summary>
        /// <typeparam name="T">Type of value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        /// <returns>Task</returns>
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null);

        /// <summary>
        /// Removes a value from cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Task</returns>
        Task RemoveAsync(string key);

        /// <summary>
        /// Checks if a key exists in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if key exists</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Clears all cached values
        /// </summary>
        /// <returns>Task</returns>
        Task ClearAsync();

        /// <summary>
        /// Gets a cached value by key (synchronous)
        /// </summary>
        /// <typeparam name="T">Type of cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default if not found</returns>
        T Get<T>(string key);

        /// <summary>
        /// Sets a value in cache (synchronous)
        /// </summary>
        /// <typeparam name="T">Type of value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        void Set<T>(string key, T value, TimeSpan? expiration = null);

        /// <summary>
        /// Removes a value from cache (synchronous)
        /// </summary>
        /// <param name="key">Cache key</param>
        void Remove(string key);

        /// <summary>
        /// Checks if a key exists in cache (synchronous)
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if key exists</returns>
        bool Exists(string key);

        /// <summary>
        /// Clears all cached values (synchronous)
        /// </summary>
        void Clear();
    }
}
