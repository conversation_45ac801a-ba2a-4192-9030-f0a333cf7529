using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    /// <summary>
    /// Repository implementation for User operations
    /// </summary>
    public class UserRepository : Repository<User>, IUserRepository
    {
        private readonly ILogger<UserRepository> _logger;

        public UserRepository(ApplicationDbContext context, ILogger<UserRepository> logger) 
            : base(context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a user by username
        /// </summary>
        /// <param name="username">Username to find</param>
        /// <returns>User with the specified username</returns>
        public async Task<User> GetByUsernameAsync(string username)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username))
                {
                    _logger.LogWarning("GetByUsernameAsync called with null or empty username");
                    return null;
                }

                return await _context.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Username.ToLower() == username.ToLower());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by username: {Username}", username);
                throw;
            }
        }

        /// <summary>
        /// Gets a user by email
        /// </summary>
        /// <param name="email">Email address to find</param>
        /// <returns>User with the specified email</returns>
        public async Task<User> GetByEmailAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                {
                    _logger.LogWarning("GetByEmailAsync called with null or empty email");
                    return null;
                }

                return await _context.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email: {Email}", email);
                throw;
            }
        }

        /// <summary>
        /// Gets a user by ID with related data
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User with related data</returns>
        public override async Task<User> GetByIdAsync(object id)
        {
            try
            {
                if (id == null)
                {
                    _logger.LogWarning("GetByIdAsync called with null id");
                    return null;
                }

                return await _context.Users
                    .Include(u => u.UserPermissions)
                    .Include(u => u.Sessions)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Id == (int)id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID: {UserId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all active users
        /// </summary>
        /// <returns>Query for active users</returns>
        public IQueryable<User> GetActiveUsers()
        {
            return _context.Users
                .Where(u => u.Status == UserStatus.Active)
                .AsNoTracking();
        }

        /// <summary>
        /// Gets users by role
        /// </summary>
        /// <param name="role">User role</param>
        /// <returns>Query for users with the specified role</returns>
        public IQueryable<User> GetUsersByRole(UserRole role)
        {
            return _context.Users
                .Where(u => u.Role == role)
                .AsNoTracking();
        }

        /// <summary>
        /// Checks if a username already exists
        /// </summary>
        /// <param name="username">Username to check</param>
        /// <param name="excludeUserId">User ID to exclude from check (for updates)</param>
        /// <returns>True if username exists</returns>
        public async Task<bool> UsernameExistsAsync(string username, int? excludeUserId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username))
                    return false;

                var query = _context.Users
                    .Where(u => u.Username.ToLower() == username.ToLower());

                if (excludeUserId.HasValue)
                {
                    query = query.Where(u => u.Id != excludeUserId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if username exists: {Username}", username);
                throw;
            }
        }

        /// <summary>
        /// Checks if an email already exists
        /// </summary>
        /// <param name="email">Email to check</param>
        /// <param name="excludeUserId">User ID to exclude from check (for updates)</param>
        /// <returns>True if email exists</returns>
        public async Task<bool> EmailExistsAsync(string email, int? excludeUserId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return false;

                var query = _context.Users
                    .Where(u => u.Email.ToLower() == email.ToLower());

                if (excludeUserId.HasValue)
                {
                    query = query.Where(u => u.Id != excludeUserId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if email exists: {Email}", email);
                throw;
            }
        }
    }
}
