using System;
using System.Collections.Generic;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Summary of inventory status across the system
    /// </summary>
    public class InventoryStatusSummary
    {
        /// <summary>
        /// Date and time when the summary was generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Total number of items in inventory
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Total number of active items
        /// </summary>
        public int ActiveItems { get; set; }

        /// <summary>
        /// Total number of inactive items
        /// </summary>
        public int InactiveItems { get; set; }

        /// <summary>
        /// Total number of items at low stock
        /// </summary>
        public int LowStockItems { get; set; }

        /// <summary>
        /// Total number of items out of stock
        /// </summary>
        public int OutOfStockItems { get; set; }

        /// <summary>
        /// Total number of items overstocked
        /// </summary>
        public int OverstockedItems { get; set; }

        /// <summary>
        /// Total number of items expiring soon
        /// </summary>
        public int ExpiringSoonItems { get; set; }

        /// <summary>
        /// Total number of expired items
        /// </summary>
        public int ExpiredItems { get; set; }

        /// <summary>
        /// Total inventory value at cost
        /// </summary>
        public decimal TotalInventoryValueCost { get; set; }

        /// <summary>
        /// Total inventory value at retail
        /// </summary>
        public decimal TotalInventoryValueRetail { get; set; }

        /// <summary>
        /// Total number of categories
        /// </summary>
        public int TotalCategories { get; set; }

        /// <summary>
        /// Total number of locations
        /// </summary>
        public int TotalLocations { get; set; }

        /// <summary>
        /// Average stock level across all items
        /// </summary>
        public decimal AverageStockLevel { get; set; }

        /// <summary>
        /// Inventory turnover ratio
        /// </summary>
        public decimal InventoryTurnoverRatio { get; set; }

        /// <summary>
        /// Days of inventory on hand
        /// </summary>
        public decimal DaysOfInventoryOnHand { get; set; }

        /// <summary>
        /// Stock accuracy percentage
        /// </summary>
        public decimal StockAccuracyPercentage { get; set; }

        /// <summary>
        /// Number of items that need reordering
        /// </summary>
        public int ItemsNeedingReorder { get; set; }

        /// <summary>
        /// Number of pending stock adjustments
        /// </summary>
        public int PendingStockAdjustments { get; set; }

        /// <summary>
        /// Number of items with discrepancies
        /// </summary>
        public int ItemsWithDiscrepancies { get; set; }

        /// <summary>
        /// Last stock count date
        /// </summary>
        public DateTime? LastStockCountDate { get; set; }

        /// <summary>
        /// Next scheduled stock count date
        /// </summary>
        public DateTime? NextStockCountDate { get; set; }

        /// <summary>
        /// Breakdown by category
        /// </summary>
        public List<CategoryStatusSummary> CategoryBreakdown { get; set; } = new List<CategoryStatusSummary>();

        /// <summary>
        /// Breakdown by location
        /// </summary>
        public List<LocationStatusSummary> LocationBreakdown { get; set; } = new List<LocationStatusSummary>();

        /// <summary>
        /// Top items by value
        /// </summary>
        public List<TopValueItem> TopValueItems { get; set; } = new List<TopValueItem>();

        /// <summary>
        /// Recent inventory movements
        /// </summary>
        public List<RecentMovement> RecentMovements { get; set; } = new List<RecentMovement>();

        /// <summary>
        /// Alerts and notifications
        /// </summary>
        public List<InventoryAlert> Alerts { get; set; } = new List<InventoryAlert>();

        /// <summary>
        /// Performance metrics
        /// </summary>
        public InventoryPerformanceMetrics Performance { get; set; } = new InventoryPerformanceMetrics();

        /// <summary>
        /// Health score of the inventory (0-100)
        /// </summary>
        public decimal HealthScore { get; set; }

        /// <summary>
        /// Overall status (Excellent, Good, Fair, Poor)
        /// </summary>
        public string OverallStatus { get; set; }

        /// <summary>
        /// Key recommendations for improvement
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Category status summary
    /// </summary>
    public class CategoryStatusSummary
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public decimal TotalValue { get; set; }
        public decimal TurnoverRatio { get; set; }
    }

    /// <summary>
    /// Location status summary
    /// </summary>
    public class LocationStatusSummary
    {
        public int LocationId { get; set; }
        public string LocationName { get; set; }
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public decimal TotalValue { get; set; }
        public decimal UtilizationPercentage { get; set; }
    }

    /// <summary>
    /// Top value item
    /// </summary>
    public class TopValueItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public string SKU { get; set; }
        public int Quantity { get; set; }
        public decimal UnitValue { get; set; }
        public decimal TotalValue { get; set; }
        public string CategoryName { get; set; }
    }

    /// <summary>
    /// Recent inventory movement
    /// </summary>
    public class RecentMovement
    {
        public int MovementId { get; set; }
        public DateTime MovementDate { get; set; }
        public string MovementType { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public int Quantity { get; set; }
        public string LocationName { get; set; }
        public string UserName { get; set; }
    }

    /// <summary>
    /// Inventory performance metrics
    /// </summary>
    public class InventoryPerformanceMetrics
    {
        /// <summary>
        /// Stock-out frequency (times per month)
        /// </summary>
        public decimal StockOutFrequency { get; set; }

        /// <summary>
        /// Average time to restock (days)
        /// </summary>
        public decimal AverageRestockTime { get; set; }

        /// <summary>
        /// Inventory accuracy percentage
        /// </summary>
        public decimal InventoryAccuracy { get; set; }

        /// <summary>
        /// Order fulfillment rate percentage
        /// </summary>
        public decimal OrderFulfillmentRate { get; set; }

        /// <summary>
        /// Carrying cost percentage
        /// </summary>
        public decimal CarryingCostPercentage { get; set; }

        /// <summary>
        /// Shrinkage percentage
        /// </summary>
        public decimal ShrinkagePercentage { get; set; }

        /// <summary>
        /// Dead stock percentage
        /// </summary>
        public decimal DeadStockPercentage { get; set; }

        /// <summary>
        /// Forecast accuracy percentage
        /// </summary>
        public decimal ForecastAccuracy { get; set; }
    }
}
