using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using InventoryManagement.Services.Interfaces;
using InventoryManagement.DataAccess;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Implementation of data access service for PostgreSQL database operations
    /// </summary>
    public class DataAccessService : IDataAccessService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<DataAccessService> _logger;
        private readonly string _connectionString;

        public DataAccessService(
            ApplicationDbContext dbContext,
            ILogger<DataAccessService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _connectionString = _dbContext.Database.GetConnectionString();
        }

        public async Task<DataTable> ExecuteQueryAsync(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                _logger.LogDebug("Executing query: {Query}", query);
                
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                using var command = new NpgsqlCommand(query, connection);
                
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }
                
                using var adapter = new NpgsqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);
                
                _logger.LogDebug("Query executed successfully, returned {RowCount} rows", dataTable.Rows.Count);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing query: {Query}", query);
                throw;
            }
        }

        public async Task<int> ExecuteCommandAsync(string command, Dictionary<string, object> parameters = null)
        {
            try
            {
                _logger.LogDebug("Executing command: {Command}", command);
                
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                using var cmd = new NpgsqlCommand(command, connection);
                
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }
                
                var result = await cmd.ExecuteNonQueryAsync();
                _logger.LogDebug("Command executed successfully, affected {RowCount} rows", result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing command: {Command}", command);
                throw;
            }
        }

        public async Task<T> ExecuteScalarAsync<T>(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                _logger.LogDebug("Executing scalar query: {Query}", query);
                
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                using var command = new NpgsqlCommand(query, connection);
                
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }
                
                var result = await command.ExecuteScalarAsync();
                
                if (result == null || result == DBNull.Value)
                {
                    return default(T);
                }
                
                return (T)Convert.ChangeType(result, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing scalar query: {Query}", query);
                throw;
            }
        }

        public async Task<DataTable> GetTableDataAsync(string tableName, string whereClause = null, Dictionary<string, object> parameters = null)
        {
            var query = $"SELECT * FROM {tableName}";
            
            if (!string.IsNullOrEmpty(whereClause))
            {
                query += $" WHERE {whereClause}";
            }
            
            return await ExecuteQueryAsync(query, parameters);
        }

        public async Task<List<string>> GetTableNamesAsync()
        {
            const string query = @"
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name";
            
            var dataTable = await ExecuteQueryAsync(query);
            var tableNames = new List<string>();
            
            foreach (DataRow row in dataTable.Rows)
            {
                tableNames.Add(row["table_name"].ToString());
            }
            
            return tableNames;
        }

        public async Task<DataTable> GetTableSchemaAsync(string tableName)
        {
            const string query = @"
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = @tableName 
                ORDER BY ordinal_position";
            
            var parameters = new Dictionary<string, object>
            {
                { "@tableName", tableName }
            };
            
            return await ExecuteQueryAsync(query, parameters);
        }

        public async Task<bool> TableExistsAsync(string tableName)
        {
            const string query = @"
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = @tableName";
            
            var parameters = new Dictionary<string, object>
            {
                { "@tableName", tableName }
            };
            
            var count = await ExecuteScalarAsync<int>(query, parameters);
            return count > 0;
        }

        public async Task<long> GetRecordCountAsync(string tableName, string whereClause = null, Dictionary<string, object> parameters = null)
        {
            var query = $"SELECT COUNT(*) FROM {tableName}";
            
            if (!string.IsNullOrEmpty(whereClause))
            {
                query += $" WHERE {whereClause}";
            }
            
            return await ExecuteScalarAsync<long>(query, parameters);
        }

        public async Task<DataTable> ExecuteStoredProcedureAsync(string procedureName, Dictionary<string, object> parameters = null)
        {
            var query = $"CALL {procedureName}(";
            
            if (parameters != null && parameters.Count > 0)
            {
                var paramNames = new List<string>();
                foreach (var param in parameters)
                {
                    paramNames.Add($"@{param.Key}");
                }
                query += string.Join(", ", paramNames);
            }
            
            query += ")";
            
            return await ExecuteQueryAsync(query, parameters);
        }

        public async Task<IDbTransaction> BeginTransactionAsync()
        {
            var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();
            return await connection.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync(IDbTransaction transaction)
        {
            if (transaction is NpgsqlTransaction npgsqlTransaction)
            {
                await npgsqlTransaction.CommitAsync();
                await npgsqlTransaction.Connection.CloseAsync();
            }
        }

        public async Task RollbackTransactionAsync(IDbTransaction transaction)
        {
            if (transaction is NpgsqlTransaction npgsqlTransaction)
            {
                await npgsqlTransaction.RollbackAsync();
                await npgsqlTransaction.Connection.CloseAsync();
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                return connection.State == ConnectionState.Open;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection test failed");
                return false;
            }
        }

        public async Task<DatabaseConnectionInfo> GetConnectionInfoAsync()
        {
            try
            {
                var startTime = DateTime.Now;
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                var endTime = DateTime.Now;
                
                var version = await ExecuteScalarAsync<string>("SELECT version()");
                
                return new DatabaseConnectionInfo
                {
                    ServerName = connection.Host,
                    DatabaseName = connection.Database,
                    Version = version,
                    IsConnected = connection.State == ConnectionState.Open,
                    LastConnectionTest = DateTime.Now,
                    ResponseTimeMs = (long)(endTime - startTime).TotalMilliseconds
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting connection info");
                return new DatabaseConnectionInfo
                {
                    IsConnected = false,
                    LastConnectionTest = DateTime.Now,
                    ResponseTimeMs = -1
                };
            }
        }

        public async Task<bool> ExecuteBatchAsync(List<string> commands)
        {
            using var transaction = await BeginTransactionAsync();
            
            try
            {
                foreach (var command in commands)
                {
                    await ExecuteCommandAsync(command);
                }
                
                await CommitTransactionAsync(transaction);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing batch commands");
                await RollbackTransactionAsync(transaction);
                return false;
            }
        }

        public async Task<DataTable> GetReportDataAsync(string reportQuery, Dictionary<string, object> parameters = null)
        {
            return await ExecuteQueryAsync(reportQuery, parameters);
        }

        public async Task<bool> ValidateQueryAsync(string query)
        {
            try
            {
                var explainQuery = $"EXPLAIN {query}";
                await ExecuteQueryAsync(explainQuery);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<DataTable> GetPerformanceMetricsAsync()
        {
            const string query = @"
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public'
                ORDER BY tablename, attname";
            
            return await ExecuteQueryAsync(query);
        }
    }
}
