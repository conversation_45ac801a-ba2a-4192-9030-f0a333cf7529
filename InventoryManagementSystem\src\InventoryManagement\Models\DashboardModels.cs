using System;
using System.Collections.Generic;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Dashboard summary model for consolidated dashboard service
    /// </summary>
    public class DashboardSummary
    {
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int TotalLocations { get; set; }
        public int TodayTransactions { get; set; }
        public DateTime LastRefreshed { get; set; }
        public DashboardPreferences UserPreferences { get; set; }
    }

    /// <summary>
    /// Inventory metrics for dashboard
    /// </summary>
    public class InventoryMetrics
    {
        public int TotalItems { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public int RecentMovements { get; set; }
        public TimeSpan TimePeriod { get; set; }
    }

    /// <summary>
    /// Sales metrics for dashboard
    /// </summary>
    public class SalesMetrics
    {
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int TotalItemsSold { get; set; }
        public TimeSpan TimePeriod { get; set; }
        public List<TopSellingItem> TopSellingItems { get; set; } = new List<TopSellingItem>();
    }

    /// <summary>
    /// Top selling item information
    /// </summary>
    public class TopSellingItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public string SKU { get; set; }
        public int QuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
    }

    /// <summary>
    /// Low stock item information
    /// </summary>
    public class LowStockItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public string SKU { get; set; }
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderLevel { get; set; }
        public string Category { get; set; }
    }

    /// <summary>
    /// Inventory status summary
    /// </summary>
    public class InventoryStatusSummary
    {
        public int TotalItems { get; set; }
        public int InStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int LowStockItems { get; set; }
        public decimal TotalValue { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Pending transfer information
    /// </summary>
    public class PendingTransfer
    {
        public int TransferId { get; set; }
        public string TransferNumber { get; set; }
        public string FromLocation { get; set; }
        public string ToLocation { get; set; }
        public int ItemCount { get; set; }
        public int TotalQuantity { get; set; }
        public DateTime RequestedDate { get; set; }
        public TransferStatus Status { get; set; }
        public string RequestedBy { get; set; }
    }

    /// <summary>
    /// Transfer metrics for dashboard
    /// </summary>
    public class TransferMetrics
    {
        public int TotalTransfers { get; set; }
        public int CompletedTransfers { get; set; }
        public int PendingTransfers { get; set; }
        public int TotalItemsTransferred { get; set; }
        public TimeSpan TimePeriod { get; set; }
    }

    /// <summary>
    /// Defective items metrics
    /// </summary>
    public class DefectiveItemsMetrics
    {
        public int TotalDefectiveItems { get; set; }
        public int PendingInspection { get; set; }
        public int UnderRepair { get; set; }
        public int Disposed { get; set; }
        public decimal TotalLossValue { get; set; }
        public TimeSpan TimePeriod { get; set; }
    }

    /// <summary>
    /// Dashboard preferences for users
    /// </summary>
    public class DashboardPreferences
    {
        public bool ShowSalesMetrics { get; set; } = true;
        public bool ShowInventoryMetrics { get; set; } = true;
        public bool ShowLowStockAlerts { get; set; } = true;
        public bool ShowRecentTransactions { get; set; } = true;
        public string DefaultView { get; set; } = "Overview";
        public int RefreshInterval { get; set; } = 30; // seconds
    }

    /// <summary>
    /// Transfer status enumeration
    /// </summary>
    public enum TransferStatus
    {
        Pending = 1,
        InTransit = 2,
        Completed = 3,
        Cancelled = 4
    }

    /// <summary>
    /// Defective item status enumeration
    /// </summary>
    public enum DefectiveItemStatus
    {
        Reported = 1,
        UnderInspection = 2,
        UnderRepair = 3,
        Repaired = 4,
        Disposed = 5
    }

    /// <summary>
    /// Financial metrics for dashboard
    /// </summary>
    public class FinancialMetrics
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal AccountsReceivable { get; set; }
        public decimal AccountsPayable { get; set; }
        public decimal CashFlow { get; set; }
        public Dictionary<DateTime, decimal> RevenueByDay { get; set; } = new Dictionary<DateTime, decimal>();
        public Dictionary<string, decimal> ExpensesByCategory { get; set; } = new Dictionary<string, decimal>();
        public TimeSpan TimePeriod { get; set; }
    }

    /// <summary>
    /// Supplier metrics for dashboard
    /// </summary>
    public class SupplierMetrics
    {
        public int TotalSuppliers { get; set; }
        public int ActiveSuppliers { get; set; }
        public decimal TotalPurchaseAmount { get; set; }
        public double AverageDeliveryTimeDays { get; set; }
        public decimal OnTimeDeliveryPercentage { get; set; }
        public decimal OrderDefectRate { get; set; }
        public Dictionary<string, decimal> TopSuppliersByAmount { get; set; } = new Dictionary<string, decimal>();
        public TimeSpan TimePeriod { get; set; }
    }
}
