using InventoryManagement.Models;
using System;

namespace InventoryManagement.Events
{
    /// <summary>
    /// Event arguments for notification events
    /// </summary>
    public class NotificationEventArgs : EventArgs
    {
        /// <summary>
        /// The notification title
        /// </summary>
        public string Title { get; }
        
        /// <summary>
        /// The notification message
        /// </summary>
        public string Message { get; }
        
        /// <summary>
        /// The notification type
        /// </summary>
        public NotificationType Type { get; }
        
        /// <summary>
        /// Optional associated data
        /// </summary>
        public object Data { get; }
        
        /// <summary>
        /// The timestamp when the notification was created
        /// </summary>
        public DateTime Timestamp { get; }
        
        /// <summary>
        /// Creates a new notification event args instance
        /// </summary>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="type">The notification type</param>
        /// <param name="data">Optional associated data</param>
        public NotificationEventArgs(string title, string message, NotificationType type, object data = null)
        {
            Title = title;
            Message = message;
            Type = type;
            Data = data;
            Timestamp = DateTime.Now;
        }
    }
    
    /// <summary>
    /// Defines the type of notification event
    /// </summary>
    public enum NotificationEventTypeEnum
    {
        /// <summary>
        /// Informational notification
        /// </summary>
        Information,

        /// <summary>
        /// Success notification
        /// </summary>
        Success,

        /// <summary>
        /// Warning notification
        /// </summary>
        Warning,

        /// <summary>
        /// Error notification
        /// </summary>
        Error,

        /// <summary>
        /// System notification
        /// </summary>
        System
    }
}
