using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Represents a preview of a report before generation
    /// </summary>
    public class ReportPreview
    {
        /// <summary>
        /// Unique identifier for the preview
        /// </summary>
        public string PreviewId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Report title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Report description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Report type
        /// </summary>
        public string ReportType { get; set; }

        /// <summary>
        /// Preview HTML content
        /// </summary>
        public string HtmlContent { get; set; }

        /// <summary>
        /// Preview CSS styles
        /// </summary>
        public string CssStyles { get; set; }

        /// <summary>
        /// Report parameters used for generation
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Formatting options applied
        /// </summary>
        public ReportFormatting Formatting { get; set; } = new ReportFormatting();

        /// <summary>
        /// Data source information
        /// </summary>
        public DataSourceInfo DataSource { get; set; } = new DataSourceInfo();

        /// <summary>
        /// Preview metadata
        /// </summary>
        public PreviewMetadata Metadata { get; set; } = new PreviewMetadata();

        /// <summary>
        /// Whether the preview is ready for viewing
        /// </summary>
        public bool IsReady { get; set; }

        /// <summary>
        /// Error message if preview generation failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Date and time when preview was generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// User who generated the preview
        /// </summary>
        public string GeneratedBy { get; set; }

        /// <summary>
        /// Preview expiry date
        /// </summary>
        public DateTime ExpiresAt { get; set; } = DateTime.Now.AddHours(24);

        /// <summary>
        /// Whether the preview has expired
        /// </summary>
        public bool IsExpired => DateTime.Now > ExpiresAt;

        /// <summary>
        /// File size of the preview content in bytes
        /// </summary>
        public long ContentSize { get; set; }

        /// <summary>
        /// Number of pages in the report
        /// </summary>
        public int PageCount { get; set; }

        /// <summary>
        /// Number of records included in the report
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// Available export formats for this report
        /// </summary>
        public List<string> AvailableFormats { get; set; } = new List<string> { "PDF", "Excel", "CSV", "HTML" };

        /// <summary>
        /// Preview thumbnail (base64 encoded image)
        /// </summary>
        public string Thumbnail { get; set; }

        /// <summary>
        /// Tags associated with the report
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// Whether the report contains sensitive data
        /// </summary>
        public bool ContainsSensitiveData { get; set; }

        /// <summary>
        /// Security classification of the report
        /// </summary>
        public SecurityClassification SecurityLevel { get; set; } = SecurityClassification.Internal;
    }

    /// <summary>
    /// Data source information for the report
    /// </summary>
    public class DataSourceInfo
    {
        /// <summary>
        /// Name of the data source
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Type of data source (Database, API, File, etc.)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Date range of the data
        /// </summary>
        public DateRange DateRange { get; set; }

        /// <summary>
        /// Filters applied to the data
        /// </summary>
        public Dictionary<string, object> Filters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Number of records in the data source
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Number of records after filtering
        /// </summary>
        public int FilteredRecords { get; set; }

        /// <summary>
        /// Last update time of the data source
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Preview metadata
    /// </summary>
    public class PreviewMetadata
    {
        /// <summary>
        /// Time taken to generate the preview in milliseconds
        /// </summary>
        public long GenerationTimeMs { get; set; }

        /// <summary>
        /// Memory usage during preview generation in MB
        /// </summary>
        public double MemoryUsageMB { get; set; }

        /// <summary>
        /// Version of the report template used
        /// </summary>
        public string TemplateVersion { get; set; }

        /// <summary>
        /// Application version that generated the preview
        /// </summary>
        public string ApplicationVersion { get; set; }

        /// <summary>
        /// Warnings generated during preview creation
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Performance metrics
        /// </summary>
        public Dictionary<string, object> PerformanceMetrics { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Date range for data filtering
    /// </summary>
    public class DateRange
    {
        /// <summary>
        /// Start date of the range
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date of the range
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Whether the range includes the start date
        /// </summary>
        public bool IncludeStartDate { get; set; } = true;

        /// <summary>
        /// Whether the range includes the end date
        /// </summary>
        public bool IncludeEndDate { get; set; } = true;

        /// <summary>
        /// Time zone for the date range
        /// </summary>
        public string TimeZone { get; set; } = "UTC";
    }

    /// <summary>
    /// Security classification levels
    /// </summary>
    public enum SecurityClassification
    {
        Public,
        Internal,
        Confidential,
        Restricted
    }
}
