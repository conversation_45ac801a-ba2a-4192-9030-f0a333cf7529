using System;
using System.Collections.Generic;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Parameters for generating sales reports
    /// </summary>
    public class SalesReportParameters
    {
        /// <summary>
        /// Start date for the report
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// End date for the report
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Specific location IDs to include (null for all locations)
        /// </summary>
        public List<int>? LocationIds { get; set; }

        /// <summary>
        /// Specific user IDs to include (null for all users)
        /// </summary>
        public List<int>? UserIds { get; set; }

        /// <summary>
        /// Specific product IDs to include (null for all products)
        /// </summary>
        public List<int>? ProductIds { get; set; }

        /// <summary>
        /// Specific category IDs to include (null for all categories)
        /// </summary>
        public List<int>? CategoryIds { get; set; }

        /// <summary>
        /// Payment methods to include (null for all payment methods)
        /// </summary>
        public List<string>? PaymentMethods { get; set; }

        /// <summary>
        /// Minimum transaction amount to include
        /// </summary>
        public decimal? MinAmount { get; set; }

        /// <summary>
        /// Maximum transaction amount to include
        /// </summary>
        public decimal? MaxAmount { get; set; }

        /// <summary>
        /// Whether to include voided transactions
        /// </summary>
        public bool IncludeVoidedTransactions { get; set; } = false;

        /// <summary>
        /// Whether to include refunded transactions
        /// </summary>
        public bool IncludeRefundedTransactions { get; set; } = true;

        /// <summary>
        /// Group results by this field (Day, Week, Month, Year, Product, Category, User, etc.)
        /// </summary>
        public string? GroupBy { get; set; }

        /// <summary>
        /// Sort results by this field
        /// </summary>
        public string? SortBy { get; set; } = "Date";

        /// <summary>
        /// Sort direction (Ascending or Descending)
        /// </summary>
        public string SortDirection { get; set; } = "Descending";

        /// <summary>
        /// Maximum number of records to return (0 for no limit)
        /// </summary>
        public int MaxRecords { get; set; } = 0;

        /// <summary>
        /// Number of top customers to include in the report
        /// </summary>
        public int TopCustomersCount { get; set; } = 10;

        /// <summary>
        /// Number of top products to include in the report
        /// </summary>
        public int TopProductsCount { get; set; } = 10;

        /// <summary>
        /// Whether to include detailed transaction data
        /// </summary>
        public bool IncludeDetailedData { get; set; } = true;

        /// <summary>
        /// Whether to include summary statistics
        /// </summary>
        public bool IncludeSummaryStatistics { get; set; } = true;

        /// <summary>
        /// Whether to include charts and graphs data
        /// </summary>
        public bool IncludeChartsData { get; set; } = false;

        /// <summary>
        /// Custom filters as key-value pairs
        /// </summary>
        public Dictionary<string, object>? CustomFilters { get; set; }

        /// <summary>
        /// Report format for export
        /// </summary>
        public SalesReportFormat Format { get; set; } = SalesReportFormat.Standard;

        /// <summary>
        /// Time zone for date calculations
        /// </summary>
        public string? TimeZone { get; set; }

        /// <summary>
        /// Currency code for the report
        /// </summary>
        public string? CurrencyCode { get; set; } = "USD";
    }

    /// <summary>
    /// Sales report format options
    /// </summary>
    public enum SalesReportFormat
    {
        /// <summary>
        /// Standard detailed report
        /// </summary>
        Standard = 0,

        /// <summary>
        /// Summary report only
        /// </summary>
        Summary = 1,

        /// <summary>
        /// Detailed transaction list
        /// </summary>
        Detailed = 2,

        /// <summary>
        /// Analytics focused report
        /// </summary>
        Analytics = 3,

        /// <summary>
        /// Executive summary
        /// </summary>
        Executive = 4
    }
}
