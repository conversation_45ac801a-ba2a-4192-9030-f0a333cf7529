using InventoryManagement.Infrastructure.Configuration;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Caching
{
    /// <summary>
    /// Memory-based cache service implementation for offline-only operation
    /// Replaces multiple overlapping caching services
    /// </summary>
    public class OfflineMemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<OfflineMemoryCacheService> _logger;
        private readonly ConcurrentDictionary<string, CacheEntryMetadata> _metadata;
        private long _hits;
        private long _misses;

        public OfflineMemoryCacheService(IMemoryCache memoryCache, ILogger<OfflineMemoryCacheService> logger)
        {
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _metadata = new ConcurrentDictionary<string, CacheEntryMetadata>();
        }

        /// <summary>
        /// Configure cache options
        /// </summary>
        public void Configure(MemoryCacheOptions options)
        {
            // Options are applied at creation time, this is just for post-init configuration
            _logger.LogInformation("Cache options configured");
        }

        /// <summary>
        /// Register a cache region with a default expiration time
        /// </summary>
        public void RegisterRegion(string region, TimeSpan defaultExpiration)
        {
            if (string.IsNullOrEmpty(region)) throw new ArgumentNullException(nameof(region));
            
            _logger.LogInformation("Registered cache region: {Region} with expiration: {Expiration}", 
                region, defaultExpiration);
        }

        /// <summary>
        /// Add a rule to invalidate a cache region when a specific event occurs
        /// </summary>
        public void AddInvalidationRule(string region, string eventName)
        {
            if (string.IsNullOrEmpty(region)) throw new ArgumentNullException(nameof(region));
            if (string.IsNullOrEmpty(eventName)) throw new ArgumentNullException(nameof(eventName));
            
            _logger.LogInformation("Added invalidation rule for region: {Region} on event: {Event}", 
                region, eventName);
        }

        /// <summary>
        /// Gets an item from the cache
        /// </summary>
        public T Get<T>(string key, string region = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            
            var cacheKey = GetRegionKey(key, region);
            
            if (_memoryCache.TryGetValue(cacheKey, out T value))
            {
                _hits++;
                _logger.LogTrace("Cache hit for key: {Key} in region: {Region}", key, region);
                return value;
            }
            
            _misses++;
            _logger.LogTrace("Cache miss for key: {Key} in region: {Region}", key, region);
            return default;
        }

        /// <summary>
        /// Gets an item from the cache or adds it if it doesn't exist
        /// </summary>
        public T GetOrAdd<T>(string key, Func<T> factory, TimeSpan? absoluteExpiration = null, TimeSpan? slidingExpiration = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            if (factory == null) throw new ArgumentNullException(nameof(factory));
            
            if (_memoryCache.TryGetValue(key, out T value))
            {
                _hits++;
                _logger.LogTrace("Cache hit for key: {Key}", key);
                return value;
            }
            
            _misses++;
            _logger.LogTrace("Cache miss for key: {Key}, creating new item", key);
            
            value = factory();
            Set(key, value, absoluteExpiration, slidingExpiration);
            
            return value;
        }

        /// <summary>
        /// Gets an item from the cache or adds it if it doesn't exist (async version)
        /// </summary>
        public async Task<T> GetOrAddAsync<T>(string key, Func<Task<T>> factory, TimeSpan? absoluteExpiration = null, TimeSpan? slidingExpiration = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            if (factory == null) throw new ArgumentNullException(nameof(factory));
            
            if (_memoryCache.TryGetValue(key, out T value))
            {
                _hits++;
                _logger.LogTrace("Cache hit for key: {Key}", key);
                return value;
            }
            
            _misses++;
            _logger.LogTrace("Cache miss for key: {Key}, creating new item asynchronously", key);
            
            value = await factory();
            Set(key, value, absoluteExpiration, slidingExpiration);
            
            return value;
        }

        /// <summary>
        /// Sets an item in the cache
        /// </summary>
        public void Set<T>(string key, T value, string region = null, TimeSpan? expiration = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            
            var cacheKey = GetRegionKey(key, region);
            var options = new MemoryCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.AbsoluteExpirationRelativeToNow = expiration.Value;
            }
            else
            {
                // Default expiration of 10 minutes
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10);
            }
            
            _memoryCache.Set(cacheKey, value, options);
            _logger.LogTrace("Cache set for key: {Key} in region: {Region}", key, region);
            
            // Track metadata
            var metadata = new CacheEntryMetadata
            {
                Key = cacheKey,
                Region = region,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow + (expiration ?? TimeSpan.FromMinutes(10)),
                Size = EstimateSize(value)
            };
            
            _metadata[cacheKey] = metadata;
            
            _logger.LogTrace("Cache item set: {Key}, Region: {Region}, Size: {Size} bytes", 
                key, region, metadata.Size);
        }

        /// <summary>
        /// Removes an item from the cache
        /// </summary>
        public void Remove(string key, string region = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            
            var cacheKey = GetRegionKey(key, region);
            _memoryCache.Remove(cacheKey);
            _metadata.TryRemove(cacheKey, out _);
            _logger.LogTrace("Cache item removed: {Key} from region: {Region}", key, region);
        }
        
        /// <summary>
        /// Invalidates all items in a cache region
        /// </summary>
        public void InvalidateRegion(string region)
        {
            if (string.IsNullOrEmpty(region)) throw new ArgumentNullException(nameof(region));
            
            // Find all keys in this region
            var keysToRemove = _metadata.Values
                .Where(m => m.Region == region)
                .Select(m => m.Key)
                .ToList();
                
            foreach (var key in keysToRemove)
            {
                _memoryCache.Remove(key);
                _metadata.TryRemove(key, out _);
            }
            
            _logger.LogInformation("Invalidated {Count} items in region: {Region}", keysToRemove.Count, region);
        }
        
        /// <summary>
        /// Tries to get a value from the cache
        /// </summary>
        public bool TryGetValue<T>(string key, out T value, string region = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            
            var cacheKey = GetRegionKey(key, region);
            var result = _memoryCache.TryGetValue(cacheKey, out T cachedValue);
            
            if (result)
            {
                value = cachedValue;
                _hits++;
                _logger.LogTrace("Cache hit for key: {Key} in region: {Region}", key, region);
            }
            else
            {
                value = default;
                _misses++;
                _logger.LogTrace("Cache miss for key: {Key} in region: {Region}", key, region);
            }
            
            return result;
        }

        /// <summary>
        /// Checks if an item exists in the cache
        /// </summary>
        public bool Contains(string key)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            
            return _metadata.ContainsKey(key);
        }

        /// <summary>
        /// Clears all items from the cache
        /// </summary>
        public void Clear()
        {
            foreach (var key in _metadata.Keys.ToList())
            {
                _memoryCache.Remove(key);
            }
            
            _metadata.Clear();
            
            _logger.LogInformation("Cache cleared");
        }

        /// <summary>
        /// Gets all cache keys
        /// </summary>
        public IEnumerable<string> GetAllKeys()
        {
            return _metadata.Keys.ToList();
        }

        /// <summary>
        /// Gets cache statistics
        /// </summary>
        public CacheStatistics GetStatistics()
        {
            return new CacheStatistics
            {
                ItemCount = _metadata.Count,
                TotalSize = _metadata.Values.Sum(m => m.Size),
                Hits = _hits,
                Misses = _misses
            };
        }

        /// <summary>
        /// Estimates the size of an object in bytes
        /// </summary>
        private long EstimateSize(object value)
        {
            if (value == null) return 0;
            
            Type type = value.GetType();
            
            // Primitive types
            if (type == typeof(bool)) return 1;
            if (type == typeof(byte)) return 1;
            if (type == typeof(char)) return 2;
            if (type == typeof(short) || type == typeof(ushort)) return 2;
            if (type == typeof(int) || type == typeof(uint) || type == typeof(float)) return 4;
            if (type == typeof(long) || type == typeof(ulong) || type == typeof(double)) return 8;
            if (type == typeof(decimal)) return 16;
            if (type == typeof(Guid)) return 16;
            if (type == typeof(DateTime)) return 8;
            if (type == typeof(TimeSpan)) return 8;
            
            // String
            if (type == typeof(string))
            {
                return 24 + (value as string).Length * 2; // 2 bytes per character in UTF-16
            }
            
            // Collections
            if (value is ICollection<object> collection)
            {
                return 24 + collection.Count * 8; // Rough estimate
            }
            
            // Default estimate for complex types
            return 256; // Arbitrary size for complex objects
        }

        /// <summary>
        /// Gets a cache key that includes the region prefix if a region is specified
        /// </summary>
        private string GetRegionKey(string key, string region)
        {
            if (string.IsNullOrEmpty(region))
                return key;
                
            return $"{region}:{key}";
        }

        /// <summary>
        /// Metadata for a cache entry
        /// </summary>
        private class CacheEntryMetadata
        {
            public string Key { get; set; }
            public string Region { get; set; }
            public DateTime CreatedAt { get; set; }
            public DateTime ExpiresAt { get; set; }
            public long Size { get; set; }
        }
    }
}
