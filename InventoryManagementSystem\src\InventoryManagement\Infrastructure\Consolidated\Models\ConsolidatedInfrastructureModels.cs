using System;
using System.Collections.Generic;

namespace InventoryManagement.Infrastructure.Consolidated.Models
{
    /// <summary>
    /// Models for consolidated infrastructure services
    /// </summary>

    #region Error Handling Models

    public class ErrorHandlingResult
    {
        public ErrorAction Action { get; set; }
        public string Message { get; set; }
        public bool ShouldRetry { get; set; }
        public Exception Exception { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    public enum ErrorAction
    {
        Log,
        Display,
        Retry,
        Escalate,
        Ignore
    }

    public enum ErrorSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    #endregion

    #region Performance Monitoring Models

    public class PerformanceResult<T>
    {
        public T Result { get; set; }
        public PerformanceMetrics Metrics { get; set; }
    }

    public class PerformanceMetrics
    {
        public string OperationName { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }

    public class PerformanceStatistics
    {
        public string OperationName { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public TimeSpan MinExecutionTime { get; set; }
        public TimeSpan MaxExecutionTime { get; set; }
        public int TotalExecutions { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public double SuccessRate { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
    }

    #endregion

    #region Health Check Models

    public class HealthCheckResult
    {
        public HealthStatus Status { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, ComponentHealthResult> ComponentResults { get; set; } = new Dictionary<string, ComponentHealthResult>();
    }

    public class ComponentHealthResult
    {
        public string ComponentName { get; set; }
        public HealthStatus Status { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }

    public enum HealthStatus
    {
        Healthy,
        Degraded,
        Unhealthy,
        Unknown
    }

    #endregion

    #region Offline Models

    public class OfflineModeStatus
    {
        public bool IsOfflineMode { get; set; }
        public bool IsNetworkAvailable { get; set; }
        public int PendingOperationsCount { get; set; }
        public DateTime? LastSyncTime { get; set; }
        public long LocalStorageSize { get; set; }
        public List<string> OfflineCapabilities { get; set; } = new List<string>();
    }

    public class SyncResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public DateTime SyncTime { get; set; }
        public int ItemsSynced { get; set; }
        public int ItemsFailed { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    public class OfflineOperation
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string OperationType { get; set; }
        public string Data { get; set; }
        public DateTime QueuedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public OfflineOperationStatus Status { get; set; }
        public string ErrorMessage { get; set; }
        public int RetryCount { get; set; }
    }

    public enum OfflineOperationStatus
    {
        Queued,
        Processing,
        Completed,
        Failed,
        Cancelled
    }

    public class NetworkStatus
    {
        public bool IsConnected { get; set; }
        public string ConnectionType { get; set; }
        public DateTime LastChecked { get; set; }
        public string ErrorMessage { get; set; }
        public TimeSpan ResponseTime { get; set; }
    }

    public class NetworkStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string ConnectionType { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    #endregion

    #region Configuration Models

    public class AppConfiguration
    {
        public string ApplicationName { get; set; }
        public string Version { get; set; }
        public string Environment { get; set; }
        public bool IsOfflineMode { get; set; }
        public string DataDirectory { get; set; }
        public string LogLevel { get; set; }
        public bool EnableAuditLogging { get; set; }
        public int SessionTimeoutMinutes { get; set; }
    }

    public class DatabaseSettings
    {
        public string ConnectionString { get; set; }
        public string Provider { get; set; }
        public int CommandTimeout { get; set; }
        public bool EnableRetryOnFailure { get; set; }
        public int MaxRetryCount { get; set; }
        public TimeSpan MaxRetryDelay { get; set; }
        public bool EnableSensitiveDataLogging { get; set; }
        public bool EnableDetailedErrors { get; set; }
    }

    public class SecuritySettings
    {
        public int PasswordMinLength { get; set; }
        public bool PasswordRequireUppercase { get; set; }
        public bool PasswordRequireLowercase { get; set; }
        public bool PasswordRequireDigit { get; set; }
        public bool PasswordRequireSpecialChar { get; set; }
        public int MaxLoginAttempts { get; set; }
        public int LockoutDurationMinutes { get; set; }
        public int SessionTimeoutMinutes { get; set; }
        public bool EnableDataEncryption { get; set; }
        public string EncryptionKey { get; set; }
    }

    public class UISettings
    {
        public string Theme { get; set; }
        public string Language { get; set; }
        public string DateFormat { get; set; }
        public string TimeFormat { get; set; }
        public string CurrencySymbol { get; set; }
        public int DecimalPlaces { get; set; }
        public int PageSize { get; set; }
        public bool EnableAnimations { get; set; }
        public bool ShowTooltips { get; set; }
        public int AutoSaveInterval { get; set; }
    }

    #endregion

    #region Scheduling Models

    public class ScheduledTask
    {
        public string Name { get; set; }
        public TaskSchedule Schedule { get; set; }
        public Func<Task> Handler { get; set; }
        public bool IsEnabled { get; set; } = true;
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
    }

    public class TaskSchedule
    {
        public int IntervalMinutes { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<DayOfWeek> DaysOfWeek { get; set; } = new List<DayOfWeek>();
    }

    #endregion

    #region UI Models

    public class ShortcutDefinition
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string KeyCombination { get; set; }
        public string Description { get; set; }
        public bool IsEnabled { get; set; } = true;
    }

    #endregion
}
