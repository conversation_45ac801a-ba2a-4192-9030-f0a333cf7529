﻿#pragma checksum "..\..\..\..\Views\MainDashboard.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0996B5655B2FEFE2986E6F32497FEB632B181845"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InventoryManagement.Controls;
using InventoryManagement.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InventoryManagement.Views {
    
    
    /// <summary>
    /// MainDashboard
    /// </summary>
    public partial class MainDashboard : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 107 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardNavButton;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryNavButton;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesNavButton;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ProductsNavButton;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CategoriesNavButton;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransfersNavButton;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsNavButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DefectiveItemsNavButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryReconciliationNavButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExchangeNavButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UsersNavButton;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsNavButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LogoutButton;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NotificationButton;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationBadge;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotificationCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl MainContentContainer;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InventoryContent;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InventorySearchBox;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LocationFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddItemButton;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InventoryDataGrid;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SalesContent;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker SalesStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker SalesEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyDateRangeButton;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TodayButton;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WeekButton;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MonthButton;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 402 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TransactionCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TransactionChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgTransactionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgTransactionChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 484 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ProductsContent;
        
        #line default
        #line hidden
        
        
        #line 499 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProductSearchBox;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProductCategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProductSupplierFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 537 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ProductSearchButton;
        
        #line default
        #line hidden
        
        
        #line 549 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportProductsButton;
        
        #line default
        #line hidden
        
        
        #line 558 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddProductButton;
        
        #line default
        #line hidden
        
        
        #line 573 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton GridViewRadioButton;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ListViewRadioButton;
        
        #line default
        #line hidden
        
        
        #line 578 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ProductGridView;
        
        #line default
        #line hidden
        
        
        #line 629 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ProductListView;
        
        #line default
        #line hidden
        
        
        #line 679 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CategoriesContent;
        
        #line default
        #line hidden
        
        
        #line 694 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CategorySearchBox;
        
        #line default
        #line hidden
        
        
        #line 706 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CategorySearchButton;
        
        #line default
        #line hidden
        
        
        #line 718 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCategoryButton;
        
        #line default
        #line hidden
        
        
        #line 737 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CategoriesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 798 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryNameDetailText;
        
        #line default
        #line hidden
        
        
        #line 804 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryDescriptionDetailText;
        
        #line default
        #line hidden
        
        
        #line 810 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryProductCountDetailText;
        
        #line default
        #line hidden
        
        
        #line 816 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox CategoryProductsList;
        
        #line default
        #line hidden
        
        
        #line 830 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid TransfersContent;
        
        #line default
        #line hidden
        
        
        #line 845 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TransferTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 862 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker TransferStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 868 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker TransferEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 872 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyTransferFilterButton;
        
        #line default
        #line hidden
        
        
        #line 884 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewTransferButton;
        
        #line default
        #line hidden
        
        
        #line 915 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTransfersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 917 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TransfersChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 927 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BasementToMainTextBlock;
        
        #line default
        #line hidden
        
        
        #line 936 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MainToBasementTextBlock;
        
        #line default
        #line hidden
        
        
        #line 945 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExternalTransfersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 957 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid TransfersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 999 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ReportsContent;
        
        #line default
        #line hidden
        
        
        #line 1020 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ReportDateRangePicker;
        
        #line default
        #line hidden
        
        
        #line 1031 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ReportCategoriesListBox;
        
        #line default
        #line hidden
        
        
        #line 1079 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ReportLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 1087 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ReportViewPanel;
        
        #line default
        #line hidden
        
        
        #line 1096 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1099 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportDateRangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1101 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportGeneratedTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1107 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContent;
        
        #line default
        #line hidden
        
        
        #line 1114 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NoReportSelectedPanel;
        
        #line default
        #line hidden
        
        
        #line 1126 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DefectiveItemsContent;
        
        #line default
        #line hidden
        
        
        #line 1141 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefectiveItemSearchBox;
        
        #line default
        #line hidden
        
        
        #line 1153 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DefectiveItemStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 1170 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DefectiveItemDatePicker;
        
        #line default
        #line hidden
        
        
        #line 1174 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DefectiveItemSearchButton;
        
        #line default
        #line hidden
        
        
        #line 1186 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDefectiveItemButton;
        
        #line default
        #line hidden
        
        
        #line 1217 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDefectiveItemsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1226 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingReviewTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1235 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReturnedToSupplierTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1244 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FinancialImpactTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1251 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DefectiveItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 1325 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ExchangeContent;
        
        #line default
        #line hidden
        
        
        #line 1330 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InventoryReconciliationContent;
        
        #line default
        #line hidden
        
        
        #line 1335 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ItemExchangeContent;
        
        #line default
        #line hidden
        
        
        #line 1349 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExchangeSearchBox;
        
        #line default
        #line hidden
        
        
        #line 1363 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExchangeStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 1380 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ExchangeStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 1386 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ExchangeEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 1390 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExchangeSearchButton;
        
        #line default
        #line hidden
        
        
        #line 1402 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewExchangeButton;
        
        #line default
        #line hidden
        
        
        #line 1433 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExchangesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1442 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingExchangesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1451 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RefundedAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1460 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MostExchangedItemTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1467 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ExchangeDataGrid;
        
        #line default
        #line hidden
        
        
        #line 1541 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid UsersContent;
        
        #line default
        #line hidden
        
        
        #line 1556 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UserSearchBox;
        
        #line default
        #line hidden
        
        
        #line 1570 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UserRoleFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 1587 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UserStatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 1603 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UserSearchButton;
        
        #line default
        #line hidden
        
        
        #line 1615 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddUserButton;
        
        #line default
        #line hidden
        
        
        #line 1634 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid UsersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 1722 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectUserMessage;
        
        #line default
        #line hidden
        
        
        #line 1726 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid UserDetailsGrid;
        
        #line default
        #line hidden
        
        
        #line 1742 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsernameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1748 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FullNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1754 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1760 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RoleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1766 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 1767 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1774 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastLoginTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1780 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountCreatedTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1785 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditUserButton;
        
        #line default
        #line hidden
        
        
        #line 1794 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 1803 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeactivateUserButton;
        
        #line default
        #line hidden
        
        
        #line 1820 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SettingsContent;
        
        #line default
        #line hidden
        
        
        #line 1832 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SettingsCategoriesListBox;
        
        #line default
        #line hidden
        
        
        #line 1849 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GeneralSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2023 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AppearanceSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2024 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NotificationsSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2025 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DatabaseSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2026 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid BackupSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2027 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SecuritySettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2028 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SystemSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2029 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AboutSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 2035 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationSidebar;
        
        #line default
        #line hidden
        
        
        #line 2042 "..\..\..\..\Views\MainDashboard.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal InventoryManagement.Controls.NotificationControl NotificationControl;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InventoryManagement;component/views/maindashboard.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\MainDashboard.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DashboardNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\Views\MainDashboard.xaml"
            this.DashboardNavButton.Click += new System.Windows.RoutedEventHandler(this.DashboardNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.InventoryNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\Views\MainDashboard.xaml"
            this.InventoryNavButton.Click += new System.Windows.RoutedEventHandler(this.InventoryNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SalesNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\..\Views\MainDashboard.xaml"
            this.SalesNavButton.Click += new System.Windows.RoutedEventHandler(this.SalesNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ProductsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\Views\MainDashboard.xaml"
            this.ProductsNavButton.Click += new System.Windows.RoutedEventHandler(this.ProductsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CategoriesNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\..\Views\MainDashboard.xaml"
            this.CategoriesNavButton.Click += new System.Windows.RoutedEventHandler(this.CategoriesNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TransfersNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\Views\MainDashboard.xaml"
            this.TransfersNavButton.Click += new System.Windows.RoutedEventHandler(this.TransfersNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ReportsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\..\Views\MainDashboard.xaml"
            this.ReportsNavButton.Click += new System.Windows.RoutedEventHandler(this.ReportsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DefectiveItemsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\Views\MainDashboard.xaml"
            this.DefectiveItemsNavButton.Click += new System.Windows.RoutedEventHandler(this.DefectiveItemsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.InventoryReconciliationNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\..\Views\MainDashboard.xaml"
            this.InventoryReconciliationNavButton.Click += new System.Windows.RoutedEventHandler(this.InventoryReconciliationNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ExchangeNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\Views\MainDashboard.xaml"
            this.ExchangeNavButton.Click += new System.Windows.RoutedEventHandler(this.ExchangeNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.UsersNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\Views\MainDashboard.xaml"
            this.UsersNavButton.Click += new System.Windows.RoutedEventHandler(this.UsersNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SettingsNavButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\MainDashboard.xaml"
            this.SettingsNavButton.Click += new System.Windows.RoutedEventHandler(this.SettingsNavButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LogoutButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\Views\MainDashboard.xaml"
            this.LogoutButton.Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PageTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.NotificationButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\Views\MainDashboard.xaml"
            this.NotificationButton.Click += new System.Windows.RoutedEventHandler(this.NotificationButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.NotificationBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 17:
            this.NotificationCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.UserNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 20:
            this.MainContentContainer = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 21:
            this.InventoryContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 22:
            this.InventorySearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.LocationFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 24:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 25:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            return;
            case 26:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            return;
            case 27:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            return;
            case 28:
            this.AddItemButton = ((System.Windows.Controls.Button)(target));
            return;
            case 29:
            this.InventoryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 30:
            this.SalesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 31:
            this.SalesStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 32:
            this.SalesEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 33:
            this.ApplyDateRangeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 34:
            this.TodayButton = ((System.Windows.Controls.Button)(target));
            return;
            case 35:
            this.WeekButton = ((System.Windows.Controls.Button)(target));
            return;
            case 36:
            this.MonthButton = ((System.Windows.Controls.Button)(target));
            return;
            case 37:
            this.TotalSalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.SalesChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.TransactionCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.TransactionChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.AvgTransactionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.AvgTransactionChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.SalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 44:
            this.ProductsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 45:
            this.ProductSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.ProductCategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 47:
            this.ProductSupplierFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 48:
            this.ProductSearchButton = ((System.Windows.Controls.Button)(target));
            return;
            case 49:
            this.ExportProductsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 50:
            this.AddProductButton = ((System.Windows.Controls.Button)(target));
            return;
            case 51:
            this.GridViewRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 52:
            this.ListViewRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 53:
            this.ProductGridView = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 54:
            this.ProductListView = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 55:
            this.CategoriesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 56:
            this.CategorySearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 57:
            this.CategorySearchButton = ((System.Windows.Controls.Button)(target));
            return;
            case 58:
            this.AddCategoryButton = ((System.Windows.Controls.Button)(target));
            return;
            case 59:
            this.CategoriesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 60:
            this.CategoryNameDetailText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 61:
            this.CategoryDescriptionDetailText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 62:
            this.CategoryProductCountDetailText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 63:
            this.CategoryProductsList = ((System.Windows.Controls.ListBox)(target));
            return;
            case 64:
            this.TransfersContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 65:
            this.TransferTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 66:
            this.TransferStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 67:
            this.TransferEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 68:
            this.ApplyTransferFilterButton = ((System.Windows.Controls.Button)(target));
            return;
            case 69:
            this.NewTransferButton = ((System.Windows.Controls.Button)(target));
            return;
            case 70:
            this.TotalTransfersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 71:
            this.TransfersChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 72:
            this.BasementToMainTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 73:
            this.MainToBasementTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 74:
            this.ExternalTransfersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 75:
            this.TransfersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 76:
            this.ReportsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 77:
            this.ReportDateRangePicker = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 78:
            this.ReportCategoriesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 80:
            this.ReportLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 81:
            this.ReportViewPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 82:
            this.ReportTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 83:
            this.ReportDateRangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 84:
            this.ReportGeneratedTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 85:
            this.ReportContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 86:
            this.NoReportSelectedPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 87:
            this.DefectiveItemsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 88:
            this.DefectiveItemSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 89:
            this.DefectiveItemStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 90:
            this.DefectiveItemDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 91:
            this.DefectiveItemSearchButton = ((System.Windows.Controls.Button)(target));
            return;
            case 92:
            this.AddDefectiveItemButton = ((System.Windows.Controls.Button)(target));
            return;
            case 93:
            this.TotalDefectiveItemsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 94:
            this.PendingReviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 95:
            this.ReturnedToSupplierTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 96:
            this.FinancialImpactTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 97:
            this.DefectiveItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 98:
            this.ExchangeContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 99:
            this.InventoryReconciliationContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 100:
            this.ItemExchangeContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 101:
            this.ExchangeSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 102:
            this.ExchangeStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 103:
            this.ExchangeStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 104:
            this.ExchangeEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 105:
            this.ExchangeSearchButton = ((System.Windows.Controls.Button)(target));
            return;
            case 106:
            this.NewExchangeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 107:
            this.TotalExchangesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 108:
            this.PendingExchangesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 109:
            this.RefundedAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 110:
            this.MostExchangedItemTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 111:
            this.ExchangeDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 112:
            this.UsersContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 113:
            this.UserSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 114:
            this.UserRoleFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 115:
            this.UserStatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 116:
            this.UserSearchButton = ((System.Windows.Controls.Button)(target));
            return;
            case 117:
            this.AddUserButton = ((System.Windows.Controls.Button)(target));
            return;
            case 118:
            this.UsersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 119:
            this.SelectUserMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 120:
            this.UserDetailsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 121:
            this.UsernameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 122:
            this.FullNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 123:
            this.EmailTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 124:
            this.RoleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 125:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 126:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 127:
            this.LastLoginTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 128:
            this.AccountCreatedTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 129:
            this.EditUserButton = ((System.Windows.Controls.Button)(target));
            return;
            case 130:
            this.ResetPasswordButton = ((System.Windows.Controls.Button)(target));
            return;
            case 131:
            this.DeactivateUserButton = ((System.Windows.Controls.Button)(target));
            return;
            case 132:
            this.SettingsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 133:
            this.SettingsCategoriesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 134:
            this.GeneralSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 135:
            this.AppearanceSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 136:
            this.NotificationsSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 137:
            this.DatabaseSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 138:
            this.BackupSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 139:
            this.SecuritySettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 140:
            this.SystemSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 141:
            this.AboutSettingsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 142:
            this.NotificationSidebar = ((System.Windows.Controls.Border)(target));
            return;
            case 143:
            this.NotificationControl = ((InventoryManagement.Controls.NotificationControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 79:
            
            #line 1062 "..\..\..\..\Views\MainDashboard.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateReport_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

