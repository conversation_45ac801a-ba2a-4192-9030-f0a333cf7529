using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple dashboard service for offline inventory management
    /// </summary>
    public class SimpleDashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SimpleDashboardService> _logger;

        public SimpleDashboardService(ApplicationDbContext dbContext, ILogger<SimpleDashboardService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Models.DashboardSummary> GetDashboardSummaryAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Getting dashboard summary for user {UserId}", userId);

                var totalProducts = await _dbContext.Products.CountAsync();
                var lowStockCount = await _dbContext.Products.CountAsync(p => p.StockQuantity <= p.ReorderLevel);
                var totalSales = await _dbContext.Sales.SumAsync(s => (decimal?)s.TotalAmount) ?? 0;
                var todaySales = await _dbContext.Sales
                    .Where(s => s.SaleDate.Date == DateTime.Today)
                    .SumAsync(s => (decimal?)s.TotalAmount) ?? 0;

                return new Models.DashboardSummary
                {
                    TotalProducts = totalProducts,
                    LowStockItems = lowStockCount,
                    TotalSales = totalSales,
                    TodaySales = todaySales,
                    TotalCustomers = await _dbContext.Customers.CountAsync(),
                    PendingOrders = 0, // Simple implementation
                    LastUpdated = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary");
                return new Models.DashboardSummary();
            }
        }

        public async Task<Models.SalesMetrics> GetSalesMetricsAsync(TimeSpan timeRange)
        {
            try
            {
                var startDate = DateTime.Now.Subtract(timeRange);
                var sales = await _dbContext.Sales
                    .Where(s => s.SaleDate >= startDate)
                    .ToListAsync();

                return new Models.SalesMetrics
                {
                    TotalRevenue = sales.Sum(s => s.TotalAmount),
                    TotalTransactions = sales.Count,
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0,
                    Period = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales metrics");
                return new Models.SalesMetrics();
            }
        }

        public async Task<Models.InventoryMetrics> GetInventoryMetricsAsync(TimeSpan timeRange)
        {
            try
            {
                var products = await _dbContext.Products.ToListAsync();

                return new Models.InventoryMetrics
                {
                    TotalProducts = products.Count,
                    TotalValue = products.Sum(p => p.Price * p.StockQuantity),
                    LowStockItems = products.Count(p => p.StockQuantity <= p.ReorderLevel),
                    OutOfStockItems = products.Count(p => p.StockQuantity == 0),
                    Period = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory metrics");
                return new Models.InventoryMetrics();
            }
        }

        public async Task<List<Models.TopSellingItem>> GetTopSellingItemsAsync(DateTime startDate, DateTime endDate, int count = 10)
        {
            try
            {
                var topItems = await _dbContext.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => new { si.ProductId, si.Product.Name })
                    .Select(g => new Models.TopSellingItem
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.Name,
                        QuantitySold = g.Sum(si => si.Quantity),
                        Revenue = g.Sum(si => si.Quantity * si.UnitPrice)
                    })
                    .OrderByDescending(item => item.QuantitySold)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top selling items");
                return new List<Models.TopSellingItem>();
            }
        }

        public async Task<List<Models.LowStockItem>> GetLowStockItemsAsync()
        {
            try
            {
                var lowStockItems = await _dbContext.Products
                    .Where(p => p.StockQuantity <= p.ReorderLevel)
                    .Select(p => new Models.LowStockItem
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        CurrentStock = p.StockQuantity,
                        ReorderLevel = p.ReorderLevel,
                        SuggestedOrderQuantity = Math.Max(p.ReorderLevel * 2 - p.StockQuantity, 0)
                    })
                    .ToListAsync();

                return lowStockItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                return new List<Models.LowStockItem>();
            }
        }

        public async Task<Models.InventoryStatusSummary> GetInventoryStatusSummaryAsync()
        {
            try
            {
                var products = await _dbContext.Products.ToListAsync();

                return new Models.InventoryStatusSummary
                {
                    TotalItems = products.Count,
                    InStockItems = products.Count(p => p.StockQuantity > p.ReorderLevel),
                    LowStockItems = products.Count(p => p.StockQuantity <= p.ReorderLevel && p.StockQuantity > 0),
                    OutOfStockItems = products.Count(p => p.StockQuantity == 0),
                    TotalValue = products.Sum(p => p.Price * p.StockQuantity)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory status summary");
                return new Models.InventoryStatusSummary();
            }
        }

        // Simple implementations for missing interface methods
        public async Task<List<Models.TopSellingItem>> GetTopSellingItemsAsync(TimeSpan timeRange, int count = 10)
        {
            var endDate = DateTime.Now;
            var startDate = endDate.Subtract(timeRange);
            return await GetTopSellingItemsAsync(startDate, endDate, count);
        }

        public async Task<List<Models.TopSellingItem>> GetTopRevenueItemsAsync(TimeSpan timeRange, int count = 10)
        {
            try
            {
                var endDate = DateTime.Now;
                var startDate = endDate.Subtract(timeRange);

                var topItems = await _dbContext.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => new { si.ProductId, si.Product.Name })
                    .Select(g => new Models.TopSellingItem
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.Name,
                        QuantitySold = g.Sum(si => si.Quantity),
                        Revenue = g.Sum(si => si.Quantity * si.UnitPrice)
                    })
                    .OrderByDescending(item => item.Revenue)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top revenue items");
                return new List<Models.TopSellingItem>();
            }
        }

        public async Task<List<InventoryAlert>> GetInventoryAlertsAsync(int limit = 50)
        {
            // Simple implementation
            await Task.CompletedTask;
            return new List<InventoryAlert>();
        }

        public async Task<List<RecentActivity>> GetRecentActivityAsync(int userId, int limit = 20)
        {
            // Simple implementation
            await Task.CompletedTask;
            return new List<RecentActivity>();
        }

        public async Task SaveDashboardPreferencesAsync(int userId, DashboardPreferences preferences)
        {
            // Simple implementation
            _logger.LogInformation("Saving dashboard preferences for user {UserId}", userId);
            await Task.CompletedTask;
        }

        public async Task<DashboardPreferences> GetDashboardPreferencesAsync(int userId)
        {
            // Simple implementation - return default preferences
            await Task.CompletedTask;
            return new DashboardPreferences
            {
                UserId = userId,
                ShowSalesChart = true,
                ShowInventoryChart = true,
                ShowRecentActivity = true,
                RefreshInterval = 30
            };
        }
    }
}
