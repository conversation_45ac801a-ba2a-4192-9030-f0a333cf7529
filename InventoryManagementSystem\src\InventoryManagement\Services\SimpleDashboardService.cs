using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple dashboard service for offline inventory management
    /// </summary>
    public class SimpleDashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SimpleDashboardService> _logger;

        public SimpleDashboardService(ApplicationDbContext dbContext, ILogger<SimpleDashboardService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<DashboardSummary> GetDashboardSummaryAsync(int userId)
        {
            try
            {
                _logger.LogInformation("Getting dashboard summary for user {UserId}", userId);

                var totalProducts = await _dbContext.Products.CountAsync();
                var lowStockCount = await _dbContext.Products.CountAsync(p => p.StockQuantity <= p.ReorderLevel);
                var totalSales = await _dbContext.Sales.SumAsync(s => (decimal?)s.TotalAmount) ?? 0;
                var todaySales = await _dbContext.Sales
                    .Where(s => s.SaleDate.Date == DateTime.Today)
                    .SumAsync(s => (decimal?)s.TotalAmount) ?? 0;

                return new DashboardSummary
                {
                    TotalInventoryValue = totalSales,
                    TotalItemsCount = totalProducts,
                    LowStockItemsCount = lowStockCount,
                    OutOfStockItemsCount = 0, // Simple implementation
                    TodaySales = todaySales,
                    TodayTransactionsCount = await _dbContext.Sales.CountAsync(s => s.SaleDate.Date == DateTime.Today),
                    PendingOrdersCount = 0, // Simple implementation
                    UnreadNotificationsCount = 0 // Simple implementation
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary");
                return new DashboardSummary();
            }
        }

        public async Task<SalesMetrics> GetSalesMetricsAsync(TimeSpan timeRange)
        {
            try
            {
                var startDate = DateTime.Now.Subtract(timeRange);
                var sales = await _dbContext.Sales
                    .Where(s => s.SaleDate >= startDate)
                    .ToListAsync();

                return new SalesMetrics
                {
                    TotalRevenue = sales.Sum(s => s.TotalAmount),
                    TotalTransactions = sales.Count,
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0,
                    TimePeriod = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales metrics");
                return new SalesMetrics();
            }
        }

        public async Task<InventoryMetrics> GetInventoryMetricsAsync(TimeSpan timeRange)
        {
            try
            {
                var products = await _dbContext.Products.ToListAsync();

                return new InventoryMetrics
                {
                    TotalItemsCount = products.Count,
                    TotalInventoryValue = products.Sum(p => p.Price * p.StockQuantity),
                    LowStockItemsCount = products.Count(p => p.StockQuantity <= p.ReorderLevel),
                    OutOfStockItemsCount = products.Count(p => p.StockQuantity == 0),
                    TimePeriod = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory metrics");
                return new InventoryMetrics();
            }
        }

        public async Task<List<Models.TopSellingItem>> GetTopSellingItemsAsync(DateTime startDate, DateTime endDate, int count = 10)
        {
            try
            {
                var topItems = await _dbContext.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => new { si.ProductId, si.Product.Name })
                    .Select(g => new Models.TopSellingItem
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.Name,
                        QuantitySold = g.Sum(si => si.Quantity),
                        Revenue = g.Sum(si => si.Quantity * si.UnitPrice)
                    })
                    .OrderByDescending(item => item.QuantitySold)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top selling items");
                return new List<Models.TopSellingItem>();
            }
        }

        public async Task<List<Models.LowStockItem>> GetLowStockItemsAsync()
        {
            try
            {
                var lowStockItems = await _dbContext.Products
                    .Where(p => p.StockQuantity <= p.ReorderLevel)
                    .Select(p => new Models.LowStockItem
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        CurrentStock = p.StockQuantity,
                        ReorderLevel = p.ReorderLevel,
                        SuggestedOrderQuantity = Math.Max(p.ReorderLevel * 2 - p.StockQuantity, 0)
                    })
                    .ToListAsync();

                return lowStockItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                return new List<Models.LowStockItem>();
            }
        }

        public async Task<Models.InventoryStatusSummary> GetInventoryStatusSummaryAsync()
        {
            try
            {
                var products = await _dbContext.Products.ToListAsync();

                return new Models.InventoryStatusSummary
                {
                    TotalItems = products.Count,
                    InStockItems = products.Count(p => p.StockQuantity > p.ReorderLevel),
                    LowStockItems = products.Count(p => p.StockQuantity <= p.ReorderLevel && p.StockQuantity > 0),
                    OutOfStockItems = products.Count(p => p.StockQuantity == 0),
                    TotalValue = products.Sum(p => p.Price * p.StockQuantity)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory status summary");
                return new Models.InventoryStatusSummary();
            }
        }

        // Simple implementations for missing interface methods
        public async Task<List<Models.TopSellingItem>> GetTopSellingItemsAsync(TimeSpan timeRange, int count = 10)
        {
            var endDate = DateTime.Now;
            var startDate = endDate.Subtract(timeRange);
            return await GetTopSellingItemsAsync(startDate, endDate, count);
        }



        public async Task<List<InventoryAlert>> GetInventoryAlertsAsync(int limit = 50)
        {
            // Simple implementation
            await Task.CompletedTask;
            return new List<InventoryAlert>();
        }

        public async Task<List<RecentActivity>> GetRecentActivityAsync(int userId, int limit = 20)
        {
            // Simple implementation
            await Task.CompletedTask;
            return new List<RecentActivity>();
        }



        public async Task<DashboardPreferences> GetDashboardPreferencesAsync(int userId)
        {
            // Simple implementation - return default preferences
            await Task.CompletedTask;
            return new DashboardPreferences
            {
                UserId = userId,
                ShowSalesChart = true,
                ShowInventoryChart = true,
                ShowRecentActivity = true,
                RefreshInterval = 30
            };
        }

        // Additional missing interface methods
        public async Task<FinancialMetrics> GetFinancialMetricsAsync(TimeSpan timeRange)
        {
            try
            {
                var sales = await _dbContext.Sales
                    .Where(s => s.SaleDate >= DateTime.Now.Subtract(timeRange))
                    .ToListAsync();

                var totalRevenue = sales.Sum(s => s.TotalAmount);
                var totalCogs = sales.Sum(s => s.TotalAmount * 0.6m); // Simple 60% COGS estimate

                return new FinancialMetrics
                {
                    TotalRevenue = totalRevenue,
                    TotalCogs = totalCogs,
                    GrossProfit = totalRevenue - totalCogs,
                    GrossProfitMargin = totalRevenue > 0 ? ((totalRevenue - totalCogs) / totalRevenue) * 100 : 0,
                    TimePeriod = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting financial metrics");
                return new FinancialMetrics { TimePeriod = timeRange };
            }
        }

        public async Task<SupplierMetrics> GetSupplierMetricsAsync(TimeSpan timeRange)
        {
            try
            {
                var suppliers = await _dbContext.Suppliers.ToListAsync();

                return new SupplierMetrics
                {
                    TotalSuppliersCount = suppliers.Count,
                    ActiveSuppliersCount = suppliers.Count(s => s.IsActive),
                    TimePeriod = timeRange
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supplier metrics");
                return new SupplierMetrics { TimePeriod = timeRange };
            }
        }

        public async Task<List<TopRevenueItem>> GetTopRevenueItemsAsync(TimeSpan timeRange, int count = 10)
        {
            try
            {
                var endDate = DateTime.Now;
                var startDate = endDate.Subtract(timeRange);

                var topItems = await _dbContext.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                    .GroupBy(si => new { si.ProductId, si.Product.Name, si.Product.SKU })
                    .Select(g => new TopRevenueItem
                    {
                        ItemId = g.Key.ProductId,
                        ItemName = g.Key.Name,
                        Sku = g.Key.SKU,
                        QuantitySold = g.Sum(si => si.Quantity),
                        SalesAmount = g.Sum(si => si.Quantity * si.UnitPrice)
                    })
                    .OrderByDescending(item => item.SalesAmount)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top revenue items");
                return new List<TopRevenueItem>();
            }
        }

        public async Task<bool> SaveDashboardPreferencesAsync(int userId, DashboardPreferences preferences)
        {
            // Simple implementation
            _logger.LogInformation("Saving dashboard preferences for user {UserId}", userId);
            await Task.CompletedTask;
            return true;
        }
    }
}
