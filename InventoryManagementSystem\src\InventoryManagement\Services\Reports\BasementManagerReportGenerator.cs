using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
// Note: Data services are in InventoryManagement.Services namespace
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Report generator for basement manager roles
    /// </summary>
    public class BasementManagerReportGenerator
    {
        private readonly IItemService _itemService;
        private readonly IInventoryService _inventoryService;
        private readonly ITransactionService _transactionService;
        private readonly ILogger _logger;
        
        public BasementManagerReportGenerator(
            IItemService itemService,
            IInventoryService inventoryService,
            ITransactionService transactionService,
            ILogger logger)
        {
            _itemService = itemService;
            _inventoryService = inventoryService;
            _transactionService = transactionService;
            _logger = logger;
        }
        
        /// <summary>
        /// Generates a report of low stock items for a specific basement location
        /// </summary>
        public async Task<BaseReport> GetLowStockReportAsync(int locationId)
        {
            try
            {
                _logger.LogInformation($"Generating low stock report for location {locationId}");
                
                // Get items with low stock
                var items = await _inventoryService.GetLowStockItemsAsync(locationId);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Low Stock Items Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = DateTime.Today.AddDays(-7),
                    EndDate = DateTime.Today,
                    Notes = $"Items below their minimum stock level at location {locationId}"
                };
                
                // Add data points for each low stock item
                foreach (var item in items)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = item.Category,
                        Value = item.CurrentStock,
                        Metadata = new Dictionary<string, object>
                        {
                            { "ItemId", item.Id },
                            { "ItemName", item.Name },
                            { "MinimumStock", item.MinimumStockLevel },
                            { "ReorderPoint", item.ReorderPoint },
                            { "UnitPrice", item.UnitPrice }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating low stock report: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Generates a report of inventory transfers between locations
        /// </summary>
        public async Task<BaseReport> GetBasementTransferReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating inventory transfer report from {startDate} to {endDate}");
                
                // Get all transfers in the date range
                var transfers = await _transactionService.GetInventoryTransfersAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Inventory Transfers Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Summary of inventory transfers between locations"
                };
                
                // Add data points for each transfer
                foreach (var transfer in transfers)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "Transfer",
                        Value = transfer.Quantity,
                        Date = transfer.TransactionDate,
                        Metadata = new Dictionary<string, object>
                        {
                            { "TransferId", transfer.Id },
                            { "ItemId", transfer.ItemId },
                            { "ItemName", transfer.ItemName },
                            { "SourceLocationId", transfer.SourceLocationId },
                            { "SourceLocationName", transfer.SourceLocationName },
                            { "DestinationLocationId", transfer.DestinationLocationId },
                            { "DestinationLocationName", transfer.DestinationLocationName },
                            { "InitiatedBy", transfer.InitiatedByUserId },
                            { "Status", transfer.Status }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating transfer report: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Generates a report of aging inventory items
        /// </summary>
        public async Task<BaseReport> GetInventoryAgingReportAsync(int locationId)
        {
            try
            {
                _logger.LogInformation($"Generating inventory aging report for location {locationId}");
                
                // Get items that have been in inventory for a long time
                var agingItems = await _inventoryService.GetAgingInventoryAsync(locationId);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Inventory Aging Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = DateTime.Today.AddMonths(-6),
                    EndDate = DateTime.Today,
                    Notes = $"Items that have been in stock for more than 90 days at location {locationId}"
                };
                
                // Add data points for each aging item
                foreach (var item in agingItems)
                {
                    int daysInStock = (DateTime.Today - item.LastReceivedDate).Days;
                    
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = item.Category,
                        Value = item.CurrentStock,
                        Date = item.LastReceivedDate,
                        Metadata = new Dictionary<string, object>
                        {
                            { "ItemId", item.Id },
                            { "ItemName", item.Name },
                            { "DaysInStock", daysInStock },
                            { "TotalValue", item.CurrentStock * item.UnitPrice }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating aging inventory report: {ex.Message}");
                throw;
            }
        }
    }
}
