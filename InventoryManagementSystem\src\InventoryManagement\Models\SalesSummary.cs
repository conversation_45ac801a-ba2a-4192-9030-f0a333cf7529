using System;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a summary of sales data for a specific period
    /// </summary>
    public class SalesSummary
    {
        /// <summary>
        /// Total sales amount for the period
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// Total number of transactions for the period
        /// </summary>
        public int TotalTransactions { get; set; }

        /// <summary>
        /// Average transaction value for the period
        /// </summary>
        public decimal AverageTransactionValue { get; set; }

        /// <summary>
        /// Start date of the summary period
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date of the summary period
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Total number of items sold
        /// </summary>
        public int TotalItemsSold { get; set; }

        /// <summary>
        /// Total number of unique customers
        /// </summary>
        public int UniqueCustomers { get; set; }

        /// <summary>
        /// Highest single transaction amount
        /// </summary>
        public decimal HighestTransaction { get; set; }

        /// <summary>
        /// Lowest single transaction amount
        /// </summary>
        public decimal LowestTransaction { get; set; }

        /// <summary>
        /// Total cash sales
        /// </summary>
        public decimal CashSales { get; set; }

        /// <summary>
        /// Total card sales
        /// </summary>
        public decimal CardSales { get; set; }

        /// <summary>
        /// Total other payment method sales
        /// </summary>
        public decimal OtherSales { get; set; }

        /// <summary>
        /// Number of voided transactions
        /// </summary>
        public int VoidedTransactions { get; set; }

        /// <summary>
        /// Total amount of voided transactions
        /// </summary>
        public decimal VoidedAmount { get; set; }

        /// <summary>
        /// Growth percentage compared to previous period
        /// </summary>
        public decimal? GrowthPercentage { get; set; }
    }

    /// <summary>
    /// Represents sales performance metrics
    /// </summary>
    public class SalesPerformanceMetrics
    {
        /// <summary>
        /// Total number of sales
        /// </summary>
        public int TotalSales { get; set; }

        /// <summary>
        /// Total sales amount
        /// </summary>
        public decimal TotalSalesAmount { get; set; }

        /// <summary>
        /// Average sale amount
        /// </summary>
        public decimal AverageSaleAmount { get; set; }

        /// <summary>
        /// Total number of items sold
        /// </summary>
        public int TotalItemsSold { get; set; }

        /// <summary>
        /// From date for the metrics
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// To date for the metrics
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Sales per day average
        /// </summary>
        public decimal SalesPerDay { get; set; }

        /// <summary>
        /// Best performing day
        /// </summary>
        public DateTime? BestDay { get; set; }

        /// <summary>
        /// Best day sales amount
        /// </summary>
        public decimal BestDayAmount { get; set; }

        /// <summary>
        /// Worst performing day
        /// </summary>
        public DateTime? WorstDay { get; set; }

        /// <summary>
        /// Worst day sales amount
        /// </summary>
        public decimal WorstDayAmount { get; set; }
    }
}
