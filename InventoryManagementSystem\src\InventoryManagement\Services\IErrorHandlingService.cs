using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for error handling services
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// Handles an exception and logs it appropriately
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">Additional context information</param>
        /// <param name="severity">Severity level of the error</param>
        /// <returns>Task representing the async operation</returns>
        Task HandleExceptionAsync(Exception exception, string context = null, ErrorSeverity severity = ErrorSeverity.Error);

        /// <summary>
        /// Handles an exception and returns a user-friendly error message
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">Additional context information</param>
        /// <param name="severity">Severity level of the error</param>
        /// <returns>User-friendly error message</returns>
        Task<string> HandleExceptionWithMessageAsync(Exception exception, string context = null, ErrorSeverity severity = ErrorSeverity.Error);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="context">Additional context information</param>
        /// <param name="severity">Severity level of the error</param>
        /// <returns>Task representing the async operation</returns>
        Task LogErrorAsync(string message, string context = null, ErrorSeverity severity = ErrorSeverity.Error);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Warning message</param>
        /// <param name="context">Additional context information</param>
        /// <returns>Task representing the async operation</returns>
        Task LogWarningAsync(string message, string context = null);

        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">Information message</param>
        /// <param name="context">Additional context information</param>
        /// <returns>Task representing the async operation</returns>
        Task LogInformationAsync(string message, string context = null);

        /// <summary>
        /// Gets recent error logs
        /// </summary>
        /// <param name="count">Number of recent errors to retrieve</param>
        /// <param name="severity">Minimum severity level</param>
        /// <returns>List of recent error logs</returns>
        Task<List<Models.ErrorLog>> GetRecentErrorsAsync(int count = 50, ErrorSeverity severity = ErrorSeverity.Warning);

        /// <summary>
        /// Gets error statistics for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Error statistics</returns>
        Task<ErrorStatistics> GetErrorStatisticsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Clears old error logs
        /// </summary>
        /// <param name="olderThanDays">Remove logs older than this many days</param>
        /// <returns>Number of logs removed</returns>
        Task<int> ClearOldLogsAsync(int olderThanDays = 30);

        /// <summary>
        /// Sends error notifications to administrators
        /// </summary>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="context">Additional context information</param>
        /// <param name="severity">Severity level of the error</param>
        /// <returns>Task representing the async operation</returns>
        Task NotifyAdministratorsAsync(Exception exception, string context = null, ErrorSeverity severity = ErrorSeverity.Error);

        /// <summary>
        /// Gets a user-friendly error message for an exception
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <returns>User-friendly error message</returns>
        string GetUserFriendlyMessage(Exception exception);

        /// <summary>
        /// Determines if an error should be reported to administrators
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <param name="severity">Severity level</param>
        /// <returns>True if the error should be reported</returns>
        bool ShouldReportError(Exception exception, ErrorSeverity severity);

        /// <summary>
        /// Creates an error report for debugging
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <param name="context">Additional context information</param>
        /// <returns>Detailed error report</returns>
        Task<ErrorReport> CreateErrorReportAsync(Exception exception, string context = null);

        /// <summary>
        /// Exports error logs to a file
        /// </summary>
        /// <param name="startDate">Start date for export</param>
        /// <param name="endDate">End date for export</param>
        /// <param name="filePath">Path to save the export file</param>
        /// <returns>Task representing the async operation</returns>
        Task ExportErrorLogsAsync(DateTime startDate, DateTime endDate, string filePath);

        /// <summary>
        /// Event fired when a critical error occurs
        /// </summary>
        event EventHandler<ErrorEventArgs> CriticalErrorOccurred;

        /// <summary>
        /// Event fired when an error is logged
        /// </summary>
        event EventHandler<ErrorEventArgs> ErrorLogged;
    }

    /// <summary>
    /// Error severity levels
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// Informational message
        /// </summary>
        Information = 0,

        /// <summary>
        /// Warning message
        /// </summary>
        Warning = 1,

        /// <summary>
        /// Error message
        /// </summary>
        Error = 2,

        /// <summary>
        /// Critical error
        /// </summary>
        Critical = 3,

        /// <summary>
        /// Fatal error
        /// </summary>
        Fatal = 4
    }

    /// <summary>
    /// Error statistics for a date range
    /// </summary>
    public class ErrorStatistics
    {
        /// <summary>
        /// Start date of the statistics period
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date of the statistics period
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Total number of errors
        /// </summary>
        public int TotalErrors { get; set; }

        /// <summary>
        /// Number of critical errors
        /// </summary>
        public int CriticalErrors { get; set; }

        /// <summary>
        /// Number of warnings
        /// </summary>
        public int Warnings { get; set; }

        /// <summary>
        /// Most common error types
        /// </summary>
        public Dictionary<string, int> ErrorTypeFrequency { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Error trend over time
        /// </summary>
        public List<ErrorTrendPoint> ErrorTrend { get; set; } = new List<ErrorTrendPoint>();
    }

    /// <summary>
    /// Error trend data point
    /// </summary>
    public class ErrorTrendPoint
    {
        /// <summary>
        /// Date of the data point
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Number of errors on this date
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// Number of warnings on this date
        /// </summary>
        public int WarningCount { get; set; }
    }

    /// <summary>
    /// Detailed error report
    /// </summary>
    public class ErrorReport
    {
        /// <summary>
        /// Unique identifier for the error report
        /// </summary>
        public string ReportId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Timestamp when the error occurred
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Exception details
        /// </summary>
        public string ExceptionDetails { get; set; }

        /// <summary>
        /// Stack trace
        /// </summary>
        public string StackTrace { get; set; }

        /// <summary>
        /// Context information
        /// </summary>
        public string Context { get; set; }

        /// <summary>
        /// User information
        /// </summary>
        public string UserInfo { get; set; }

        /// <summary>
        /// System information
        /// </summary>
        public string SystemInfo { get; set; }

        /// <summary>
        /// Application version
        /// </summary>
        public string ApplicationVersion { get; set; }

        /// <summary>
        /// Error severity
        /// </summary>
        public ErrorSeverity Severity { get; set; }

        /// <summary>
        /// Additional metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Event arguments for error events
    /// </summary>
    public class ErrorEventArgs : EventArgs
    {
        /// <summary>
        /// The exception that occurred
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// Error context
        /// </summary>
        public string Context { get; set; }

        /// <summary>
        /// Error severity
        /// </summary>
        public ErrorSeverity Severity { get; set; }

        /// <summary>
        /// Timestamp when the error occurred
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Error report ID
        /// </summary>
        public string ReportId { get; set; }
    }
}
