using InventoryManagement.Infrastructure.Caching;
using InventoryManagement.Infrastructure.ErrorHandling;
using InventoryManagement.Infrastructure.Logging;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Consolidated.CrossCutting
{
    /// <summary>
    /// Consolidated cross-cutting service that merges functionality from multiple cross-cutting services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - Logging services
    /// - Caching services
    /// - Error handling services
    /// - Performance monitoring
    /// - Health checking
    /// </summary>
    public class ConsolidatedCrossCuttingService
    {
        private readonly ICacheService _cacheService;
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly IPerformanceMonitor _performanceMonitor;
        private readonly IHealthCheckService _healthCheckService;
        private readonly ILogger<ConsolidatedCrossCuttingService> _logger;

        public ConsolidatedCrossCuttingService(
            ICacheService cacheService,
            IErrorHandlingService errorHandlingService,
            IPerformanceMonitor performanceMonitor,
            IHealthCheckService healthCheckService,
            ILogger<ConsolidatedCrossCuttingService> logger)
        {
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _errorHandlingService = errorHandlingService ?? throw new ArgumentNullException(nameof(errorHandlingService));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
            _healthCheckService = healthCheckService ?? throw new ArgumentNullException(nameof(healthCheckService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Logging Operations

        /// <summary>
        /// Logs an information message with structured data
        /// </summary>
        /// <param name="message">Log message</param>
        /// <param name="properties">Additional properties</param>
        public void LogInformation(string message, Dictionary<string, object> properties = null)
        {
            try
            {
                if (properties != null && properties.Count > 0)
                {
                    using var scope = _logger.BeginScope(properties);
                    _logger.LogInformation(message);
                }
                else
                {
                    _logger.LogInformation(message);
                }
            }
            catch (Exception ex)
            {
                // Fallback logging to prevent logging failures from breaking the application
                Console.WriteLine($"Logging failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a warning message with structured data
        /// </summary>
        /// <param name="message">Log message</param>
        /// <param name="properties">Additional properties</param>
        public void LogWarning(string message, Dictionary<string, object> properties = null)
        {
            try
            {
                if (properties != null && properties.Count > 0)
                {
                    using var scope = _logger.BeginScope(properties);
                    _logger.LogWarning(message);
                }
                else
                {
                    _logger.LogWarning(message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Logging failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs an error with exception details
        /// </summary>
        /// <param name="exception">Exception to log</param>
        /// <param name="message">Additional message</param>
        /// <param name="properties">Additional properties</param>
        public void LogError(Exception exception, string message = null, Dictionary<string, object> properties = null)
        {
            try
            {
                if (properties != null && properties.Count > 0)
                {
                    using var scope = _logger.BeginScope(properties);
                    _logger.LogError(exception, message ?? "An error occurred");
                }
                else
                {
                    _logger.LogError(exception, message ?? "An error occurred");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error logging failed: {ex.Message}");
            }
        }

        #endregion

        #region Caching Operations

        /// <summary>
        /// Gets or sets a cached value with automatic expiration
        /// </summary>
        /// <typeparam name="T">Type of cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="factory">Factory function to create value if not cached</param>
        /// <param name="expiration">Cache expiration time</param>
        /// <returns>Cached or newly created value</returns>
        public async Task<T> GetOrSetCacheAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
        {
            try
            {
                // Try to get from cache first
                var cachedValue = await _cacheService.GetAsync<T>(key);
                if (cachedValue != null)
                {
                    LogInformation($"Cache hit for key: {key}");
                    return cachedValue;
                }

                // Create new value
                var newValue = await factory();
                
                // Cache the new value
                var cacheExpiration = expiration ?? TimeSpan.FromMinutes(30);
                await _cacheService.SetAsync(key, newValue, cacheExpiration);
                
                LogInformation($"Cache miss for key: {key}, value cached with expiration: {cacheExpiration}");
                return newValue;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error in cache operation for key: {key}");
                
                // If caching fails, still try to get the value
                try
                {
                    return await factory();
                }
                catch (Exception factoryEx)
                {
                    LogError(factoryEx, $"Factory function failed for key: {key}");
                    throw;
                }
            }
        }

        /// <summary>
        /// Invalidates cache entries by pattern
        /// </summary>
        /// <param name="pattern">Pattern to match cache keys</param>
        /// <returns>Task representing the async operation</returns>
        public async Task InvalidateCacheByPatternAsync(string pattern)
        {
            try
            {
                await _cacheService.RemoveByPatternAsync(pattern);
                LogInformation($"Cache invalidated for pattern: {pattern}");
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error invalidating cache for pattern: {pattern}");
                throw;
            }
        }

        /// <summary>
        /// Clears all cache entries
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task ClearAllCacheAsync()
        {
            try
            {
                await _cacheService.ClearAsync();
                LogInformation("All cache entries cleared");
            }
            catch (Exception ex)
            {
                LogError(ex, "Error clearing all cache entries");
                throw;
            }
        }

        #endregion

        #region Error Handling

        /// <summary>
        /// Handles an exception with appropriate logging and user notification
        /// </summary>
        /// <param name="exception">Exception to handle</param>
        /// <param name="context">Context information</param>
        /// <param name="showToUser">Whether to show error to user</param>
        /// <returns>Error handling result</returns>
        public async Task<ErrorHandlingResult> HandleExceptionAsync(Exception exception, string context = null, bool showToUser = true)
        {
            try
            {
                LogError(exception, $"Exception in context: {context}");

                var result = await _errorHandlingService.HandleExceptionAsync(exception, context, showToUser);
                
                LogInformation($"Exception handled with result: {result.Action}");
                
                return result;
            }
            catch (Exception ex)
            {
                LogError(ex, "Error in exception handling");
                
                // Return a default error handling result
                return new ErrorHandlingResult
                {
                    Action = ErrorAction.Log,
                    Message = "An unexpected error occurred",
                    ShouldRetry = false
                };
            }
        }

        /// <summary>
        /// Executes an operation with automatic error handling
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="context">Context information</param>
        /// <param name="defaultValue">Default value to return on error</param>
        /// <returns>Operation result or default value</returns>
        public async Task<T> ExecuteWithErrorHandlingAsync<T>(Func<Task<T>> operation, string context = null, T defaultValue = default)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context, false);
                return defaultValue;
            }
        }

        #endregion

        #region Performance Monitoring

        /// <summary>
        /// Measures the execution time of an operation
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to measure</param>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Operation result and performance metrics</returns>
        public async Task<PerformanceResult<T>> MeasurePerformanceAsync<T>(Func<Task<T>> operation, string operationName)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                LogInformation($"Starting performance measurement for: {operationName}");
                
                var result = await operation();
                
                stopwatch.Stop();
                
                var performanceMetrics = new PerformanceMetrics
                {
                    OperationName = operationName,
                    ExecutionTime = stopwatch.Elapsed,
                    Success = true,
                    Timestamp = DateTime.Now
                };

                await _performanceMonitor.RecordMetricsAsync(performanceMetrics);
                
                LogInformation($"Performance measurement completed for: {operationName}, Duration: {stopwatch.Elapsed.TotalMilliseconds}ms");
                
                return new PerformanceResult<T>
                {
                    Result = result,
                    Metrics = performanceMetrics
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                var performanceMetrics = new PerformanceMetrics
                {
                    OperationName = operationName,
                    ExecutionTime = stopwatch.Elapsed,
                    Success = false,
                    ErrorMessage = ex.Message,
                    Timestamp = DateTime.Now
                };

                await _performanceMonitor.RecordMetricsAsync(performanceMetrics);
                
                LogError(ex, $"Performance measurement failed for: {operationName}, Duration: {stopwatch.Elapsed.TotalMilliseconds}ms");
                
                throw;
            }
        }

        /// <summary>
        /// Gets performance statistics for an operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="timeRange">Time range for statistics</param>
        /// <returns>Performance statistics</returns>
        public async Task<PerformanceStatistics> GetPerformanceStatisticsAsync(string operationName, TimeSpan timeRange)
        {
            try
            {
                var statistics = await _performanceMonitor.GetStatisticsAsync(operationName, timeRange);
                
                LogInformation($"Performance statistics retrieved for: {operationName}");
                
                return statistics;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error getting performance statistics for: {operationName}");
                throw;
            }
        }

        #endregion

        #region Health Checking

        /// <summary>
        /// Performs a comprehensive health check of the system
        /// </summary>
        /// <returns>Health check result</returns>
        public async Task<HealthCheckResult> PerformHealthCheckAsync()
        {
            try
            {
                LogInformation("Starting comprehensive health check");
                
                var healthResult = await _healthCheckService.CheckHealthAsync();
                
                LogInformation($"Health check completed. Status: {healthResult.Status}");
                
                return healthResult;
            }
            catch (Exception ex)
            {
                LogError(ex, "Error performing health check");
                
                return new HealthCheckResult
                {
                    Status = HealthStatus.Unhealthy,
                    Message = $"Health check failed: {ex.Message}",
                    Timestamp = DateTime.Now
                };
            }
        }

        /// <summary>
        /// Checks the health of a specific component
        /// </summary>
        /// <param name="componentName">Name of the component to check</param>
        /// <returns>Component health result</returns>
        public async Task<ComponentHealthResult> CheckComponentHealthAsync(string componentName)
        {
            try
            {
                LogInformation($"Checking health of component: {componentName}");
                
                var result = await _healthCheckService.CheckComponentHealthAsync(componentName);
                
                LogInformation($"Component health check completed for: {componentName}, Status: {result.Status}");
                
                return result;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error checking health of component: {componentName}");
                
                return new ComponentHealthResult
                {
                    ComponentName = componentName,
                    Status = HealthStatus.Unhealthy,
                    Message = $"Health check failed: {ex.Message}",
                    Timestamp = DateTime.Now
                };
            }
        }

        #endregion
    }
}
