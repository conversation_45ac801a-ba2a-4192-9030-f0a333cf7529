using InventoryManagement.DataAccess;
using InventoryManagement.Infrastructure.Auth;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple authentication service for offline inventory management
    /// </summary>
    public class SimpleAuthService : IAuthService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SimpleAuthService> _logger;
        private User _currentUser;

        public SimpleAuthService(ApplicationDbContext dbContext, ILogger<SimpleAuthService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public User CurrentUser => _currentUser;

        public bool IsAuthenticated => _currentUser != null;

        public event EventHandler<User> UserLoggedIn;
        public event EventHandler<User> UserLoggedOut;
        public event EventHandler<string> AuthenticationFailed;

        public async Task<AuthResult> AuthenticateAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("Attempting authentication for user: {Username}", username);

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    var result = new AuthResult
                    {
                        Success = false,
                        Message = "Username and password are required"
                    };
                    AuthenticationFailed?.Invoke(this, result.Message);
                    return result;
                }

                var user = await _dbContext.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user == null)
                {
                    var result = new AuthResult
                    {
                        Success = false,
                        Message = "Invalid username or password"
                    };
                    AuthenticationFailed?.Invoke(this, result.Message);
                    return result;
                }

                // Simple password verification (in production, use proper hashing)
                var hashedPassword = HashPassword(password);
                if (user.PasswordHash != hashedPassword)
                {
                    var result = new AuthResult
                    {
                        Success = false,
                        Message = "Invalid username or password"
                    };
                    AuthenticationFailed?.Invoke(this, result.Message);
                    return result;
                }

                _currentUser = user;
                _logger.LogInformation("User {Username} authenticated successfully", username);
                
                UserLoggedIn?.Invoke(this, user);

                return new AuthResult
                {
                    Success = true,
                    Message = "Authentication successful",
                    User = user
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for user: {Username}", username);
                var result = new AuthResult
                {
                    Success = false,
                    Message = "Authentication failed due to system error"
                };
                AuthenticationFailed?.Invoke(this, result.Message);
                return result;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                if (_currentUser != null)
                {
                    _logger.LogInformation("User {Username} logging out", _currentUser.Username);
                    var user = _currentUser;
                    _currentUser = null;
                    UserLoggedOut?.Invoke(this, user);
                }
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
            }
        }

        public bool HasPermission(string permission)
        {
            if (_currentUser == null) return false;

            // Simple role-based permissions
            return _currentUser.Role switch
            {
                UserRole.Admin => true, // Admin has all permissions
                UserRole.BasementManager => permission.StartsWith("Inventory") || permission.StartsWith("Transfer"),
                UserRole.Cashier => permission.StartsWith("Sales") || permission.StartsWith("POS"),
                _ => false
            };
        }

        public string[] GetUserPermissions()
        {
            if (_currentUser == null) return new string[0];

            return _currentUser.Role switch
            {
                UserRole.Admin => new[] { "All" },
                UserRole.BasementManager => new[] { "Inventory.View", "Inventory.Edit", "Transfer.Create", "Transfer.View" },
                UserRole.Cashier => new[] { "Sales.Create", "POS.Use", "Customer.View" },
                _ => new string[0]
            };
        }

        public async Task<bool> ChangePasswordAsync(string newPassword)
        {
            try
            {
                if (_currentUser == null) return false;

                _currentUser.PasswordHash = HashPassword(newPassword);
                _dbContext.Users.Update(_currentUser);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Password changed for user: {Username}", _currentUser.Username);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user: {Username}", _currentUser?.Username);
                return false;
            }
        }

        public bool IsPasswordExpired()
        {
            // Simple implementation - no password expiration for offline system
            return false;
        }

        public async Task<int> GetFailedLoginAttemptsAsync(string username)
        {
            // Simple implementation - no failed attempt tracking
            await Task.CompletedTask;
            return 0;
        }

        public async Task<bool> IsAccountLockedAsync(int userId)
        {
            // Simple implementation - no account locking
            await Task.CompletedTask;
            return false;
        }

        public async Task UnlockAccountAsync(int userId)
        {
            // Simple implementation - no account locking
            await Task.CompletedTask;
        }

        public async Task ClearFailedLoginAttemptsAsync(string username)
        {
            // Simple implementation - no failed attempt tracking
            await Task.CompletedTask;
        }

        private string HashPassword(string password)
        {
            // Simple hashing for demo purposes (use proper hashing in production)
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "salt"));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
