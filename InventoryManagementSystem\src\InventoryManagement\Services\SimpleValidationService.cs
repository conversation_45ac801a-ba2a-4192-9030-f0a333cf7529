using InventoryManagement.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Simple validation service implementation for offline inventory management
    /// </summary>
    public class SimpleValidationService : IValidationService
    {
        private readonly ILogger<SimpleValidationService> _logger;

        public SimpleValidationService(ILogger<SimpleValidationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ValidationResult> ValidateAsync<T>(T entity) where T : class
        {
            try
            {
                _logger.LogDebug("Validating entity of type: {EntityType}", typeof(T).Name);

                var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
                var validationContext = new ValidationContext(entity);

                bool isValid = Validator.TryValidateObject(entity, validationContext, validationResults, true);

                var result = new ValidationResult
                {
                    IsValid = isValid,
                    Errors = validationResults.Select(vr => new ValidationError
                    {
                        PropertyName = vr.MemberNames.FirstOrDefault() ?? string.Empty,
                        ErrorMessage = vr.ErrorMessage ?? string.Empty
                    }).ToList()
                };

                _logger.LogDebug("Validation completed for {EntityType}. IsValid: {IsValid}, Errors: {ErrorCount}", 
                    typeof(T).Name, result.IsValid, result.Errors.Count);

                await Task.CompletedTask;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating entity of type: {EntityType}", typeof(T).Name);
                return new ValidationResult
                {
                    IsValid = false,
                    Errors = new List<ValidationError>
                    {
                        new ValidationError
                        {
                            PropertyName = "General",
                            ErrorMessage = "Validation failed due to an internal error."
                        }
                    }
                };
            }
        }

        public async Task<ValidationResult> ValidatePropertyAsync<T>(T entity, string propertyName, object value) where T : class
        {
            try
            {
                _logger.LogDebug("Validating property {PropertyName} for entity type: {EntityType}", propertyName, typeof(T).Name);

                var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
                var validationContext = new ValidationContext(entity) { MemberName = propertyName };

                bool isValid = Validator.TryValidateProperty(value, validationContext, validationResults);

                var result = new ValidationResult
                {
                    IsValid = isValid,
                    Errors = validationResults.Select(vr => new ValidationError
                    {
                        PropertyName = propertyName,
                        ErrorMessage = vr.ErrorMessage ?? string.Empty
                    }).ToList()
                };

                await Task.CompletedTask;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating property {PropertyName} for entity type: {EntityType}", propertyName, typeof(T).Name);
                return new ValidationResult
                {
                    IsValid = false,
                    Errors = new List<ValidationError>
                    {
                        new ValidationError
                        {
                            PropertyName = propertyName,
                            ErrorMessage = "Property validation failed due to an internal error."
                        }
                    }
                };
            }
        }

        public async Task<bool> IsValidAsync<T>(T entity) where T : class
        {
            try
            {
                var result = await ValidateAsync(entity);
                return result.IsValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking validity of entity type: {EntityType}", typeof(T).Name);
                return false;
            }
        }

        public async Task<List<ValidationError>> GetValidationErrorsAsync<T>(T entity) where T : class
        {
            try
            {
                var result = await ValidateAsync(entity);
                return result.Errors;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting validation errors for entity type: {EntityType}", typeof(T).Name);
                return new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "General",
                        ErrorMessage = "Failed to retrieve validation errors due to an internal error."
                    }
                };
            }
        }

        public async Task<ValidationResult> ValidateBusinessRulesAsync<T>(T entity) where T : class
        {
            try
            {
                _logger.LogDebug("Validating business rules for entity type: {EntityType}", typeof(T).Name);

                // Simple implementation - just return valid
                // In a real implementation, you would check business-specific rules
                await Task.CompletedTask;
                return new ValidationResult
                {
                    IsValid = true,
                    Errors = new List<ValidationError>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating business rules for entity type: {EntityType}", typeof(T).Name);
                return new ValidationResult
                {
                    IsValid = false,
                    Errors = new List<ValidationError>
                    {
                        new ValidationError
                        {
                            PropertyName = "BusinessRules",
                            ErrorMessage = "Business rule validation failed due to an internal error."
                        }
                    }
                };
            }
        }

        public void RegisterCustomValidator<T>(Func<T, ValidationResult> validator) where T : class
        {
            try
            {
                _logger.LogInformation("Registering custom validator for entity type: {EntityType}", typeof(T).Name);
                // Simple implementation - no actual registration needed for this basic service
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering custom validator for entity type: {EntityType}", typeof(T).Name);
            }
        }
    }

    // Note: Using existing ValidationResult and ValidationError classes from Models namespace
}
