using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using InventoryManagement.Services.Analytics;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Implementation of the advanced reporting service for administrators
    /// </summary>
    public class AdvancedReportingService : IAdvancedReportingService
    {
        private readonly IItemService _itemService;
        private readonly IInventoryService _inventoryService;
        private readonly ITransactionService _transactionService;
        private readonly ISalesService _salesService;
        private readonly IUserService _userService;
        private readonly IAuditService _auditService;
        private readonly IAnalyticsService _analyticsService;
        private readonly ILogger _logger;
        
        public AdvancedReportingService(
            IItemService itemService,
            IInventoryService inventoryService,
            ITransactionService transactionService,
            ISalesService salesService,
            IUserService userService,
            IAuditService auditService,
            IAnalyticsService analyticsService,
            ILogger logger)
        {
            _itemService = itemService;
            _inventoryService = inventoryService;
            _transactionService = transactionService;
            _salesService = salesService;
            _userService = userService;
            _auditService = auditService;
            _analyticsService = analyticsService;
            _logger = logger;
        }

        /// <summary>
        /// Gets a comprehensive business performance report for administrators
        /// </summary>
        public async Task<AdminBusinessPerformanceReport> GetBusinessPerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating business performance report from {startDate} to {endDate}");
                
                // Get sales data for the period
                var sales = await _salesService.GetSalesForPeriodAsync(startDate, endDate);
                var totalRevenue = sales.Sum(s => s.TotalAmount);
                
                // Get expense data for the period
                var expenses = await _transactionService.GetExpensesForPeriodAsync(startDate, endDate);
                var totalExpenses = expenses.Sum(e => e.Amount);
                
                // Calculate profits
                var grossProfit = totalRevenue;
                var netProfit = totalRevenue - totalExpenses;
                var profitMargin = totalRevenue > 0 ? netProfit / totalRevenue : 0;
                
                // Get revenue by category
                var salesDetails = await _salesService.GetDetailedSalesForPeriodAsync(startDate, endDate);
                var revenueByCategory = salesDetails
                    .GroupBy(sd => sd.Category)
                    .ToDictionary(
                        g => g.Key, 
                        g => g.Sum(sd => sd.Quantity * sd.UnitPrice)
                    );
                
                // Get revenue by location
                var revenueByLocation = sales
                    .GroupBy(s => s.LocationId)
                    .ToDictionary(
                        g => g.Key.ToString(), 
                        g => g.Sum(s => s.TotalAmount)
                    );
                
                // Create the report
                var report = new AdminBusinessPerformanceReport
                {
                    Title = "Business Performance Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalRevenue = totalRevenue,
                    TotalExpenses = totalExpenses,
                    GrossProfit = grossProfit,
                    NetProfit = netProfit,
                    ProfitMargin = profitMargin,
                    RevenueByCategory = revenueByCategory,
                    RevenueByLocation = revenueByLocation,
                    Notes = "Comprehensive business performance analysis"
                };
                
                // Add trending data from analytics service
                var trends = await _analyticsService.GetSalesTrendsAsync(startDate, endDate);
                
                foreach (var trend in trends)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "Trend",
                        Value = trend.Value,
                        Date = trend.Date,
                        Metadata = new Dictionary<string, object>
                        {
                            { "TrendType", trend.TrendType },
                            { "Growth", trend.GrowthRate }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating business performance report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets an inventory valuation report for all locations
        /// </summary>
        public async Task<BaseReport> GetInventoryValuationReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating inventory valuation report as of {endDate}");
                
                // Get inventory valuation data
                var inventoryValuation = await _inventoryService.GetInventoryValuationAsync();
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Inventory Valuation Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Current value of inventory across all locations"
                };
                
                // Group by location
                var valuationByLocation = inventoryValuation
                    .GroupBy(iv => iv.LocationId)
                    .ToDictionary(
                        g => g.Key,
                        g => new 
                        { 
                            LocationName = g.First().LocationName,
                            TotalValue = g.Sum(iv => iv.TotalValue),
                            ItemCount = g.Count()
                        }
                    );
                
                // Add data points for each location
                foreach (var locationId in valuationByLocation.Keys)
                {
                    var locationData = valuationByLocation[locationId];
                    
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "Location",
                        Value = locationData.TotalValue,
                        Metadata = new Dictionary<string, object>
                        {
                            { "LocationId", locationId },
                            { "LocationName", locationData.LocationName },
                            { "ItemCount", locationData.ItemCount }
                        }
                    });
                }
                
                // Add detailed data points for high-value items
                var highValueItems = inventoryValuation
                    .OrderByDescending(iv => iv.TotalValue)
                    .Take(10);
                
                foreach (var item in highValueItems)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "HighValueItem",
                        Value = item.TotalValue,
                        Metadata = new Dictionary<string, object>
                        {
                            { "ItemId", item.ItemId },
                            { "ItemName", item.ItemName },
                            { "LocationId", item.LocationId },
                            { "LocationName", item.LocationName },
                            { "Quantity", item.Quantity },
                            { "UnitPrice", item.UnitPrice }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating inventory valuation report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets a profit margins report by product category and location
        /// </summary>
        public async Task<BaseReport> GetProfitMarginsReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating profit margins report from {startDate} to {endDate}");
                
                // Get sales and cost data for margin calculations
                var profitMargins = await _analyticsService.GetProfitMarginsByProductCategoryAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Profit Margins Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Profit margins by product category and location"
                };
                
                // Add data points for each category
                foreach (var margin in profitMargins)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = margin.Category,
                        Value = margin.ProfitMargin,
                        Metadata = new Dictionary<string, object>
                        {
                            { "CategoryId", margin.CategoryId },
                            { "CategoryName", margin.Category },
                            { "Revenue", margin.Revenue },
                            { "Cost", margin.Cost },
                            { "Profit", margin.Profit },
                            { "ProfitMarginPercent", margin.ProfitMargin * 100 }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating profit margins report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets an employee performance report for all employees
        /// </summary>
        public async Task<BaseReport> GetEmployeePerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating employee performance report from {startDate} to {endDate}");
                
                // Get performance metrics for all employees
                var performanceMetrics = await _analyticsService.GetEmployeePerformanceMetricsAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Employee Performance Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Performance metrics for all employees"
                };
                
                // Add data points for each employee
                foreach (var metric in performanceMetrics)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = "Employee",
                        Value = metric.PerformanceScore,
                        Metadata = new Dictionary<string, object>
                        {
                            { "EmployeeId", metric.EmployeeId },
                            { "EmployeeName", metric.EmployeeName },
                            { "Role", metric.Role },
                            { "SalesCount", metric.SalesCount },
                            { "TotalSalesAmount", metric.TotalSalesAmount },
                            { "AverageTransactionValue", metric.AverageTransactionValue },
                            { "ActivityCount", metric.ActivityCount }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating employee performance report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets an audit trail report for system activities
        /// </summary>
        public async Task<BaseReport> GetAuditTrailReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"Generating audit trail report from {startDate} to {endDate}");
                
                // Get audit log entries for the period
                var auditLogs = await _auditService.GetAuditLogsForPeriodAsync(startDate, endDate);
                
                // Create the report
                var report = new BaseReport
                {
                    Title = "Audit Trail Report",
                    GeneratedAt = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    Notes = "Log of system activities by user"
                };
                
                // Add data points for each audit log entry
                foreach (var log in auditLogs)
                {
                    report.DataPoints.Add(new ReportDataPoint
                    {
                        Category = log.ActionType,
                        Date = log.Timestamp,
                        Value = 1, // Each action counts as 1 for counting purposes
                        Metadata = new Dictionary<string, object>
                        {
                            { "UserId", log.UserId },
                            { "UserName", log.UserName },
                            { "ActionType", log.ActionType },
                            { "EntityType", log.EntityType },
                            { "EntityId", log.EntityId },
                            { "Description", log.Description },
                            { "IPAddress", log.IPAddress }
                        }
                    });
                }
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating audit trail report: {ex.Message}");
                throw;
            }
        }
    }
}
