using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a sales transaction in the system
    /// </summary>
    public class SalesTransaction
    {
        /// <summary>
        /// Unique identifier for the transaction
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Transaction number for reference
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TransactionNumber { get; set; }

        /// <summary>
        /// Date and time when the transaction occurred
        /// </summary>
        [Required]
        public DateTime TransactionDate { get; set; }

        /// <summary>
        /// ID of the user who processed the transaction
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// Name of the user who processed the transaction
        /// </summary>
        [StringLength(100)]
        public string UserName { get; set; }

        /// <summary>
        /// Customer ID if applicable
        /// </summary>
        public int? CustomerId { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        [StringLength(200)]
        public string CustomerName { get; set; }

        /// <summary>
        /// Total amount before tax
        /// </summary>
        [Required]
        public decimal SubTotal { get; set; }

        /// <summary>
        /// Tax amount
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Discount amount
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Total amount after tax and discount
        /// </summary>
        [Required]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Amount paid by customer
        /// </summary>
        [Required]
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// Change given to customer
        /// </summary>
        public decimal ChangeAmount { get; set; }

        /// <summary>
        /// Payment method used
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Transaction status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; }

        /// <summary>
        /// Notes or comments about the transaction
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Location where the transaction occurred
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// Location name
        /// </summary>
        [StringLength(100)]
        public string LocationName { get; set; }

        /// <summary>
        /// Whether this transaction was processed offline
        /// </summary>
        public bool IsOfflineTransaction { get; set; }

        /// <summary>
        /// Date when the transaction was synced (if offline)
        /// </summary>
        public DateTime? SyncedAt { get; set; }

        /// <summary>
        /// Receipt number
        /// </summary>
        [StringLength(50)]
        public string ReceiptNumber { get; set; }

        /// <summary>
        /// List of items sold in this transaction
        /// </summary>
        public virtual ICollection<SalesDetail> SalesDetails { get; set; } = new List<SalesDetail>();

        /// <summary>
        /// Date when the record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when the record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
