<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="InventoryManagement.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="HasShownWelcome" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="LastBackupDate" Type="System.DateTime" Scope="User">
      <Value Profile="(Default)">1900-01-01</Value>
    </Setting>
    <Setting Name="PreferredTheme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="AutoBackupEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowQuickTips" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
  </Settings>
</SettingsFile>
