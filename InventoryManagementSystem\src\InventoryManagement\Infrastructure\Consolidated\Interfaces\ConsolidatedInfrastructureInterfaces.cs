using InventoryManagement.Infrastructure.Consolidated.Models;
using InventoryManagement.Infrastructure.Network;
using InventoryManagement.Infrastructure.Scheduling;
using InventoryManagement.Infrastructure.Shortcuts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Consolidated.Interfaces
{
    /// <summary>
    /// Interfaces for consolidated infrastructure services
    /// </summary>

    #region Performance Monitoring

    public interface IPerformanceMonitor
    {
        Task RecordMetricsAsync(PerformanceMetrics metrics);
        Task<PerformanceStatistics> GetStatisticsAsync(string operationName, TimeSpan timeRange);
        Task<List<PerformanceMetrics>> GetRecentMetricsAsync(string operationName, int count = 100);
    }

    #endregion

    #region Health Checking

    public interface IHealthCheckService
    {
        Task<HealthCheckResult> CheckHealthAsync();
        Task<ComponentHealthResult> CheckComponentHealthAsync(string componentName);
        Task<Dictionary<string, ComponentHealthResult>> CheckAllComponentsAsync();
    }

    #endregion

    #region Network Services

    public interface INetworkStatusService
    {
        Task<bool> IsConnectedAsync();
        Task<NetworkStatus> GetNetworkStatusAsync();
        event EventHandler<NetworkStatusChangedEventArgs> NetworkStatusChanged;
    }

    public interface IOfflineQueueService
    {
        Task EnqueueAsync(OfflineOperation operation);
        Task<List<OfflineOperation>> GetPendingOperationsAsync();
        Task<OfflineOperation> DequeueAsync();
        Task MarkCompletedAsync(Guid operationId);
        Task MarkFailedAsync(Guid operationId, string errorMessage);
    }

    public interface IOfflineSyncService
    {
        Task<SyncResult> SynchronizeAsync();
        Task<SyncResult> FullSynchronizeAsync();
        Task<DateTime?> GetLastSyncTimeAsync();
    }

    public interface ILocalStorageService
    {
        Task InitializeAsync();
        Task<T> RetrieveAsync<T>(string key);
        Task StoreAsync<T>(string key, T data);
        Task RemoveAsync(string key);
        Task<long> GetStorageSizeAsync();
        Task OptimizeStorageAsync();
    }

    #endregion

    #region Task Scheduling

    public interface ITaskSchedulerService
    {
        Task ScheduleTaskAsync(ScheduledTask task);
        Task UnscheduleTaskAsync(string taskName);
        Task<List<ScheduledTask>> GetScheduledTasksAsync();
        Task StartSchedulerAsync();
        Task StopSchedulerAsync();
    }

    #endregion

    #region Shortcut Management

    public interface IShortcutManager
    {
        void RegisterShortcut(ShortcutDefinition shortcut, Action action);
        void UnregisterShortcut(ShortcutDefinition shortcut);
        List<ShortcutDefinition> GetRegisteredShortcuts();
        bool IsShortcutRegistered(string name);
    }

    #endregion

    #region Database Management

    public interface IDatabaseManager
    {
        Task<bool> CanConnectAsync();
        Task InitializeAsync();
        Task<bool> TestConnectionAsync();
        Task BackupDatabaseAsync(string backupPath);
        Task RestoreDatabaseAsync(string backupPath);
    }

    public interface IConnectionRetryPolicy
    {
        Task<T> ExecuteAsync<T>(Func<Task<T>> operation);
        Task ExecuteAsync(Func<Task> operation);
    }

    public interface IQueryOptimizationService
    {
        Func<Task<T>> OptimizeQuery<T>(Func<Task<T>> query);
        Task<string> AnalyzeQueryPerformanceAsync(string query);
    }

    #endregion

    #region Security Services

    public interface IDataEncryptionService
    {
        string Encrypt(string plainText);
        string Decrypt(string encryptedText);
        byte[] Encrypt(byte[] data);
        byte[] Decrypt(byte[] encryptedData);
    }

    public interface IPermissionManager
    {
        bool HasPermission(Models.User user, string permission);
        List<string> GetUserPermissions(Models.User user);
        Task<bool> ValidatePermissionAsync(int userId, string permission);
    }

    public interface IValidationManager
    {
        ValidationResult Validate<T>(T obj);
        Task<ValidationResult> ValidateAsync<T>(T obj);
        void RegisterValidator<T>(IValidator<T> validator);
    }

    public interface IValidator<T>
    {
        ValidationResult Validate(T obj);
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
    }

    public interface ILoginRateLimitService
    {
        Task<bool> IsLoginAllowedAsync(string username, string ipAddress);
        Task RecordFailedAttemptAsync(string username, string ipAddress);
        Task ResetAttemptsAsync(string username);
    }

    #endregion

    #region Configuration Services

    public interface IOfflineConfigurationService
    {
        Task<T> GetConfigurationAsync<T>(string key);
        Task SetConfigurationAsync<T>(string key, T value);
        Task<Dictionary<string, object>> GetAllConfigurationsAsync();
        Task ReloadConfigurationAsync();
    }

    #endregion
}
