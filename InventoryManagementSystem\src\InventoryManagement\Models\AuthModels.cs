using System;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Authentication result for basic auth operations
    /// </summary>
    public class AuthResult
    {
        public bool Success { get; set; }
        public User User { get; set; }
        public string ErrorMessage { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// Enhanced authentication result with additional security features
    /// </summary>
    public class AuthenticationResult
    {
        public bool IsSuccessful { get; set; }
        public User User { get; set; }
        public string ErrorMessage { get; set; }
        public bool RequiresTwoFactor { get; set; }
        public string SessionToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }
}
