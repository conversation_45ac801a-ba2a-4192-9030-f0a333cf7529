using InventoryManagement.DataAccess;
using InventoryManagement.Infrastructure.Caching;
using InventoryManagement.Infrastructure.Database;
using InventoryManagement.Infrastructure.Consolidated.Models;
using InventoryManagement.Infrastructure.Consolidated.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Consolidated.DataAccess
{
    /// <summary>
    /// Consolidated data access service that merges functionality from multiple data-related services
    /// while preserving all methods and features for the offline inventory management system.
    /// 
    /// This service consolidates:
    /// - Database management services
    /// - Caching services
    /// - Data persistence services
    /// - Connection management
    /// - Query optimization
    /// </summary>
    public class ConsolidatedDataService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ICacheService _cacheService;
        private readonly IDatabaseManager _databaseManager;
        private readonly IConnectionRetryPolicy _retryPolicy;
        private readonly IQueryOptimizationService _queryOptimization;
        private readonly ILogger<ConsolidatedDataService> _logger;

        public ConsolidatedDataService(
            ApplicationDbContext dbContext,
            ICacheService cacheService,
            IDatabaseManager databaseManager,
            IConnectionRetryPolicy retryPolicy,
            IQueryOptimizationService queryOptimization,
            ILogger<ConsolidatedDataService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _databaseManager = databaseManager ?? throw new ArgumentNullException(nameof(databaseManager));
            _retryPolicy = retryPolicy ?? throw new ArgumentNullException(nameof(retryPolicy));
            _queryOptimization = queryOptimization ?? throw new ArgumentNullException(nameof(queryOptimization));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Database Management

        /// <summary>
        /// Ensures the database is properly initialized and ready for use
        /// </summary>
        /// <returns>True if database is ready, false otherwise</returns>
        public async Task<bool> EnsureDatabaseReadyAsync()
        {
            try
            {
                _logger.LogInformation("Ensuring database is ready for offline operations");

                // Check database connection
                var canConnect = await _databaseManager.CanConnectAsync();
                if (!canConnect)
                {
                    _logger.LogWarning("Database connection failed, attempting to initialize");
                    await _databaseManager.InitializeAsync();
                }

                // Ensure database is created and migrated
                await _dbContext.Database.EnsureCreatedAsync();
                
                _logger.LogInformation("Database is ready for offline operations");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ensure database is ready");
                return false;
            }
        }

        /// <summary>
        /// Performs database health check
        /// </summary>
        /// <returns>Database health status</returns>
        public async Task<DatabaseHealthStatus> CheckDatabaseHealthAsync()
        {
            try
            {
                var canConnect = await _databaseManager.CanConnectAsync();
                if (!canConnect)
                {
                    return new DatabaseHealthStatus
                    {
                        IsHealthy = false,
                        Message = "Cannot connect to database",
                        LastChecked = DateTime.Now
                    };
                }

                // Check if we can perform basic operations
                var itemCount = await _dbContext.Items.CountAsync();
                
                return new DatabaseHealthStatus
                {
                    IsHealthy = true,
                    Message = $"Database is healthy. {itemCount} items in inventory.",
                    LastChecked = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database health check failed");
                return new DatabaseHealthStatus
                {
                    IsHealthy = false,
                    Message = $"Database health check failed: {ex.Message}",
                    LastChecked = DateTime.Now
                };
            }
        }

        #endregion

        #region Caching Operations

        /// <summary>
        /// Gets data from cache or database with automatic caching
        /// </summary>
        /// <typeparam name="T">Type of data to retrieve</typeparam>
        /// <param name="cacheKey">Cache key</param>
        /// <param name="dataFactory">Function to retrieve data if not in cache</param>
        /// <param name="cacheExpiry">Cache expiration time</param>
        /// <returns>Cached or fresh data</returns>
        public async Task<T> GetCachedDataAsync<T>(string cacheKey, Func<Task<T>> dataFactory, TimeSpan? cacheExpiry = null)
        {
            try
            {
                // Try to get from cache first
                var cachedData = await _cacheService.GetAsync<T>(cacheKey);
                if (cachedData != null)
                {
                    _logger.LogDebug("Data retrieved from cache: {CacheKey}", cacheKey);
                    return cachedData;
                }

                // Get fresh data
                var freshData = await dataFactory();
                
                // Cache the fresh data
                var expiry = cacheExpiry ?? TimeSpan.FromMinutes(30);
                await _cacheService.SetAsync(cacheKey, freshData, expiry);
                
                _logger.LogDebug("Data cached with key: {CacheKey}", cacheKey);
                return freshData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in cached data retrieval for key: {CacheKey}", cacheKey);
                throw;
            }
        }

        /// <summary>
        /// Invalidates cache for a specific key pattern
        /// </summary>
        /// <param name="keyPattern">Cache key pattern to invalidate</param>
        /// <returns>Task representing the async operation</returns>
        public async Task InvalidateCacheAsync(string keyPattern)
        {
            try
            {
                await _cacheService.RemoveByPatternAsync(keyPattern);
                _logger.LogDebug("Cache invalidated for pattern: {KeyPattern}", keyPattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating cache for pattern: {KeyPattern}", keyPattern);
                throw;
            }
        }

        #endregion

        #region Query Optimization

        /// <summary>
        /// Executes an optimized query with retry policy
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="queryFunc">Query function to execute</param>
        /// <returns>Query result</returns>
        public async Task<T> ExecuteOptimizedQueryAsync<T>(Func<Task<T>> queryFunc)
        {
            try
            {
                return await _retryPolicy.ExecuteAsync(async () =>
                {
                    var optimizedQuery = _queryOptimization.OptimizeQuery(queryFunc);
                    return await optimizedQuery();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing optimized query");
                throw;
            }
        }

        /// <summary>
        /// Executes a bulk operation with optimization
        /// </summary>
        /// <param name="operation">Bulk operation to execute</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ExecuteBulkOperationAsync(Func<Task> operation)
        {
            try
            {
                _logger.LogInformation("Executing bulk operation");
                
                // Disable change tracking for better performance
                _dbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                
                await operation();
                
                // Save changes in batch
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("Bulk operation completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing bulk operation");
                throw;
            }
            finally
            {
                // Re-enable change tracking
                _dbContext.ChangeTracker.AutoDetectChangesEnabled = true;
            }
        }

        #endregion

        #region Data Integrity

        /// <summary>
        /// Validates data integrity across the database
        /// </summary>
        /// <returns>Data integrity report</returns>
        public async Task<DataIntegrityReport> ValidateDataIntegrityAsync()
        {
            try
            {
                _logger.LogInformation("Starting data integrity validation");

                var report = new DataIntegrityReport
                {
                    ValidationDate = DateTime.Now,
                    Issues = new List<string>()
                };

                // Check for orphaned records
                var orphanedItems = await _dbContext.Items
                    .Where(i => !_dbContext.Categories.Any(c => c.Id == i.CategoryId))
                    .CountAsync();

                if (orphanedItems > 0)
                {
                    report.Issues.Add($"Found {orphanedItems} items with invalid category references");
                }

                // Check for negative stock quantities
                var negativeStock = await _dbContext.Items
                    .Where(i => i.StockByLocation.Any(s => s.Quantity < 0))
                    .CountAsync();

                if (negativeStock > 0)
                {
                    report.Issues.Add($"Found {negativeStock} items with negative stock quantities");
                }

                report.IsValid = report.Issues.Count == 0;
                
                _logger.LogInformation("Data integrity validation completed. Issues found: {IssueCount}", report.Issues.Count);
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data integrity validation");
                throw;
            }
        }

        #endregion
    }

    /// <summary>
    /// Database health status information
    /// </summary>
    public class DatabaseHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Message { get; set; }
        public DateTime LastChecked { get; set; }
    }

    /// <summary>
    /// Data integrity validation report
    /// </summary>
    public class DataIntegrityReport
    {
        public bool IsValid { get; set; }
        public DateTime ValidationDate { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
    }
}
