using InventoryManagement.Models.Domain;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models.Domain
{
    /// <summary>
    /// Consolidated sales domain models for the offline inventory management system.
    /// This file consolidates all sales-related entities including transactions, payments, and customers.
    /// </summary>

    #region Transaction Models

    /// <summary>
    /// Core transaction model representing all types of inventory transactions
    /// </summary>
    public class SalesTransaction
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string TransactionNumber { get; set; }

        [Required]
        public TransactionType TransactionType { get; set; }

        [Required]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        public int UserId { get; set; }

        public int? CustomerId { get; set; }

        public int LocationId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public TransactionStatus Status { get; set; } = TransactionStatus.Completed;

        [StringLength(500)]
        public string Notes { get; set; }

        public bool IsOfflineTransaction { get; set; } = true;

        public DateTime? SyncedAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual CoreUser User { get; set; }

        [ForeignKey("CustomerId")]
        public virtual SalesCustomer Customer { get; set; }

        [ForeignKey("LocationId")]
        public virtual CoreLocation Location { get; set; }

        public virtual ICollection<SalesTransactionDetail> TransactionDetails { get; set; } = new List<SalesTransactionDetail>();
        public virtual ICollection<SalesPayment> Payments { get; set; } = new List<SalesPayment>();
    }

    /// <summary>
    /// Transaction detail representing individual items in a transaction
    /// </summary>
    public class SalesTransactionDetail
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int TransactionId { get; set; }

        [Required]
        public int ItemId { get; set; }

        [Required]
        public int Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [StringLength(200)]
        public string Notes { get; set; }

        // Navigation properties
        [ForeignKey("TransactionId")]
        public virtual SalesTransaction Transaction { get; set; }

        [ForeignKey("ItemId")]
        public virtual CoreItem Item { get; set; }
    }

    /// <summary>
    /// Transaction types in the system
    /// </summary>
    public enum TransactionType
    {
        Sale = 1,
        Return = 2,
        Exchange = 3,
        Transfer = 4,
        Adjustment = 5,
        Damage = 6
    }

    /// <summary>
    /// Transaction status
    /// </summary>
    public enum TransactionStatus
    {
        Pending = 1,
        Completed = 2,
        Cancelled = 3,
        Refunded = 4
    }

    #endregion

    #region Payment Models

    /// <summary>
    /// Payment information for transactions
    /// </summary>
    public class SalesPayment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int TransactionId { get; set; }

        [Required]
        public PaymentMethod PaymentMethod { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [StringLength(100)]
        public string Reference { get; set; }

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        public PaymentStatus Status { get; set; } = PaymentStatus.Completed;

        [StringLength(500)]
        public string Notes { get; set; }

        // Navigation properties
        [ForeignKey("TransactionId")]
        public virtual SalesTransaction Transaction { get; set; }
    }

    /// <summary>
    /// Payment methods supported by the system
    /// </summary>
    public enum PaymentMethod
    {
        Cash = 1,
        CreditCard = 2,
        DebitCard = 3,
        Check = 4,
        BankTransfer = 5,
        Credit = 6
    }

    /// <summary>
    /// Payment status
    /// </summary>
    public enum PaymentStatus
    {
        Pending = 1,
        Completed = 2,
        Failed = 3,
        Refunded = 4
    }

    #endregion

    #region Customer Models

    /// <summary>
    /// Customer information for sales tracking
    /// </summary>
    public class SalesCustomer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string CustomerNumber { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(20)]
        public string Phone { get; set; }

        [StringLength(500)]
        public string Address { get; set; }

        public CustomerType Type { get; set; } = CustomerType.Regular;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastPurchaseDate { get; set; }

        // Navigation properties
        public virtual ICollection<SalesTransaction> Transactions { get; set; } = new List<SalesTransaction>();
        public virtual ICollection<SalesCreditTransaction> CreditTransactions { get; set; } = new List<SalesCreditTransaction>();
    }

    /// <summary>
    /// Customer types for different pricing and credit policies
    /// </summary>
    public enum CustomerType
    {
        Regular = 1,
        VIP = 2,
        Wholesale = 3,
        Employee = 4
    }

    /// <summary>
    /// Credit transactions for customers with credit accounts
    /// </summary>
    public class SalesCreditTransaction
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int CustomerId { get; set; }

        public int? TransactionId { get; set; }

        [Required]
        public CreditTransactionType Type { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        public int CreatedBy { get; set; }

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual SalesCustomer Customer { get; set; }

        [ForeignKey("TransactionId")]
        public virtual SalesTransaction Transaction { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual CoreUser CreatedByUser { get; set; }
    }

    /// <summary>
    /// Credit transaction types
    /// </summary>
    public enum CreditTransactionType
    {
        Sale = 1,
        Payment = 2,
        Adjustment = 3,
        Refund = 4
    }

    #endregion

    #region Sales Analytics Models

    /// <summary>
    /// Sales metrics for dashboard and reporting
    /// </summary>
    public class SalesMetrics
    {
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int TotalItemsSold { get; set; }
        public decimal TotalDiscountGiven { get; set; }
        public TimeSpan TimePeriod { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        public List<TopSellingItem> TopSellingItems { get; set; } = new List<TopSellingItem>();
        public Dictionary<string, decimal> SalesByPaymentMethod { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, int> SalesByCategory { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// Top selling item information
    /// </summary>
    public class TopSellingItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; }
        public string SKU { get; set; }
        public int QuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AveragePrice { get; set; }
        public string Category { get; set; }
    }

    /// <summary>
    /// Sales summary for reporting
    /// </summary>
    public class SalesSummary
    {
        public DateTime Date { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal CashSales { get; set; }
        public decimal CardSales { get; set; }
        public decimal CreditSales { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalRefunds { get; set; }
        public int ItemsSold { get; set; }
    }

    #endregion

    #region Return and Exchange Models

    /// <summary>
    /// Return transaction for handling product returns
    /// </summary>
    public class SalesReturn
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string ReturnNumber { get; set; }

        [Required]
        public int OriginalTransactionId { get; set; }

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public int ProcessedBy { get; set; }

        [Required]
        public ReturnReason Reason { get; set; }

        [StringLength(500)]
        public string ReasonDescription { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RefundAmount { get; set; }

        public RefundMethod RefundMethod { get; set; }

        public DateTime ReturnDate { get; set; } = DateTime.Now;

        public ReturnStatus Status { get; set; } = ReturnStatus.Pending;

        // Navigation properties
        [ForeignKey("OriginalTransactionId")]
        public virtual SalesTransaction OriginalTransaction { get; set; }

        [ForeignKey("CustomerId")]
        public virtual SalesCustomer Customer { get; set; }

        [ForeignKey("ProcessedBy")]
        public virtual CoreUser ProcessedByUser { get; set; }

        public virtual ICollection<SalesReturnDetail> ReturnDetails { get; set; } = new List<SalesReturnDetail>();
    }

    /// <summary>
    /// Return detail for individual items being returned
    /// </summary>
    public class SalesReturnDetail
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ReturnId { get; set; }

        [Required]
        public int ItemId { get; set; }

        [Required]
        public int Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public ItemCondition Condition { get; set; } = ItemCondition.Good;

        [StringLength(200)]
        public string Notes { get; set; }

        // Navigation properties
        [ForeignKey("ReturnId")]
        public virtual SalesReturn Return { get; set; }

        [ForeignKey("ItemId")]
        public virtual CoreItem Item { get; set; }
    }

    /// <summary>
    /// Reasons for product returns
    /// </summary>
    public enum ReturnReason
    {
        Defective = 1,
        WrongItem = 2,
        CustomerChanged = 3,
        Damaged = 4,
        Expired = 5,
        Other = 99
    }

    /// <summary>
    /// Refund methods
    /// </summary>
    public enum RefundMethod
    {
        Cash = 1,
        CreditCard = 2,
        StoreCredit = 3,
        Exchange = 4
    }

    /// <summary>
    /// Return status
    /// </summary>
    public enum ReturnStatus
    {
        Pending = 1,
        Approved = 2,
        Rejected = 3,
        Completed = 4
    }

    /// <summary>
    /// Item condition for returns
    /// </summary>
    public enum ItemCondition
    {
        Good = 1,
        Damaged = 2,
        Defective = 3,
        Expired = 4
    }

    #endregion
}
