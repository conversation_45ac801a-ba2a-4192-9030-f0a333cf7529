using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Enhanced exception handler for better error management
    /// </summary>
    public class EnhancedExceptionHandler
    {
        private readonly ILogger<EnhancedExceptionHandler> _logger;

        public EnhancedExceptionHandler(ILogger<EnhancedExceptionHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles an exception and logs it appropriately
        /// </summary>
        /// <param name="exception">Exception to handle</param>
        /// <param name="context">Context information</param>
        /// <returns>Task</returns>
        public async Task HandleExceptionAsync(Exception exception, string context = null)
        {
            try
            {
                var contextInfo = string.IsNullOrEmpty(context) ? "" : $" in context: {context}";
                _logger.LogError(exception, "Exception occurred{Context}", contextInfo);

                // Additional handling logic can be added here
                // For example: sending notifications, creating error reports, etc.

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                // Fallback logging in case the main logging fails
                Console.WriteLine($"Critical error in exception handler: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles an exception synchronously
        /// </summary>
        /// <param name="exception">Exception to handle</param>
        /// <param name="context">Context information</param>
        public void HandleException(Exception exception, string context = null)
        {
            try
            {
                var contextInfo = string.IsNullOrEmpty(context) ? "" : $" in context: {context}";
                _logger.LogError(exception, "Exception occurred{Context}", contextInfo);
            }
            catch (Exception ex)
            {
                // Fallback logging in case the main logging fails
                Console.WriteLine($"Critical error in exception handler: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes an action with exception handling
        /// </summary>
        /// <param name="action">Action to execute</param>
        /// <param name="context">Context information</param>
        /// <returns>True if successful, false if exception occurred</returns>
        public bool TryExecute(Action action, string context = null)
        {
            try
            {
                action?.Invoke();
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex, context);
                return false;
            }
        }

        /// <summary>
        /// Executes an async action with exception handling
        /// </summary>
        /// <param name="action">Async action to execute</param>
        /// <param name="context">Context information</param>
        /// <returns>True if successful, false if exception occurred</returns>
        public async Task<bool> TryExecuteAsync(Func<Task> action, string context = null)
        {
            try
            {
                if (action != null)
                {
                    await action();
                }
                return true;
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context);
                return false;
            }
        }

        /// <summary>
        /// Executes a function with exception handling
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="function">Function to execute</param>
        /// <param name="defaultValue">Default value to return on exception</param>
        /// <param name="context">Context information</param>
        /// <returns>Function result or default value</returns>
        public T TryExecute<T>(Func<T> function, T defaultValue = default(T), string context = null)
        {
            try
            {
                return function != null ? function() : defaultValue;
            }
            catch (Exception ex)
            {
                HandleException(ex, context);
                return defaultValue;
            }
        }

        /// <summary>
        /// Executes an async function with exception handling
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="function">Async function to execute</param>
        /// <param name="defaultValue">Default value to return on exception</param>
        /// <param name="context">Context information</param>
        /// <returns>Function result or default value</returns>
        public async Task<T> TryExecuteAsync<T>(Func<Task<T>> function, T defaultValue = default(T), string context = null)
        {
            try
            {
                return function != null ? await function() : defaultValue;
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context);
                return defaultValue;
            }
        }
    }
}
