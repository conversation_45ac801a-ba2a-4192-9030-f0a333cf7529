using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using InventoryManagement.Commands;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// Base class for all ViewModels providing common functionality
    /// </summary>
    public abstract class ViewModelBase : INotifyPropertyChanged, IDisposable
    {
        private readonly Dictionary<string, object> _propertyValues = new Dictionary<string, object>();
        private bool _isBusy;
        private string _busyMessage;
        private bool _disposed;

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Gets or sets whether the ViewModel is currently busy
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Gets or sets the busy message displayed when IsBusy is true
        /// </summary>
        public string BusyMessage
        {
            get => _busyMessage;
            set => SetProperty(ref _busyMessage, value);
        }

        /// <summary>
        /// Sets a property value and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">The name of the property (automatically provided)</param>
        /// <returns>True if the property value changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Sets a property value using a dictionary and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">The name of the property (automatically provided)</param>
        /// <returns>True if the property value changed, false otherwise</returns>
        protected bool SetProperty<T>(T value, [CallerMemberName] string propertyName = null)
        {
            if (_propertyValues.TryGetValue(propertyName, out var currentValue) && 
                EqualityComparer<T>.Default.Equals((T)currentValue, value))
                return false;

            _propertyValues[propertyName] = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Gets a property value from the dictionary
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="defaultValue">The default value if the property hasn't been set</param>
        /// <param name="propertyName">The name of the property (automatically provided)</param>
        /// <returns>The property value</returns>
        protected T GetProperty<T>(T defaultValue = default(T), [CallerMemberName] string propertyName = null)
        {
            if (_propertyValues.TryGetValue(propertyName, out var value))
                return (T)value;
            return defaultValue;
        }

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Executes an async operation while setting IsBusy to true
        /// </summary>
        /// <param name="operation">The async operation to execute</param>
        /// <param name="busyMessage">Optional message to display while busy</param>
        protected async Task ExecuteAsync(Func<Task> operation, string busyMessage = "Loading...")
        {
            if (IsBusy) return;

            try
            {
                IsBusy = true;
                BusyMessage = busyMessage;
                await operation();
            }
            finally
            {
                IsBusy = false;
                BusyMessage = null;
            }
        }

        /// <summary>
        /// Executes an async operation with a return value while setting IsBusy to true
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="operation">The async operation to execute</param>
        /// <param name="busyMessage">Optional message to display while busy</param>
        /// <returns>The result of the operation</returns>
        protected async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string busyMessage = "Loading...")
        {
            if (IsBusy) return default(T);

            try
            {
                IsBusy = true;
                BusyMessage = busyMessage;
                return await operation();
            }
            finally
            {
                IsBusy = false;
                BusyMessage = null;
            }
        }

        /// <summary>
        /// Creates a command that can be executed
        /// </summary>
        /// <param name="execute">The action to execute</param>
        /// <param name="canExecute">Optional function to determine if the command can execute</param>
        /// <returns>A new RelayCommand</returns>
        protected ICommand CreateCommand(Action execute, Func<bool> canExecute = null)
        {
            return new RelayCommand(execute, canExecute);
        }

        /// <summary>
        /// Creates a command with a parameter that can be executed
        /// </summary>
        /// <param name="execute">The action to execute</param>
        /// <param name="canExecute">Optional function to determine if the command can execute</param>
        /// <returns>A new RelayCommand</returns>
        protected ICommand CreateCommand<T>(Action<T> execute, Func<T, bool> canExecute = null)
        {
            return new RelayCommand<T>(execute, canExecute);
        }

        /// <summary>
        /// Creates an async command that can be executed
        /// </summary>
        /// <param name="execute">The async action to execute</param>
        /// <param name="canExecute">Optional function to determine if the command can execute</param>
        /// <returns>A new AsyncRelayCommand</returns>
        protected ICommand CreateAsyncCommand(Func<Task> execute, Func<bool> canExecute = null)
        {
            return new AsyncRelayCommand(execute, canExecute);
        }

        /// <summary>
        /// Disposes of the ViewModel resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes of the ViewModel resources
        /// </summary>
        /// <param name="disposing">True if disposing, false if finalizing</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _propertyValues.Clear();
                _disposed = true;
            }
        }
    }
}
