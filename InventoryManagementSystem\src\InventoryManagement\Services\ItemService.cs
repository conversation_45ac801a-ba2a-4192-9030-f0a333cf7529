using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service implementation for item management operations
    /// </summary>
    public class ItemService : IItemService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ItemService> _logger;

        public ItemService(IUnitOfWork unitOfWork, ILogger<ItemService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets all items
        /// </summary>
        public async Task<IEnumerable<Item>> GetAllItemsAsync()
        {
            try
            {
                return await _unitOfWork.Items.GetAll().ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all items");
                throw;
            }
        }

        /// <summary>
        /// Gets an item by ID
        /// </summary>
        public async Task<Item> GetItemByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.Items.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item by ID: {ItemId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets items by category
        /// </summary>
        public async Task<IEnumerable<Item>> GetItemsByCategoryAsync(int categoryId)
        {
            try
            {
                return await _unitOfWork.Items
                    .Find(i => i.CategoryId == categoryId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items by category: {CategoryId}", categoryId);
                throw;
            }
        }

        /// <summary>
        /// Gets items by supplier
        /// </summary>
        public async Task<IEnumerable<Item>> GetItemsBySupplierAsync(int supplierId)
        {
            try
            {
                return await _unitOfWork.Items
                    .Find(i => i.SupplierId == supplierId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items by supplier: {SupplierId}", supplierId);
                throw;
            }
        }

        /// <summary>
        /// Creates a new item
        /// </summary>
        public async Task<Item> CreateItemAsync(Item item)
        {
            try
            {
                if (item == null)
                    throw new ArgumentNullException(nameof(item));

                // Validate item data
                await ValidateItemAsync(item);

                // Set creation date
                item.CreatedDate = DateTime.Now;

                // Add the item
                await _unitOfWork.Items.AddAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Created new item: {ItemName} with ID: {ItemId}", item.Name, item.Id);
                return item;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating item: {ItemName}", item?.Name);
                throw;
            }
        }

        /// <summary>
        /// Updates an existing item
        /// </summary>
        public async Task<bool> UpdateItemAsync(Item item)
        {
            try
            {
                if (item == null)
                    throw new ArgumentNullException(nameof(item));

                // Validate item data
                await ValidateItemAsync(item, item.Id);

                // Set modification date
                item.LastModifiedDate = DateTime.Now;

                // Update the item
                await _unitOfWork.Items.UpdateAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Updated item: {ItemName} with ID: {ItemId}", item.Name, item.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item: {ItemName} with ID: {ItemId}", item?.Name, item?.Id);
                return false;
            }
        }

        /// <summary>
        /// Deletes an item (soft delete)
        /// </summary>
        public async Task<bool> DeleteItemAsync(int id)
        {
            try
            {
                var item = await _unitOfWork.Items.GetByIdAsync(id);
                if (item == null)
                {
                    _logger.LogWarning("Attempted to delete non-existent item with ID: {ItemId}", id);
                    return false;
                }

                // Soft delete
                item.IsDeleted = true;
                item.DeletedDate = DateTime.Now;

                await _unitOfWork.Items.UpdateAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Deleted item: {ItemName} with ID: {ItemId}", item.Name, item.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item with ID: {ItemId}", id);
                return false;
            }
        }

        /// <summary>
        /// Gets items by SKU
        /// </summary>
        public async Task<Item> GetItemBySkuAsync(string sku)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sku))
                    return null;

                return await _unitOfWork.Items.GetBySkuAsync(sku);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item by SKU: {SKU}", sku);
                throw;
            }
        }

        /// <summary>
        /// Gets items with their stock information
        /// </summary>
        public async Task<IEnumerable<Item>> GetItemsWithStockAsync()
        {
            try
            {
                return await _unitOfWork.Items.GetItemsWithStockAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items with stock");
                throw;
            }
        }

        /// <summary>
        /// Searches items by name or SKU
        /// </summary>
        public async Task<IEnumerable<Item>> SearchItemsAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllItemsAsync();

                var term = searchTerm.ToLower();
                return await _unitOfWork.Items
                    .Find(i => i.Name.ToLower().Contains(term) || 
                              i.SKU.ToLower().Contains(term) ||
                              i.Description.ToLower().Contains(term))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching items with term: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <summary>
        /// Gets low stock items
        /// </summary>
        public async Task<IEnumerable<Item>> GetLowStockItemsAsync()
        {
            try
            {
                return await _unitOfWork.Items
                    .Find(i => i.StockByLocation.Sum(s => s.Quantity) <= i.MinimumStockLevel && 
                              i.MinimumStockLevel > 0)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                throw;
            }
        }

        /// <summary>
        /// Validates item data
        /// </summary>
        private async Task ValidateItemAsync(Item item, int? excludeId = null)
        {
            // Check for duplicate SKU
            var existingItem = await _unitOfWork.Items
                .Find(i => i.SKU == item.SKU && (!excludeId.HasValue || i.Id != excludeId.Value))
                .FirstOrDefaultAsync();

            if (existingItem != null)
            {
                throw new InvalidOperationException($"An item with SKU '{item.SKU}' already exists.");
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(item.Name))
                throw new ArgumentException("Item name is required.");

            if (string.IsNullOrWhiteSpace(item.SKU))
                throw new ArgumentException("Item SKU is required.");

            if (item.SalePrice < 0)
                throw new ArgumentException("Sale price cannot be negative.");

            if (item.CostPrice < 0)
                throw new ArgumentException("Cost price cannot be negative.");
        }
    }
}
